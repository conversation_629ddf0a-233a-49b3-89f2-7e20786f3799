<?php

namespace App\Modules\InstansiInduk\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\InstansiInduk\Repositories\InstansiIndukRepository;
use App\Modules\InstansiInduk\Models\InstansiInduk;
use App\Modules\InstansiInduk\Requests\InstansiIndukCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class InstansiIndukController extends Controller
{
    public function index(Request $request)
    {
        $data = DB::table('a_instansi_induk as a')
            ->leftJoin(
                'a_instansi_induk_atasan as b', 
                'a.id_atasan', '=', 'b.id'
            )
            ->select(
                'a.id',
                'a.id_instansi_induk',
                'a.id_bkn',
                'a.instansi_induk',
                'a.jenis',
                'a.cepat_kode',
                'a.jenis_instansi',
                'a.id_atasan',
                'b.atasan'
            )
            ->get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = InstansiIndukRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('InstansiInduk::create');
    }

    public function store(InstansiIndukCreateRequest $request)
    {
        $payload = $request->all();
        $instansi_induk = InstansiIndukRepository::create($payload);
        return JsonResponseHandler::setResult($instansi_induk)->send();
    }

    public function show(Request $request, $id)
    {
        $instansi_induk = InstansiIndukRepository::get($id);
        return JsonResponseHandler::setResult($instansi_induk)->send();
    }

    public function edit($id)
    {
        return view('InstansiInduk::edit', ['id_instansi_induk' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $instansi_induk = InstansiIndukRepository::update($id, $payload);
        return JsonResponseHandler::setResult($instansi_induk)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = InstansiIndukRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }

    public function detail(Request $request, $instansi_induk_id)
    {
        $instansi_induk_id = InstansiInduk::where('id', $instansi_induk_id)->first();
        return JsonResponseHandler::setResult($instansi_induk_id)->send();
    }
}
