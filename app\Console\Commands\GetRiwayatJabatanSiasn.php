<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Modules\Skpd\Models\Skpd;

class GetRiwayatJabatanSiasn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get-riwayat-jabatan-siasn';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $skpd = Skpd::where('id_bkn', '!=', '')->limit(100)->get()->toArray();
        dd($skpd);
    }
}
