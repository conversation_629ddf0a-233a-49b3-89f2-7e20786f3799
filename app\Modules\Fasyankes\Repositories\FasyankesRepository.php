<?php

namespace App\Modules\Fasyankes\Repositories;

use App\Modules\Fasyankes\Models\Fasyankes;

class FasyankesRepository
{
    public static function datatable($per_page = 15)
    {
        $data = Fasyankes::paginate($per_page);
        return $data;
    }
    public static function get($fasyankes_id)
    {
        $fasyankes = Fasyankes::where('id', $fasyankes_id)->first();
        return $fasyankes;
    }
    public static function create($fasyankes)
    {
        $fasyankes = Fasyankes::create($fasyankes);
        return $fasyankes;
    }

    public static function update($fasyankes_id, $fasyankes)
    {
        Fasyankes::where('id', $fasyankes_id)->update($fasyankes);
        $fasyankes = Fasyankes::where('id', $fasyankes_id)->first();
        return $fasyankes;
    }

    public static function delete($fasyankes_id)
    {
        $delete = Fasyankes::where('id', $fasyankes_id)->delete();
        return $delete;
    }
}
