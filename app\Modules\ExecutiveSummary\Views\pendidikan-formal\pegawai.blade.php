<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee List</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
        }

        th {
            background-color: #f2f2f2;
        }

        .table-header {
            text-align: center;
            font-weight: bold;
            padding: 10px;
        }
    </style>
</head>

<body>
    <div class="table-header">
        DAFTAR PEGAWAI<br>
    </div>
    <table>
        <tr>
            <th rowspan="2">NO.</th>
            <th rowspan="2">NAMA<br>TEMPAT, TANGGAL LAHIR</th>
            <th rowspan="2">NIP<br>KARPEG</th>
            <th rowspan="2">GOL.<br>TMT</th>
            <th rowspan="2">JABATAN<br>UNIT KERJA<br>TMT</th>
            <th colspan="2">MASA KERJA</th>
            <th rowspan="2">PENDIDIKAN TERAKHIR</th>
        </tr>
        <tr> 
            <th>THN</th>
            <th>BLN</th>
        </tr>
        @foreach($employee as $key => $peg)
            <tr>
                <td>{{ $key + 1 }}</td>
                <td>
                    <b>{{ $peg->nama }}</b><br>
                    {{ $peg->tempat_lahir }},&nbsp;
                    {{ 
                        $peg->tanggal_lahir
                            ? \Carbon\Carbon::parse($peg->tanggal_lahir)->isoFormat('D MMMM YYYY', 'Do MMMM YYYY') 
                            : '-'
                    }}
                </td>
                <td>
                    <b>{{ $peg->nip }}</b><br>
                    {{ $peg->nomor_kartu_asn ?? '-' }}
                </td>
                <td>
                    <b>{{ @$peg->riwayatPangkat[0]->golru->golongan ?? '-' }}</b><br>
                    {{ 
                        @$peg->riwayatPangkat[0]->tmt_sk
                            ? \Carbon\Carbon::parse($peg->riwayatPangkat[0]->tmt_sk)->isoFormat('D MMMM YYYY', 'Do MMMM YYYY') 
                            : '-'
                    }}
                </td>
                <td>
                    <b>{{ @$peg->riwayatJabatan[0]->jabatan ?? '-' }}</b><br>
                    <b></b>{{ $peg->skpd ?? '-' }}<br>
                    {{ 
                        @$peg->riwayatJabatan[0]->tmt_jabatan
                            ? \Carbon\Carbon::parse($peg->riwayatJabatan[0]->tmt_jabatan)->isoFormat('D MMMM YYYY', 'Do MMMM YYYY') 
                            : '-'
                    }}
                </td>
                <td>{{ @$peg->riwayatPangkat[0]->masa_kerja_tahun ?? '-' }}</td>
                <td>{{ @$peg->riwayatPangkat[0]->masa_kerja_bulan ?? '-' }}</td>
                <td>{{ @$peg->riwayatPendidikan[0]->tkpendid->pend ?? '-' }}</td>
                {{-- {{dd($peg->riwayatPendidikan[0]->tkpendid->pend)}} --}}
                {{-- {{dd($peg)}} --}}
            </tr>
            @endforeach
    </table>
</body>

</html>
