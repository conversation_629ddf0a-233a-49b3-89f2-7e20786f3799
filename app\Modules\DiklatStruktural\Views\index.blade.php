@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="diklat-struktural">
    <default-datatable title="DiklatStruktural" url="{!! url('diklat-struktural') !!}" :headers="headers" :can-add="{{ $permissions['create-diklat_struktural'] }}" :can-edit="{{ $permissions['update-diklat_struktural'] }}" :can-delete="{{ $permissions['delete-diklat_struktural'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'ket',
        						text: 'ket'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#diklat-struktural');
</script>
@endsection