@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jenis-diklat">
    <default-datatable title="JenisDiklat" url="{!! url('jenis-diklat') !!}" :headers="headers" :can-add="{{ $permissions['create-jenis_diklat'] }}" :can-edit="{{ $permissions['update-jenis_diklat'] }}" :can-delete="{{ $permissions['delete-jenis_diklat'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'ket',
        						text: 'ket'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jenis-diklat');
</script>
@endsection