<?php

namespace App\Modules\JenBahasa\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenBahasa\Models\JenBahasaModel;
use App\Modules\JenBahasa\Repositories\JenBahasaRepository;
// use App\Modules\JenBahasa\Requests\JenBahasaCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenBahasaController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenBahasaRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function index(Request $request)
    {
        $permissions = JenBahasaModel::get();
        return JsonResponseHandler::setResult($permissions)->send();

    }
}
