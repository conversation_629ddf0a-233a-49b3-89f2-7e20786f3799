<?php

namespace App\Modules\JenisDiklat\Repositories;

use App\Modules\JenisDiklat\Models\JenisDiklat;
use Illuminate\Support\Facades\Auth;

class JenisDiklatRepository
{
    public static function datatable($per_page = 15)
    {
        $data = JenisDiklat::paginate($per_page);
        return $data;
    }
    public static function get($jenis_diklat_id)
    {
        $jenis_diklat = JenisDiklat::where('id', $jenis_diklat_id)->first();
        return $jenis_diklat;
    }

    public static function byMetodeDiklat($jenis_diklat_id)
    {   
        if($jenis_diklat_id!=3):
            $jenis_diklat = JenisDiklat::where('kategori', $jenis_diklat_id)->get();
        else:
            $jenis_diklat = JenisDiklat::get();
        endif;
        return $jenis_diklat;
    }
    public static function create($jenis_diklat)
    {
        $jenis_diklat['user_id'] = Auth::user()->id;
        $jenis_diklat['role_id'] = Auth::user()['roles']->toArray()[0]['id'];
        $jenis_diklat = JenisDiklat::create($jenis_diklat);
        return $jenis_diklat;
    }

    public static function update($jenis_diklat_id, $jenis_diklat)
    {
        $jenis_diklat['user_id'] = Auth::user()->id;
        $jenis_diklat['role_id'] = Auth::user()['roles']->toArray()[0]['id'];
        JenisDiklat::where('id', $jenis_diklat_id)->update($jenis_diklat);
        $jenis_diklat = JenisDiklat::where('id', $jenis_diklat_id)->first();
        return $jenis_diklat;
    }

    public static function delete($jenis_diklat_id)
    {
        $delete = JenisDiklat::where('id', $jenis_diklat_id)->delete();
        return $delete;
    }
}
