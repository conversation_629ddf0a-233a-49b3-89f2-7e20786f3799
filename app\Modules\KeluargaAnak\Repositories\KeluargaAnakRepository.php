<?php

namespace App\Modules\KeluargaAnak\Repositories;

use App\Exceptions\AppException;
use App\Models\User;
use App\Modules\KeluargaAnak\Models\KeluargaAnakModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiModel;
use App\Modules\KeluargaAnak\Models\KeluargaAnakTempModel;
use App\Modules\User\Model\UserModel;
use App\Modules\Employee\Model\EmployeeModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Handler\FileHandler;

class KeluargaAnakRepository
{
    public static function employeeDatatable($employeeId, $per_page = 15)
    {
        //kondisi jika punya pasangan sebagai asn
        $cekpasangan = KeluargaIstriSuamiModel::where('id_pegawai', $employeeId)
            ->join('tb_01', 'r_pasangan.nik', '=', 'tb_01.nik')
            ->select('r_pasangan.nik', 'tb_01.id as id_pegawai')
            ->where('r_pasangan.status_asn', 1)
            ->orderBy('r_pasangan.nikah_tanggal', 'desc')
            ->first();

        if ($cekpasangan) {
            //cek jika pasangan saling aprove
            $nik_issu = KeluargaIstriSuamiModel::where('id_pegawai', $cekpasangan->id_pegawai)
                        ->orderBy('r_pasangan.nikah_tanggal', 'desc')
                        ->first()
                        ?->nik;

            $nik_suis = EmployeeModel::where('id', $employeeId)
                        ->first()
                        ?->nik;

            if ($nik_issu == $nik_suis) {
                $data = KeluargaAnakModel::select(
                        '*', 
                        DB::raw("if(id_pegawai != '$cekpasangan->id_pegawai', 1, 2) as sts_data")
                    )
                    ->where('id_pegawai', $employeeId)
                    ->orWhere('id_pegawai', $cekpasangan->id_pegawai)
                    ->with(['statusAnak', 'jenisKelamin', 'tkpendid', 'pekerjaan'])
                    ->paginate($per_page);
            } else {
                $data = KeluargaAnakModel::select(
                        '*', 
                        DB::raw("1 as sts_data")
                    )
                    ->where('id_pegawai', $employeeId)
                    ->with(['statusAnak', 'jenisKelamin', 'tkpendid', 'pekerjaan'])
                    ->paginate($per_page);
            }
        } else {
            $data = KeluargaAnakModel::select(
                    '*', 
                    DB::raw("1 as sts_data")
                )
                ->where('id_pegawai', $employeeId)
                ->with(['statusAnak', 'jenisKelamin', 'tkpendid', 'pekerjaan'])
                ->paginate($per_page);
        }
        return $data;
    }
    public static function createSubmission($payload)
    {
        $user = UserModel::where('id', Auth::user()->id)->first();
        $isAdmin = $user->isAdmin();
        if ($isAdmin) {
            $efile_payload = [];
            foreach ($payload as $key => $value) {
                if (str_contains($key, 'efile')) {
                    if (!empty($value)) {
                        $efile_payload[$key] = $value;
                    }
                    unset($payload[$key]);
                }
            }

            // cleaning payload
            foreach ($payload as $key => $value) {
                if ($key == 'status_tunjangan_final') {
                    continue;
                }
                if (!in_array($key, (new KeluargaAnakModel())->allowedPdmFields)) {
                    unset($payload[$key]);
                }
                if (in_array($value, ['null', 'undefined'])) {
                    unset($payload[$key]);
                }
            }

            $riwayat = KeluargaAnakModel::create($payload);

            if (!empty($efile_payload)) {
                KeluargaAnakModel::where('id', $riwayat->id)
                    ->first()
                    ->handleEfileUpdate($efile_payload, 1);
            }
            return $riwayat;
        }

        unset($payload['status_tunjangan_final']);
        $riwayat = (new KeluargaAnakModel())->doPdm($payload, 1);
        return $riwayat;
    }
    public static function deleteSubmission($riwayat_id)
    {
        $user = UserModel::where('id', Auth::user()->id)->first();
        $isAdmin = $user->isAdmin();
        $riwayat = KeluargaAnakModel::where('id', $riwayat_id)->first();
        $deletedRiwayat = $riwayat;

        if ($isAdmin) {
            DB::beginTransaction();
            try {
                $delete = $riwayat->delete();
                if ($delete) {
                    $riwayat->handleEfileDelete($deletedRiwayat->id, @$deletedRiwayat->old_id);
                }

                DB::commit();
                return $delete;
            } 
            catch (AppException $e) {
                DB::rollBack();
                throw $e;
            }
        } else {
            $riwayat = $riwayat->doPdm(null, 3);
            return $riwayat;
        }
    }
    public static function updateSubmission($riwayat_id, $payload)
    {
        $user = UserModel::where('id', Auth::user()->id)->first();
        $isAdmin = $user->isAdmin();

        $riwayat = KeluargaAnakModel::where('id', $riwayat_id)->first();
        if ($isAdmin) {

            $efile_payload = [];
            foreach ($payload as $key => $value) {
                if (str_contains($key, 'efile')) {
                    if (!empty($value)) {
                        $efile_payload[$key] = $value;
                    }
                    unset($payload[$key]);
                }
            }

            // cleaning payload
            foreach ($payload as $key => $value) {
                if ($key == 'status_tunjangan_final') {
                    continue;
                }
                if (!in_array($key, (new KeluargaAnakModel())->allowedPdmFields)) {
                    unset($payload[$key]);
                }
                if (in_array($value, ['null', 'undefined'])) {
                    unset($payload[$key]);
                }
            }


            $riwayat = KeluargaAnakModel::where('id', $riwayat_id)->first();
            $riwayat->update($payload);
            $riwayat->touch();

            if (!empty($efile_payload)) {
                KeluargaAnakModel::where('id', $riwayat_id)
                    ->first()
                    ->handleEfileUpdate($efile_payload, 2);
            }

            return $riwayat;
        }

        unset($payload['status_tunjangan_final']);
        $riwayat = $riwayat->doPdm($payload, 2);
        return $riwayat;
    }
}
