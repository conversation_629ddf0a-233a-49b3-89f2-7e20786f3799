<?php

namespace App\Modules\Employee;

use Illuminate\Support\Facades\Route;
use App\Modules\Employee\Controller\EmployeeController;
use App\Modules\Employee\Controllers\BiodataPegawaiController as ControllersBiodataPegawaiController;
use App\Modules\Employee\Controller\PhotoController;
use App\Modules\RiwayatKgb\Controllers\RiwayatKgbController;
use App\Modules\RiwayatPpk\Controllers\RiwayatPpkController;
use App\Modules\RiwayatCuti\Controllers\RiwayatCutiController;
use App\Modules\KeluargaAnak\Controllers\KeluargaAnakController;
use App\Modules\Kepangkatan\Controllers\KepangkatanController;
use App\Modules\RiwayatJabatan\Controllers\RiwayatJabatanController;
use App\Modules\RiwayatSeminar\Controllers\RiwayatSeminarController;
use App\Modules\KeluargaKekerabatan\Controllers\KeluargaKekerabatanController;
use App\Modules\KeluargaOrangTua\Controllers\KeluargaOrangTuaController;
use App\Modules\RiwayatTandaJasa\Controllers\RiwayatTandaJasaController;
use App\Modules\RiwayatKinerjaAsn\Controllers\RiwayatKinerjaAsnController;
use App\Modules\RiwayatPendidikan\Controllers\RiwayatPendidikanController;
use App\Modules\KeluargaIstriSuami\Controllers\KeluargaIstriSuamiController;
use App\Modules\KeluargaMertua\Controllers\KeluargaMertuaController;
use App\Modules\RiwayatAngkaKredit\Controllers\RiwayatAngkaKreditController;
use App\Modules\RiwayatCpns\Controllers\RiwayatCpnsController;
use App\Modules\RiwayatDiklatTeknis\Controllers\RiwayatDiklatTeknisController;
use App\Modules\RiwayatHukumDisiplin\Controllers\RiwayatHukumDisiplinController;
use App\Modules\RiwayatDiklatFungsional\Controllers\RiwayatDiklatFungsionalController;
use App\Modules\RiwayatDiklatStruktural\Controllers\RiwayatDiklatStrukturalController;
use App\Modules\RiwayatPenguasaanBahasa\Controllers\RiwayatPenguasaanBahasaController;
use App\Modules\RiwayatPendidikanNonFormal\Controllers\RiwayatPendidikanNonFormalController;
use App\Modules\RiwayatSertifikatKompetensi\Controllers\RiwayatSertifikatKompetensiController;
use App\Modules\RiwayatProfesi\Controllers\RiwayatProfesiController;
use App\Modules\RiwayatPns\Controllers\RiwayatPnsController;
use App\Modules\RiwayatDataPeriodik\Controllers\RiwayatDataPeriodikController;
use App\Modules\RiwayatCltn\Controllers\RiwayatCltnController;
use App\Modules\RiwayatUjiKompetensi\Controllers\RiwayatUjiKompetensiController;
use App\Modules\RiwayatOrganisasi\Controllers\RiwayatOrganisasiController;
use App\Modules\RiwayatPenugasan\Controllers\RiwayatPenugasanController;
use App\Modules\RiwayatPeningkatanPendidikan\Controllers\RiwayatPeningkatanPendidikanController;
use App\Modules\RiwayatPppk\Controllers\RiwayatPppkController;
use PHPUnit\TextUI\XmlConfiguration\Group;

Route::prefix('/employee')->middleware(['check.pegawai'])->group(function () {
    Route::get('/', [EmployeeController::class, 'index']);
    Route::get('/datatable', [EmployeeController::class, 'datatable']);
    Route::get('/create', [EmployeeController::class, 'create']);
    Route::post('/', [EmployeeController::class, 'store'])->middleware('authorize:create-employee');
    Route::post('/import', [EmployeeController::class, 'import'])->middleware('authorize:import-employee');
    Route::get('/{employee_id}', [EmployeeController::class, 'show']);
    Route::post('/{employee_id}/sinkron-gelar', [EmployeeController::class, 'sinkronGelar']);
    Route::get('/{employee_id}/detail', [EmployeeController::class, 'detail']);
    Route::get('/{employee_id}/detail/pangkat', [EmployeeController::class, 'detailPangkat']);
    Route::get('/{employee_id}/detail/pppk', [EmployeeController::class, 'detailPppk']);
    Route::get('/{employee_id}/detail/pendidikan', [EmployeeController::class, 'detailPendidikan']);

    Route::withoutMiddleware(['deny.pegawai', 'check.pegawai'])->group(function () {
        Route::get('/{nik}/attkeluarga', [EmployeeController::class, 'attkeluarga']);
        Route::get('/{nik}/{nip}/attkeluargapasangan', [EmployeeController::class, 'attkeluargapasangan']);
    });

    Route::get('/{employee_id}/cpns-pns', [EmployeeController::class, 'cpnsPns']);
    Route::get('/{employee_id}/kgb', [EmployeeController::class, 'kgb']);
    Route::get('/{employee_id}/jabatan', [EmployeeController::class, 'jabatan']);
    // Route::get('/{employee_id}/photo', [EmployeeController::class, 'showAvatar']);
    Route::get('/{employee_id}/edit', [EmployeeController::class, 'edit']);
    Route::get('/{employee_id}/submission-status', [EmployeeController::class, 'submissionStatus']);
    Route::put('/{employee_id}', [EmployeeController::class, 'update']);
    // Route::post('/{employee_id}/photo', [EmployeeController::class, 'updateAvatar']);
    Route::patch('/{employee_id}', [EmployeeController::class, 'update']);
    Route::post('/{employee_id}/biodata', [EmployeeController::class, 'update']);
    Route::delete('/{employee_id}', [EmployeeController::class, 'destroy']);

    Route::get('/{employee_id}/{hubungan_id}/emergencyname', [EmployeeController::class, 'emergencyname']);
    Route::get('/{employee_id}/{hubungan_id}/{familyId}/emergencynumber', [EmployeeController::class, 'emergencynumber']);

    Route::get('/{employee_id}/print-pdf-pegawai', [EmployeeController::class, 'printPdfEmployee']);
    Route::get('/{employee_id}/detail/hukdis', [EmployeeController::class, 'checkStatusHukdis']); //menampillkan hukdis

    Route::prefix('/{employee_id}/photo')->controller(PhotoController::class)->group(function () {
        Route::prefix('/profile')->group(function () {
            Route::get('/', 'getProfilePhoto');
            Route::post('/', 'uploadProfilePhoto');
        });
        Route::prefix('/presence')->group(function () {
            Route::get('/', 'getPresencePhoto');
            Route::post('/', 'uploadPresencePhoto');
            
        });
    });

    Route::prefix('/{employee_id}/pangkat')->group(function () {
        Route::post('/', [KepangkatanController::class, 'createSubmission']);
        Route::post('/sinkronisasi', [KepangkatanController::class, 'sinkronPangkatSiasn']);
        Route::get('/datatable', [KepangkatanController::class, 'employeeDatatable']);
        Route::get('/submission-status', [KepangkatanController::class, 'employeeSubmissionStatus']);
        Route::get('/{riwayatId}', [KepangkatanController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [KepangkatanController::class, 'createDeleteSubmission']);
        Route::patch('/{riwayatId}', [KepangkatanController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [KepangkatanController::class, 'updateSubmission']);
    });
    Route::prefix('/{employee_id}/jabatan')->group(function () {
        Route::post('/', [RiwayatJabatanController::class, 'createSubmission']);
        Route::post('/sinkronisasi', [RiwayatJabatanController::class, 'sinkronJabatanSiasn']);
        Route::get('/datatable', [RiwayatJabatanController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatJabatanController::class, 'employeeSubmissionStatus']);
        Route::patch('/{riwayatId}', [RiwayatJabatanController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [RiwayatJabatanController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatJabatanController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatJabatanController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/kgb')->group(function () {
        Route::get('/datatable', [RiwayatKgbController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatKgbController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatKgbController::class, 'createSubmission']);
        Route::patch('/', [RiwayatKgbController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [RiwayatKgbController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatKgbController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatKgbController::class, 'createDeleteSubmission']);
        Route::get('/gaji/{golonganId}/{masakerjaTahun}', [RiwayatKgbController::class, 'gajiSubmission']);
    });
    Route::prefix('/{employee_id}/pendidikan')->group(function () {
        Route::get('/datatable', [RiwayatPendidikanController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatPendidikanController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatPendidikanController::class, 'createSubmission']);
        Route::post('/sinkronisasi', [RiwayatPendidikanController::class, 'sinkronPendidikanSiasn']);
        Route::patch('/{riwayatId}', [RiwayatPendidikanController::class, 'updateSubmission']);
        Route::put('/{riwayatId}', [RiwayatPendidikanController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [RiwayatPendidikanController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatPendidikanController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatPendidikanController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/pppk')->group(function () {
        Route::get('/datatable', [RiwayatPppkController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatPppkController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatPppkController::class, 'createSubmission']);
        Route::post('/{riwayatId}', [RiwayatPppkController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatPppkController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatPppkController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/cuti')->group(function () {
        Route::get('/datatable', [RiwayatCutiController::class, 'employeeDatatable']);
    });
    Route::prefix('/{employee_id}/diklat-struktural')->group(function () {
        Route::get('/datatable', [RiwayatDiklatStrukturalController::class, 'employeeDatatable']);
    });
    Route::prefix('/{employee_id}/diklat-fungsional')->group(function () {
        Route::get('/datatable', [RiwayatDiklatFungsionalController::class, 'employeeDatatable']);
    });
    Route::prefix('/{employee_id}/diklat-teknis')->group(function () {
        Route::get('/datatable', [RiwayatDiklatTeknisController::class, 'employeeDatatable']);
    });
    Route::prefix('/{employee_id}/seminar')->group(function () {
        Route::get('/datatable', [RiwayatSeminarController::class, 'employeeDatatable']);
    });
    Route::prefix('/{employee_id}/tanda-jasa')->group(function () {
        Route::get('/datatable', [RiwayatTandaJasaController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatTandaJasaController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatTandaJasaController::class, 'createSubmission']);
        Route::patch('/', [RiwayatTandaJasaController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [RiwayatTandaJasaController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatTandaJasaController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatTandaJasaController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/penguasaan-bahasa')->group(function () {
        Route::get('/datatable', [RiwayatPenguasaanBahasaController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatPenguasaanBahasaController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatPenguasaanBahasaController::class, 'createSubmission']);
        Route::patch('/', [RiwayatPenguasaanBahasaController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatPenguasaanBahasaController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatPenguasaanBahasaController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/hukum-disiplin')->group(function () {
        Route::get('/datatable', [RiwayatHukumDisiplinController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatHukumDisiplinController::class, 'employeeSubmissionStatus']);
        Route::post('/sinkronisasi', [RiwayatHukumDisiplinController::class, 'sinkronSiasn']);
        Route::post('/', [RiwayatHukumDisiplinController::class, 'createSubmission']);
        Route::patch('/', [RiwayatHukumDisiplinController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatHukumDisiplinController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatHukumDisiplinController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/ppk')->group(function () {
        Route::get('/datatable', [RiwayatPpkController::class, 'employeeDatatable']);
    });
    Route::prefix('/{employee_id}/kinerja-asn')->group(function () {
        Route::get('/datatable', [RiwayatKinerjaAsnController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatKinerjaAsnController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatKinerjaAsnController::class, 'createSubmission']);
        Route::patch('/', [RiwayatKinerjaAsnController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatKinerjaAsnController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatKinerjaAsnController::class, 'createDeleteSubmission']);
        Route::get('/penilai/{nipPenilai}', [RiwayatKinerjaAsnController::class, 'penilaiSubmission']);
    });
    Route::prefix('/{employee_id}/angka-kredit')->group(function () {
        Route::get('/datatable', [RiwayatAngkaKreditController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatAngkaKreditController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatAngkaKreditController::class, 'createSubmission']);
        Route::post('/sinkronisasi', [RiwayatAngkaKreditController::class, 'sinkronSiasn']);
        Route::patch('/{riwayatId}', [RiwayatAngkaKreditController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatAngkaKreditController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatAngkaKreditController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/pendidikan-non-formal')->group(function () {
        Route::get('/datatable', [RiwayatPendidikanNonFormalController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatPendidikanNonFormalController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatPendidikanNonFormalController::class, 'createSubmission']);
        Route::post('/sinkronisasi-kursus', [RiwayatPendidikanNonFormalController::class, 'sinkronKursusSiasn']);
        Route::post('/sinkronisasi-diklat', [RiwayatPendidikanNonFormalController::class, 'sinkronDiklatSiasn']);
        Route::patch('/{riwayatId}', [RiwayatPendidikanNonFormalController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatPendidikanNonFormalController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatPendidikanNonFormalController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/sertifikat-kompetensi')->group(function () {
        Route::get('/datatable', [RiwayatSertifikatKompetensiController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatSertifikatKompetensiController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatSertifikatKompetensiController::class, 'createSubmission']);
        Route::patch('/{riwayatId}', [RiwayatSertifikatKompetensiController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatSertifikatKompetensiController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatSertifikatKompetensiController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/profesi')->group(function () {
        Route::get('/datatable', [RiwayatProfesiController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatProfesiController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatProfesiController::class, 'createSubmission']);
        Route::patch('/{riwayatId}', [RiwayatProfesiController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatProfesiController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatProfesiController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/orang-tua')->group(function () {
        Route::get('/datatable', [KeluargaOrangTuaController::class, 'employeeDatatable']);
        Route::get('/submission-status', [KeluargaOrangTuaController::class, 'employeeSubmissionStatus']);
        Route::post('/', [KeluargaOrangTuaController::class, 'createSubmission']);
        Route::patch('/', [KeluargaOrangTuaController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [KeluargaOrangTuaController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [KeluargaOrangTuaController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/mertua')->group(function () {
        Route::get('/datatable', [KeluargaMertuaController::class, 'employeeDatatable']);
        Route::get('/submission-status', [KeluargaMertuaController::class, 'employeeSubmissionStatus']);
        Route::post('/', [KeluargaMertuaController::class, 'createSubmission']);
        Route::patch('/', [KeluargaMertuaController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [KeluargaMertuaController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [KeluargaMertuaController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/istri-suami')->group(function () {
        Route::get('/datatable', [KeluargaIstriSuamiController::class, 'employeeDatatable']);
        Route::get('/datatableusulan', [KeluargaIstriSuamiController::class, 'employeeDatatableUsulan']);
        Route::get('/submission-status', [KeluargaIstriSuamiController::class, 'employeeSubmissionStatus']);
        Route::post('/', [KeluargaIstriSuamiController::class, 'createSubmission']);
        Route::patch('/', [KeluargaIstriSuamiController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [KeluargaIstriSuamiController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [KeluargaIstriSuamiController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [KeluargaIstriSuamiController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/anak')->group(function () {
        Route::get('/datatable', [KeluargaAnakController::class, 'employeeDatatable']);
        Route::get('/submission-status', [KeluargaAnakController::class, 'employeeSubmissionStatus']);
        Route::post('/', [KeluargaAnakController::class, 'createSubmission']);
        Route::patch('/', [KeluargaAnakController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [KeluargaAnakController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [KeluargaAnakController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [KeluargaAnakController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/kekerabatan')->group(function () {
        Route::get('/datatable', [KeluargaKekerabatanController::class, 'employeeDatatable']);
        Route::get('/submission-status', [KeluargaKekerabatanController::class, 'employeeSubmissionStatus']);
        Route::post('/', [KeluargaKekerabatanController::class, 'createSubmission']);
        Route::patch('/', [KeluargaKekerabatanController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [KeluargaKekerabatanController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [KeluargaKekerabatanController::class, 'createDeleteSubmission']);
    });

    Route::prefix('/{employee_id}/cpns')->group(function () {
        Route::post('/', [RiwayatCpnsController::class, 'updateEmployeeCpns']);
    });
    Route::prefix('/{employee_id}/pns')->group(function () {
        Route::post('/', [RiwayatPnsController::class, 'updateEmployeePns']);
    });

    Route::prefix('/{employee_id}/peningkatanpendidikan')->group(function () {
        Route::get('/datatable', [RiwayatPeningkatanPendidikanController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatPeningkatanPendidikanController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatPeningkatanPendidikanController::class, 'createSubmission']);
        Route::patch('/', [RiwayatPeningkatanPendidikanController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [RiwayatPeningkatanPendidikanController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatPeningkatanPendidikanController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatPeningkatanPendidikanController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/penugasan')->group(function () {
        Route::get('/datatable', [RiwayatPenugasanController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatPenugasanController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatPenugasanController::class, 'createSubmission']);
        Route::post('/{riwayatId}', [RiwayatPenugasanController::class, 'updateSubmission']);
        Route::get('/check-penugasan-employee', [RiwayatPenugasanController::class, 'checkPenugasanEmployee']);
        Route::get('/{riwayatId}', [RiwayatPenugasanController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatPenugasanController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/organisasi')->group(function () {
        Route::get('/datatable', [RiwayatOrganisasiController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatOrganisasiController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatOrganisasiController::class, 'createSubmission']);
        Route::patch('/', [RiwayatOrganisasiController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatOrganisasiController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatOrganisasiController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/dataperiodik')->group(function () {
        Route::get('/datatable', [RiwayatDataPeriodikController::class, 'employeeDatatable']);
        Route::post('/', [RiwayatDataPeriodikController::class, 'createSubmission']);
        Route::patch('/', [RiwayatDataPeriodikController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatDataPeriodikController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatDataPeriodikController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/ujikompetensi')->group(function () {
        Route::get('/datatable', [RiwayatUjiKompetensiController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatUjiKompetensiController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatUjiKompetensiController::class, 'createSubmission']);
        Route::patch('/', [RiwayatUjiKompetensiController::class, 'updateSubmission']);
        Route::post('/{riwayatId}', [RiwayatUjiKompetensiController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatUjiKompetensiController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatUjiKompetensiController::class, 'createDeleteSubmission']);
    });
    Route::prefix('/{employee_id}/cltn')->group(function () {
        Route::get('/datatable', [RiwayatCltnController::class, 'employeeDatatable']);
        Route::get('/submission-status', [RiwayatCltnController::class, 'employeeSubmissionStatus']);
        Route::post('/', [RiwayatCltnController::class, 'createSubmission']);
        Route::patch('/', [RiwayatCltnController::class, 'updateSubmission']);
        Route::get('/{riwayatId}', [RiwayatCltnController::class, 'employeeRiwayat']);
        Route::delete('/{riwayatId}', [RiwayatCltnController::class, 'createDeleteSubmission']);
    });
});

Route::prefix('/biodata-pegawai')->group(function () {
    Route::get('/', [ControllersBiodataPegawaiController::class, 'index']);
});
