# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

This is a Laravel 10-based Human Resources Management System (SIMPEG/SIASN) for BKD Jawa Tengah. The application uses a modular architecture with Laravel Passport for API authentication, Vue.js 3 for frontend components, and includes extensive employee data management with a Personal Data Management (PDM) system.

## Development Commands

### Environment Setup
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Copy environment file (then configure database, JWT, etc.)
cp .env.example .env

# Generate application key
php artisan key:generate

# Run database migrations
php artisan migrate

# Generate Passport keys for API authentication
php artisan passport:install
```

### Development Server
```bash
# Start Laravel development server (PHP)
php artisan serve

# Start Vite development server for assets (JavaScript/CSS)
npm run dev

# Watch for file changes during development
npm run dev -- --watch
```

### Building Assets
```bash
# Build for development
npm run dev

# Build for production
npm run build
```

### Testing
```bash
# Run all tests
php artisan test
# or
./vendor/bin/phpunit

# Run specific test suite
./vendor/bin/phpunit --testsuite=Feature
./vendor/bin/phpunit --testsuite=Unit

# Run tests with coverage
./vendor/bin/phpunit --coverage-html coverage
```

### Database Operations
```bash
# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Refresh migrations (drops all tables and re-runs)
php artisan migrate:refresh

# Seed database
php artisan db:seed
```

### Artisan Commands
```bash
# Clear application cache
php artisan cache:clear

# Clear configuration cache
php artisan config:clear

# Clear route cache
php artisan route:clear

# Clear view cache
php artisan view:clear

# Generate IDE helper files (if installed)
php artisan ide-helper:generate
```

### Docker Development
```bash
# Build and start containers
docker-compose up -d

# View logs
docker-compose logs -f

# Access container shell
docker-compose exec php-apache bash

# Stop containers
docker-compose down
```

## Architecture Overview

### Modular Structure
This application follows a custom modular architecture where each domain/feature is organized as a module under `app/Modules/`. Each module contains:

- **Controllers/**: HTTP controllers for handling requests
- **Models/**: Eloquent models and database interactions  
- **Repositories/**: Data access layer abstracting model operations
- **Requests/**: Form request classes for validation
- **Views/**: Blade templates and Vue components
- **routes.php**: Module-specific routes

Key modules include:
- `Employee`: Core employee management
- `PerubahanData`: Data change requests and PDM system
- `ExecutiveSummary`: Reports and analytics
- `Siasn`: Integration with national SIASN system
- `User`, `Role`, `Permission`: Authentication and authorization
- Various `Riwayat*` modules: Employee history tracking (education, positions, etc.)

### Authentication & Authorization
- **Laravel Passport**: API authentication with OAuth2
- **JWT Authentication**: Token-based auth for SPA
- **Role-based Access Control**: Users have roles with specific permissions
- **Middleware**: `jwt.auth` for API routes, `deny.pegawai` for admin-only functions

### Personal Data Management (PDM) System
The PDM system allows employees to submit data changes that require verification:

- **PdmTrait**: Core trait providing PDM functionality to models
- **Temp Tables**: Temporary tables store proposed changes (e.g., `*_temp` tables)
- **Verification Flow**: Changes go through verificator approval process
- **Field-level Tracking**: Individual field changes are tracked
- **Schedule Control**: PDM periods can be opened/closed per data type

Key PDM components:
- `PdmSchedule`: Controls when PDM is open for different data types
- `PdmVerificator`: Manages verificator assignments  
- Temp models with verificator and field tracking tables

### Frontend Integration
- **Vue.js 3**: Component-based frontend with Composition API
- **Vite**: Modern build tool replacing Laravel Mix
- **Tailwind CSS**: Utility-first CSS framework
- **Bootstrap 4**: Legacy CSS framework (mixed usage)
- **CKEditor**: Rich text editing
- **VeeValidate + Yup**: Form validation

### Database Architecture
- **Modular Tables**: Each module has its own set of tables
- **History Tracking**: Extensive `r_*` (riwayat/history) tables
- **Master Data**: `a_*` tables for reference data
- **Temp Tables**: `*_temp` tables for PDM system
- **Employee Central**: `tb_01` is the main employee table

### Key Traits & Utilities
- **PdmTrait**: Enables PDM functionality on models
- **UnitKerjaFilterTrait**: Filters data by organizational unit
- **FileHandler**: File upload and management utilities
- **JsonResponseHandler**: Standardized API responses
- **JWTHelper**: JWT token management

## Development Guidelines

### Adding New Modules
When creating new modules, follow the established pattern:
1. Create directory structure under `app/Modules/NewModule/`
2. Include Controllers, Models, Repositories, Requests, Views directories
3. Add `routes.php` file for module routes
4. Register routes in `routes/web.php` and `routes/api.php`
5. Use the `ROUTE_MARKER` comment for automatic route registration

### PDM Implementation
To add PDM support to a model:
1. Create temp table with `*_temp` suffix
2. Create verificator table: `*_temp_verificator`
3. Create field table: `*_temp_field`  
4. Add `PdmTrait` to your model
5. Define `$allowedPdmFields`, `$pdmClass`, `$pdmVerificatorClass`, `$pdmFieldClass`
6. Implement required PDM methods in repository

### API Development
- Use JWT authentication for API routes
- Implement repository pattern for data access
- Follow RESTful conventions
- Use form requests for validation
- Return standardized JSON responses via JsonResponseHandler

### Vue.js Components
- Place Vue components in module Views directories
- Use Composition API for new components
- Implement proper prop validation
- Follow existing naming conventions

### Database Migrations
- Use descriptive migration names with timestamps
- Include both up and down methods
- Add foreign key constraints where appropriate
- Use proper indexing for performance

### Testing Strategy
- Write feature tests for API endpoints
- Create unit tests for complex business logic
- Test PDM workflows thoroughly
- Mock external API calls (SIASN integration)

## Integration Points

### SIASN Integration
The application integrates with Indonesia's national civil servant information system (SIASN):
- Employee data synchronization
- Automated data validation against national records
- Bidirectional data exchange for personnel actions

### File Management
- File uploads handled via dedicated File module
- Support for various document types (PDF, images, etc.)
- Integration with document management workflows

### Reporting System
- Executive summary reports with multiple output formats
- PDF generation using Laravel Snappy
- Excel export capabilities via Maatwebsite Excel
- Statistical dashboards and analytics

## Environment Notes

### Local Development with Laragon
This project is configured for Laragon development environment:
- Document root: `D:\laragon\www\bkd-jateng-backend`
- Database: MySQL via Laragon
- PHP extensions required: pdo_mysql, gd, fileinfo, zip

### Docker Setup
Alternative Docker setup available:
- PHP-Apache container on port 4009
- Volume mounting for development
- Apache configuration in `/apache/conf/`

### Production Considerations
- Configure proper environment variables
- Set up task scheduling for cron jobs
- Enable proper logging and monitoring
- Configure file storage and backups
- Set up SSL certificates
- Optimize Composer autoloader with `--optimize-autoloader`