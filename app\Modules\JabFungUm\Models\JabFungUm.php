<?php

namespace App\Modules\JabFungUm\Models;
    
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Handler\ModelSearchHandler;

class JabFungUm extends Model
{
    use SoftDeletes;
    protected $table = 'a_jabfungum';
    protected $guarded = [];
    protected $primaryKey = 'idjabfungum';
    protected $casts = [
        'id' => 'string',
        'idjabfungum' => 'string'
    ];

    public function scopeSearch($query, $keyword)
    {
        $searchable = ['idjabfungum', 'jabfungum'];
        return ModelSearchHandler::handle($query, $searchable, $keyword);
    }
}