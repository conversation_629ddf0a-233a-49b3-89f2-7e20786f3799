<?php

namespace App\Modules\KeluargaAnak\Models;

use App\Modules\Employee\Model\EmployeeModel;
use Illuminate\Database\Eloquent\Model;

class KeluargaAnakTempModel extends Model
{
    public $table = 'r_anak_temp';
    protected $guarded = [];

    public function employee()
    {
        return $this->belongsTo(EmployeeModel::class, 'id_pegawai', 'id');
    }

    public function initial()
    {
        return $this->belongsTo(KeluargaAnakModel::class, 'id_riwayat', 'id');
    }

    public function fields()
    {
        return $this->hasMany(KeluargaAnakTempFieldModel::class, 'temp_id', 'id');
    }
}
