<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class NominatifPegawaiController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;

        $id_unit_kerja = $request->input('id_unit_kerja');
        $id_sub_unit = $request->input('id_sub_unit');
        $id_sub_sub_unit = $request->input('id_sub_sub_unit');

        $pecah_induk_upt = substr($id_sub_unit, 3, 2);
        $pecah_sub_unit = substr($id_sub_unit, 6, 2);
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2);
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2);

        $status_pegawai = $request->input('status_pegawai');

        $id_jenjab = $request->input('id_jenjab');
        $idtingkat = $request->input('idtingkat');
        $tingkat_jabatan = $request->input('tingkat_jabatan');
        $idjabfungum = $request->input('idjabfungum');

        $id_golongan_awal = $request->input('id_golongan_awal');
        $id_golongan_akhir = $request->input('id_golongan_akhir');
        $picked_gol = $request->input('picked_gol'); 

        $id_eselon = $request->input('id_eselon');
        $id_gender = $request->input('id_gender');
        $id_agama = $request->input('id_agama');
        $id_pendidikan = $request->input('id_pendidikan');
        $id_jenkedudupeg = $request->input('id_jenkedudupeg');
        $id_penugasan = $request->input('id_penugasan');
        $id_pegawai = $request->input('id_pegawai'); 
        
        $picked_urutan = $request->input('picked_urutan');
        $picked_order = $request->input('picked_order');

        $keyword = $request->input('keyword') != null ? $request->input('keyword') : null;

        // dd($request->all());

        /* Kondisi urut data */
        switch ($picked_urutan) {
            case "1":
                $urutan = "r_kepangkatan.id_kepangkatan";
                break;
            case "2":
                $urutan = "tb_01.nip";
                break;
            case "3":
                $urutan = "tb_01.nama";
                break;
            case "4":
                $urutan = "tb_01.tanggal_lahir";
                break;
            default:
                $urutan = "r_kepangkatan.id_kepangkatan";
                break;
        }

        $data = DB::table('tb_01')
            ->join('a_skpd as unit_kerja', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'unit_kerja.id_unit_kerja')
                    ->where('unit_kerja.id_induk_upt', '=', '00')
                    ->where('unit_kerja.id_sub_unit', '=', '00')
                    ->where('unit_kerja.id_sub_sub_unit', '=', '00')
                    ->where('unit_kerja.id_sub_sub_sub_unit', '=', '00');
            })
            ->leftJoin('a_skpd as induk_upt', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'induk_upt.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'induk_upt.id_induk_upt')
                    ->where('induk_upt.id_sub_unit', '=', '00')
                    ->where('induk_upt.id_sub_sub_unit', '=', '00')
                    ->where('induk_upt.id_sub_sub_sub_unit', '=', '00')
                    ->where('induk_upt.id_induk_upt', '!=', '00');
            })
            ->leftJoin('a_skpd as sub_unit', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'sub_unit.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'sub_unit.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'sub_unit.id_sub_unit')
                    ->where('sub_unit.id_sub_sub_unit', '=', '00')
                    ->where('sub_unit.id_sub_sub_sub_unit', '=', '00')
                    ->where('sub_unit.id_sub_unit', '!=', '00');
            })
            ->leftJoin('a_skpd as sub_sub_unit', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'sub_sub_unit.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'sub_sub_unit.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'sub_sub_unit.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'sub_sub_unit.id_sub_sub_unit')
                    ->where('sub_sub_unit.id_sub_sub_sub_unit', '=', '00')
                    ->where('sub_sub_unit.id_sub_sub_unit', '!=', '00');
            })
            ->leftJoin('a_skpd as sub_sub_sub_unit', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'sub_sub_sub_unit.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'sub_sub_sub_unit.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'sub_sub_sub_unit.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'sub_sub_sub_unit.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'sub_sub_sub_unit.id_sub_sub_sub_unit')
                    ->where('sub_sub_sub_unit.id_sub_sub_sub_unit', '!=', '00');
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            // ->leftjoin('a_skpd as z', 'r_jabatan.id_koordinator', '=', 'z.idskpd')
            ->leftjoin('r_kepangkatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_kepangkatan.id_pegawai')
                    ->where('r_kepangkatan.isakhir', '=', 1);
            })
            ->leftjoin('r_pendidikan_formal', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
                    ->where('r_pendidikan_formal.isakhir', '=', 1);
            })
            ->leftjoin('a_stspeg', 'tb_01.status_pegawai', '=', 'a_stspeg.idstspeg')
            ->leftjoin('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftjoin('a_jenjab', 'r_jabatan.id_jenis_jabatan', '=', 'a_jenjab.idjenjab')
            ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
            ->leftjoin('agama', 'tb_01.agama', '=', 'agama.id')
            ->leftjoin('gender', 'tb_01.jenis_kelamin', '=', 'gender.id')
            ->leftjoin('a_sub_jabatan', 'r_jabatan.id_sub_jabatan', '=', 'a_sub_jabatan.id')
            // ->leftjoin('a_instansi_luar', 'r_jabatan.id_penugasan', '=', 'a_instansi_luar.id')
            ->leftjoin('r_cpns', 'tb_01.id', '=', 'r_cpns.id_pegawai')
            ->leftjoin('r_pns', 'tb_01.id', '=', 'r_pns.id_pegawai')
            ->leftjoin('jenkedudupeg', 'tb_01.kedudukan_pegawai', '=', 'jenkedudupeg.id')
            ->leftjoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('r_penugasan', 'tb_01.id', '=', 'r_penugasan.id_pegawai')
            ->leftjoin('a_jenis_penugasan', 'r_penugasan.id_penugasan', '=', 'a_jenis_penugasan.id')
            ->select(
                'tb_01.id',
                'tb_01.nip as nip',
                DB::raw(
                    'CONCAT(tb_01.gelar_depan,IF(LENGTH(tb_01.gelar_depan)>0,". ",""),tb_01.nama,IF(LENGTH(tb_01.gelar_belakang)>0,", ",""),tb_01.gelar_belakang) as nama
                '),
                'tb_01.tempat_lahir as tmlhr',
                'tb_01.tanggal_lahir as tglhr',
                'agama.agama as idagama',
                'gender.name as idjenkel',
                'tb_01.domisili_alamat as alm',
                'tb_01.nomor_hp as telp',
                'tb_01.nomor_npwp as nonpwp',
                'tb_01.nik as noktp',
                'a_stspeg.stspeg as idstspeg',
                'r_cpns.tmt_cpns as tmtcpn',
                // 'z.skpd as idkoord',
                'r_pns.tmt_pns as tmtpns',
                'kepangkatan.name as golru',
                'kepangkatan.pangkat as pangkat',
                'r_kepangkatan.tmt_sk as tmtpkt',
                'r_pendidikan_formal.jenjang as idtkpendid',
                'r_pendidikan_formal.jurusan as idjenjurusan',
                'r_pendidikan_formal.nama_sekolah as sekolah',
                'r_pendidikan_formal.tahun_lulus as thijaz',
                'unit_kerja.skpd as unit_kerja',
                'induk_upt.skpd as induk_upt',
                'sub_unit.skpd as sub_unit',
                'sub_sub_unit.skpd as sub_sub_unit',
                'sub_sub_sub_unit.skpd as sub_sub_sub_unit',
                'a_jenjab.jenjab as idjenjab',
                'r_jabatan.id as r_jabatan_id',
                'r_jabatan.jabatan as jabatan',
                'r_jabatan.tmt_jabatan as tmtjbt',
                'eselon.name as idesljbt',
                'a_sub_jabatan.nama as subjabatan',
                // 'a_instansi_luar.nama_instansi as instansiluar',
                'a_jenis_penugasan.penugasan',
                // 'r_penugasan.id_penugasan',
                DB::raw("IF(a_jabfung.jenjang = 1, 'Keterampilan', 'Keahlian') as jenjang"),
                DB::raw("CONCAT(r_kepangkatan.masa_kerja_tahun, ' tahun ', r_kepangkatan.masa_kerja_bulan, ' bulan') AS masa_kerja")
            )
            ->orderBy($urutan, $picked_order);

            // dd($data->toSql());
            // dd($data->getBindings());



        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data = $data->where('tb_01.id_unit_kerja', [$id_unit_kerja]);
        }

        if ($id_sub_unit != null && $id_sub_unit != "") {
            $data = $data->where('tb_01.id_unit_kerja', [$id_unit_kerja])
                        ->where('tb_01.id_induk_upt', [$pecah_induk_upt])
                        ->where('tb_01.id_sub_unit', [$pecah_sub_unit]);
        }

        if ($id_sub_sub_unit != null && $id_sub_sub_unit != "") {
            $data = $data->where('tb_01.id_unit_kerja', [$id_unit_kerja])
                        ->where('tb_01.id_induk_upt', [$pecah_induk_upt])
                        ->where('tb_01.id_sub_unit', [$pecah_sub_unit])
                        ->where('tb_01.id_sub_sub_unit', [$pecah_sub_sub_unit])
                        ->where('tb_01.id_sub_sub_sub_unit', [$pecah_sub_sub_sub_unit]);
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('tb_01.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        if ($idtingkat != null && $idtingkat != "") { //nama jabatan fungsional
            $data = $data->where('a_jabfung.tingkat', [$idtingkat]);
        }

        if ($tingkat_jabatan != null && $tingkat_jabatan != "") { //tingkat jabatan fungsional
            $data = $data->where('r_jabatan.id_jabatan', [$tingkat_jabatan]);
        }

        if ($idjabfungum != null && $idjabfungum != "") { //nama jabatan teknis
            $data = $data->where('r_jabatan.id_jabatan', [$idjabfungum]);
        }

        if ($id_golongan_awal != null && $id_golongan_awal != "") {
            if($picked_gol == 1){ //keatas
                $data = $data->where('r_kepangkatan.id_kepangkatan', '>=' ,[$id_golongan_awal]);
            }else if($picked_gol == 2){ //kebawah
                $data = $data->where('r_kepangkatan.id_kepangkatan', '<=' ,[$id_golongan_awal]);
            }else{ //antara
                $data->whereBetween('r_kepangkatan.id_kepangkatan', [$id_golongan_awal, $id_golongan_akhir]);
            }
        }

        if ($id_golongan_akhir != null && $id_golongan_akhir != "") {
            if($picked_gol == 1){ //keatas
                $data = $data->where('r_kepangkatan.id_kepangkatan', '>=' ,[$id_golongan_akhir]);
            }else if($picked_gol == 2){ //kebawah
                $data = $data->where('r_kepangkatan.id_kepangkatan', '<=' ,[$id_golongan_akhir]);
            }else{ //antara
                $data->whereBetween('r_kepangkatan.id_kepangkatan', [$id_golongan_awal, $id_golongan_akhir]);
            }
        }

        if ($id_eselon != null && $id_eselon != "") {
            $data = $data->where('r_jabatan.id_eselon', [$id_eselon]);
        }

        if ($id_gender != null && $id_gender != "") {
            $data = $data->where('tb_01.jenis_kelamin', [$id_gender]);
        }

        if ($id_agama != null && $id_agama != "") {
            $data = $data->where('tb_01.agama', [$id_agama]);
        }

        if ($id_pendidikan != null && $id_pendidikan != "") {
            $data = $data->where('r_pendidikan_formal.id_jenjang', [$id_pendidikan]);
        }

        if ($id_jenkedudupeg != null && $id_jenkedudupeg != "") {
            $data = $data->where('tb_01.kedudukan_pegawai', [$id_jenkedudupeg]);
        }

        if ($id_penugasan != null && $id_penugasan != "") {
            $data = $data->where('r_penugasan.id_penugasan', $id_penugasan)
                         ->where('r_penugasan.isakhir', '=', 1);
        }

        if ($id_pegawai != null && $id_pegawai != "") {
            // $x = 0;
            // $pegawai = '';
            // foreach ($id_pegawai as $item) {
            //     $x++;
            //     $pegawai .= $item.((count($id_pegawai) == $x)?'':',');
            // }

            // $data = $data->where('tb_01.id_pegawai', [$pegawai]);
            $data = $data->whereIn('tb_01.id', $id_pegawai);
        }

        if ($keyword != null) $data->whereRaw("tb_01.nama LIKE '%" . $keyword . "%' or tb_01.nip LIKE '%" . $keyword . "%'");

        $data = $data->paginate($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }
}
