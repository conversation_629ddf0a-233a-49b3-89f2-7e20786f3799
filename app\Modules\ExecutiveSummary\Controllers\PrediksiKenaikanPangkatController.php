<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PrediksiKenaikanPangkatController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.00.50.00.00

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01')
            ->select(
                'tb_01.nip as nip',
                DB::raw(
                    'CONCAT(tb_01.gelar_depan,IF(LENGTH(tb_01.gelar_depan)>0,". ",""),tb_01.nama,IF(LENGTH(tb_01.gelar_belakang)>0,", ",""),tb_01.gelar_belakang) as nama
                    '
                ),
                'r_kepangkatan.tmt_sk as tmt_sk',
                'kepangkatan.name as golongan',
                'r_jabatan.jabatan as jabatan',
                'unit_kerja.path as unit_kerja',
                'r_kepangkatan.masa_kerja_tahun',
                'r_kepangkatan.masa_kerja_bulan',
                DB::raw('IF(r_jabatan.id_jenis_jabatan=2,DATE_ADD(r_kepangkatan.tmt_sk, INTERVAL 2 YEAR),
                    DATE_ADD(r_kepangkatan.tmt_sk, INTERVAL 4 YEAR)) AS tmt_kp_next')
            )
            ->join('r_kepangkatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_kepangkatan.id_pegawai')
                    ->where('r_kepangkatan.isakhir', '=', 1);
            })
            ->leftjoin('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('a_skpd as unit_kerja', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'unit_kerja.id_unit_kerja')
                    ->where('unit_kerja.id_induk_upt', '=', '00')
                    ->where('unit_kerja.id_sub_unit', '=', '00')
                    ->where('unit_kerja.id_sub_sub_unit', '=', '00')
                    ->where('unit_kerja.id_sub_sub_sub_unit', '=', '00');
            })
            ->leftJoin('a_skpd as induk_upt', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'induk_upt.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'induk_upt.id_induk_upt')
                    ->where('induk_upt.id_sub_unit', '=', '00')
                    ->where('induk_upt.id_sub_sub_unit', '=', '00')
                    ->where('induk_upt.id_sub_sub_sub_unit', '=', '00')
                    ->where('induk_upt.id_induk_upt', '!=', '00');
            })
            ->leftJoin('a_skpd as sub_unit', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'sub_unit.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'sub_unit.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'sub_unit.id_sub_unit')
                    ->where('sub_unit.id_sub_sub_unit', '=', '00')
                    ->where('sub_unit.id_sub_sub_sub_unit', '=', '00')
                    ->where('sub_unit.id_sub_unit', '!=', '00');
            })
            ->leftJoin('a_skpd as sub_sub_unit', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'sub_sub_unit.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'sub_sub_unit.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'sub_sub_unit.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'sub_sub_unit.id_sub_sub_unit')
                    ->where('sub_sub_unit.id_sub_sub_sub_unit', '=', '00')
                    ->where('sub_sub_unit.id_sub_sub_unit', '!=', '00');
            })
            ->leftJoin('a_skpd as sub_sub_sub_unit', function ($join) {
                $join->on('tb_01.id_unit_kerja', '=', 'sub_sub_sub_unit.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'sub_sub_sub_unit.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'sub_sub_sub_unit.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'sub_sub_sub_unit.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'sub_sub_sub_unit.id_sub_sub_sub_unit')
                    ->where('sub_sub_sub_unit.id_sub_sub_sub_unit', '!=', '00');
            })
            ->where('r_kepangkatan.isakhir', '=', 1)
            ->whereNotIn('tb_01.kedudukan_pegawai', [99, 21])
            ->where(function ($query) {
                return (new EmployeeModel())->scopeWhereUserHaveAccess($query, 'tb_01.id_unit_kerja');
            });


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                    ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                    ->where('tb_01.id_sub_unit', $pecah_sub_unit)
                    ->where('tb_01.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('tb_01.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                    ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                    ->where('tb_01.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('tb_01.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        if ($month != null && $month != "") {
            $data = $data->havingRaw('MONTH(tmt_kp_next) <= ?', [$month]);
        }

        if ($year != null && $year != "") {
            $data = $data->havingRaw('YEAR(tmt_kp_next) <= ?', [$year]);
        }

        $data = $data
            ->orderBy('tmt_kp_next', 'desc')
            ->paginate($per_page);

        return JsonResponseHandler::setResult($data)->send();
    }
}
