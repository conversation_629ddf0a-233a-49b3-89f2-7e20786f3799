<?php

namespace App\Modules\JabFung\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JabFung\Models\JabFung;
use App\Modules\JabFung\Repositories\JabFungRepository;
use App\Modules\JabFung\Requests\JabFungCreateRequest;
use Illuminate\Http\Request;

class JabFungController extends Controller
{
    public function index()
    {
        $data = JabFung::where('flag', 1)
            ->selectRaw("
                idjabfung,
                concat(if(kelas_jabatan, concat(kelas_jabatan, ' - '), ''), jabfung) as jabfung,
                kelas_jabatan,
                tingkat,
                jenjang2
            ")
            ->get();
        return JsonResponseHandler::setResult($data)->send();

    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JabFungRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $jabfung_id)
    {
        $jabfung = JabFung::where('id', $jabfung_id)->first();
        return JsonResponseHandler::setResult($jabfung)->send();
    }


    public function create()
    {
        return view('JabFung::create');
    }

    public function store(JabFungCreateRequest $request)
    {
        $payload = $request->all();
        $jabfung = JabFungRepository::create($payload);
        return JsonResponseHandler::setResult($jabfung)->send();
    }

    public function show(Request $request, $id)
    {
        $jabfung = JabFungRepository::get($id);
        return JsonResponseHandler::setResult($jabfung)->send();
    }

    public function edit($id)
    {
        return view('JabFung::edit', ['jabfung_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jabfung = JabFungRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jabfung)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JabFungRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
