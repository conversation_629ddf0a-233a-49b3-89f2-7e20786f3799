<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PrediksiKGBController extends Controller
{
    public function datatable(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.00.50.00.00

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $month = $request->input('month');
        $year = $request->input('year');

        // dd($request->all());

        $data = DB::table('r_gaji_berkala')
            ->leftJoin('tb_01 as b', 'r_gaji_berkala.id_pegawai', '=', 'b.id')
            ->join('kepangkatan', 'r_gaji_berkala.id_golongan_ruang', '=', 'kepangkatan.id')
            ->select(
                'b.nip',
                DB::raw(
                    'CONCAT(b.gelar_depan,IF(LENGTH(b.gelar_depan)>0,". ",""),b.nama,IF(LENGTH(b.gelar_belakang)>0,", ",""),b.gelar_belakang) as nama
                    '
                ),
                'kepangkatan.name AS golongan',
                'r_gaji_berkala.tmt_sk',
                'r_gaji_berkala.masa_kerja_tahun',
                'r_gaji_berkala.masa_kerja_bulan'
            )
            ->where('r_gaji_berkala.isakhir', '=', 1)
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->where(function ($query) {
                return (new EmployeeModel())->scopeWhereUserHaveAccess($query, 'b.id_unit_kerja');
            });

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', $status_pegawai);
        }

        if ($month != null && $month != "") {
            $data = $data->whereRaw('MONTH(r_gaji_berkala.tmt_sk) = ?', [$month]);
        }

        if ($year != null && $year != "") {
            $data = $data->havingRaw('YEAR(r_gaji_berkala.tmt_sk) + 2 = ?', [$year]);
        }

        $data = $data->orderBy('r_gaji_berkala.tmt_sk', 'asc')->limit(10)->paginate();
        return JsonResponseHandler::setResult($data)->send();
    }
}
