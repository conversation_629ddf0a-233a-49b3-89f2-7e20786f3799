<?php

namespace App\Modules\Eselon\Repositories;

use App\Modules\Eselon\Models\Eselon;

class EselonRepository
{
    public static function datatable($per_page = 15)
    {
        $data = Eselon::paginate($per_page);
        return $data;
    }
    public static function get($eselon_id)
    {
        $eselon = Eselon::where('id', $eselon_id)->first();
        return $eselon;
    }
    public static function create($eselon)
    {
        $eselon = Eselon::create($eselon);
        return $eselon;
    }

    public static function update($eselon_id, $eselon)
    {
        Eselon::where('id', $eselon_id)->update($eselon);
        $eselon = Eselon::where('id', $eselon_id)->first();
        return $eselon;
    }

    public static function delete($eselon_id)
    {
        $delete = Eselon::where('id', $eselon_id)->delete();
        return $delete;
    }
}
