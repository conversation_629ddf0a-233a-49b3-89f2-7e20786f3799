<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use App\Modules\User\Model\UserModel;
use App\Handler\JsonResponseHandler;
use App\Type\JsonResponseType;

class DenyPegawaiAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // $XRoleIdHeader = $request->header('X-Role-Id');
        // $user = UserModel::with('roles')->find(Auth::user()->id);
        // $roles = $user->roles->toArray();

        // foreach ($roles as $role) {
        //     if (($role['id'] == $XRoleIdHeader) && ($role['id'] == 13)) {
        //         return JsonResponseHandler::denied();
        //     }
        // }
        
        return $next($request);
    }
}
