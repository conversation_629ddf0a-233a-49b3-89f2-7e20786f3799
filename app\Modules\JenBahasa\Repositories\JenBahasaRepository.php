<?php

namespace App\Modules\JenBahasa\Repositories;

use App\Modules\JenBahasa\Models\JenBahasaModel;

class JenBahasaRepository
{
    public static function datatable($per_page = 15)
    {
        $data =  JenBahasaModel::paginate($per_page);
        return $data;
    }
    public static function get($jeniskp_id)
    {
        $jeniskp = JenBahasaModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }
    public static function create($jeniskp)
    {
        $jeniskp = JenBahasaModel::create($jeniskp);
        return $jeniskp;
    }

    public static function update($jeniskp_id, $jeniskp)
    {
        JenBahasaModel::where('id', $jeniskp_id)->update($jeniskp);
        $jeniskp = JenBahasaModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }

    public static function delete($jeniskp_id)
    {
        $delete = JenBahasaModel::where('id', $jeniskp_id)->delete();
        return $delete;
    }
}
