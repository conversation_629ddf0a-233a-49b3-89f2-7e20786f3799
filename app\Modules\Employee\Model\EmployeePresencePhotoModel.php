<?php

namespace App\Modules\Employee\Model;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Schema;
use App\Traits\PdmTrait;
use App\Modules\Employee\Model\EmployeePresencePhotoTempModel;
use App\Modules\Employee\Model\EmployeePresencePhotoVerificatorModel;
use App\Modules\Employee\Model\EmployeePresencePhotoTempFieldModel;

class EmployeePresencePhotoModel extends Model
{
    // use SoftDeletes;
    use PdmTrait;
    protected $table = 'presence_photo';
    protected $guarded = [];
    protected $primaryKey = 'id_pegawai';
    protected $pdmRiwayatKey = 'id_pegawai';
    protected $pdmClass = EmployeePresencePhotoTempModel::class;
    protected $pdmVerificatorClass = EmployeePresencePhotoVerificatorModel::class;
    protected $pdmFieldClass = EmployeePresencePhotoTempFieldModel::class;
    public $timestamps = true;
    public $allowedPdmFields = [
        'id_pegawai',
        'mime',
        'status',
        'verified_at',
        'user_id',
        'role_id',
        'updated_at'
    ];
}