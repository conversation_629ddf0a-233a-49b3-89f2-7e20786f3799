<?php
namespace App\Modules\Eselon;

use App\Modules\Eselon\Controllers\EselonController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/eselon')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [EselonController::class, 'index']);
    Route::get('/datatable', [EselonController::class, 'datatable']);
    Route::get('/create', [EselonController::class, 'create'])->middleware('authorize:create-eselon');
    Route::post('/', [EselonController::class, 'store'])->middleware('authorize:create-eselon');
    Route::get('/{eselon_id}', [EselonController::class, 'show'])->middleware('authorize:read-eselon');
    Route::get('/{eselon_id}/detail', [EselonController::class, 'detail'])->middleware('authorize:read-eselon');
    Route::get('/{eselon_id}/edit', [EselonController::class, 'edit'])->middleware('authorize:update-eselon');
    Route::put('/{eselon_id}', [EselonController::class, 'update'])->middleware('authorize:update-eselon');
    Route::patch('/{eselon_id}', [EselonController::class, 'update'])->middleware('authorize:update-eselon');
    Route::delete('/{eselon_id}', [EselonController::class, 'destroy'])->middleware('authorize:delete-eselon');
});