<?php
namespace App\Modules\JenKP;

use App\Modules\JenKP\Controllers\JenKPController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenkp')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenKPController::class, 'index']);
    Route::get('/datatable', [JenKPController::class, 'datatable']);
    // Route::get('/create', [JenKeduduPegController::class, 'create'])->middleware('authorize:create-jenkedudupeg');
    // Route::post('/', [JenKeduduPegController::class, 'store'])->middleware('authorize:create-jenkedudupeg');
    // Route::get('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'show'])->middleware('authorize:read-jenkedudupeg');
    // Route::get('/{jenkedudupeg_id}/detail', [JenKeduduPegController::class, 'detail'])->middleware('authorize:read-jenkedudupeg');
    // Route::get('/{jenkedudupeg_id}/edit', [JenKeduduPegController::class, 'edit'])->middleware('authorize:update-jenkedudupeg');
    // Route::put('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'update'])->middleware('authorize:update-jenkedudupeg');
    // Route::patch('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'update'])->middleware('authorize:update-jenkedudupeg');
    // Route::delete('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'destroy'])->middleware('authorize:delete-jenkedudupeg');
});