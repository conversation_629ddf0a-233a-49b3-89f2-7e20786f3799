<?php

namespace App\Modules\ChildStatus\Repositories;

use App\Modules\ChildStatus\Models\ChildStatus;

class ChildStatusRepository
{
    public static function datatable($per_page = 15)
    {
        $data = ChildStatus::paginate($per_page);
        return $data;
    }
    public static function get($child_status_id)
    {
        $child_status = ChildStatus::where('id', $child_status_id)->first();
        return $child_status;
    }
    public static function create($child_status)
    {
        $child_status = ChildStatus::create($child_status);
        return $child_status;
    }

    public static function update($child_status_id, $child_status)
    {
        ChildStatus::where('id', $child_status_id)->update($child_status);
        $child_status = ChildStatus::where('id', $child_status_id)->first();
        return $child_status;
    }

    public static function delete($child_status_id)
    {
        $delete = ChildStatus::where('id', $child_status_id)->delete();
        return $delete;
    }
}
