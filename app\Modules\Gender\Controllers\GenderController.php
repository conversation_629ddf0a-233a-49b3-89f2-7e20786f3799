<?php

namespace App\Modules\Gender\Controllers;

use Illuminate\Http\Request;
use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Gender\Models\Gender;
use App\Modules\Gender\Requests\GenderCreateRequest;
use App\Modules\Gender\Repositories\GenderRepository;
use App\Modules\Permission\Repositories\PermissionRepository;

class GenderController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('Gender::index', ['permissions' => $permissions]);
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = GenderRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function get()
    {
        $data = Gender::get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $gender_id)
    {
        $gender = Gender::where('id', $gender_id)->first();
        return JsonResponseHandler::setResult($gender)->send();
    }

    public function create()
    {
        return view('Gender::create');
    }

    public function store(GenderCreateRequest $request)
    {
        $payload = $request->all();
        $gender = GenderRepository::create($payload);
        return JsonResponseHandler::setResult($gender)->send();
    }

    public function show(Request $request, $id)
    {
        $gender = GenderRepository::get($id);
        return JsonResponseHandler::setResult($gender)->send();
    }

    public function edit($id)
    {
        return view('Gender::edit', ['gender_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $gender = GenderRepository::update($id, $payload);
        return JsonResponseHandler::setResult($gender)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = GenderRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
