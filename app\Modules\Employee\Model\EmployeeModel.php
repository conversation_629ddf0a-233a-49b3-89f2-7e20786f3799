<?php

namespace App\Modules\Employee\Model;

use App\Handler\ModelSearchHandler;
use App\Handler\FileHandler;
use App\Modules\JenKeduduPeg\Models\JenKeduduPeg;
use App\Modules\JenKePeg\Models\JenKePeg;
use App\Modules\RiwayatJabatan\Models\RiwayatJabatanModel;
use App\Modules\InstansiInduk\Models\InstansiInduk;
use App\Modules\Skpd\Models\Skpd;
use App\Modules\Agama\Models\Agama;
use App\Modules\Efile\Repositories\EfileRepository;
use App\Modules\Gender\Models\Gender;
use App\Modules\KeluargaAnak\Models\KeluargaAnakModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiModel;
use App\Modules\StsPeg\Models\StsPeg;
use App\Modules\PerubahanData\Models\PerubahanData;
use App\Modules\RiwayatCpns\Models\RiwayatCpnsModel;
use App\Modules\RiwayatKepangkatan\Models\RiwayatKepangkatanModel;
use App\Modules\RiwayatKgb\Models\RiwayatKgbModel;
use App\Modules\RiwayatPendidikan\Models\RiwayatPendidikanModel;
use App\Modules\RiwayatPns\Models\RiwayatPnsModel;
use App\Modules\RiwayatPpk\Models\RiwayatPpkModel;
use App\Modules\StsKawin\Models\StsKawin;
use App\Modules\User\Model\UserModel;
use App\Traits\PdmTrait;
use App\Traits\UnitKerjaFilterTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use Exception;

class EmployeeModel extends Model
{
    use SoftDeletes;
    use UnitKerjaFilterTrait;
    use PdmTrait;
    use \Awobaz\Compoships\Compoships;

    protected $guarded = [];

    protected $table = 'tb_01';
    protected $pdmClass = PerubahanData::class;
    protected $pdmRiwayatKey = 'id_pegawai';
    protected $pdmVerificatorClass = EmployeeSubmissionVerificatorModel::class;
    protected $pdmFieldClass = EmployeeSubmissionFieldModel::class;
    public $allowedPdmFields = [
        // 'id_bkn',
        // 'nip',
        // 'nip_lama',  
        'nama',
        // 'nama_ijazah',
        // 'gelar_depan',
        // 'gelar_belakang',
        'tempat_lahir',
        // 'tanggal_lahir',
        'jenis_kelamin',
        'golongan_darah',
        'agama',
        // 'status_pegawai',
        // 'jenis_pegawai',
        // 'kedudukan_pegawai',
        // 'id_instansi_induk',
        // 'id_instansi_kerja',
        // 'id_unit_kerja',
        // 'id_induk_upt',
        // 'id_sub_unit',
        // 'id_sub_sub_unit',
        // 'id_sub_sub_sub_unit',
        'email',
        'email_instansi',
        'status_pernikahan',
        'nomor_kartu_asn',
        'nomor_karis_karsu',
        'domisili_alamat',
        'domisili_rt',
        'domisili_rw',
        'domisili_kode_pos',
        'domisili_provinsi',
        'domisili_kabupaten',
        'domisili_kecamatan',
        'domisili_kelurahan',
        'domisili_long',
        'domisili_lat',
        'kantor_kppn',
        'ktp_alamat',
        'ktp_rt',
        'ktp_rw',
        'ktp_kode_pos',
        'ktp_provinsi',
        'ktp_kabupaten',
        'ktp_kecamatan',
        'ktp_kelurahan',
        'ktp_long',
        'ktp_lat',
        'nomor_hp',
        'nomor_telepon',
        'emergensi_nomor',
        'emergensi_nama',
        'emergensi_hubungan',
        'jenis_sosial_media',
        'username_sosial_media',
        'nomor_kk',
        'nik',
        'nomor_akta_lahir',
        'tanggal_akta_lahir',
        'nomor_bpjs',
        'nomor_npwp',
        'nomor_taspen',
        'nomor_bapertarum',
        'tinggi',
        'berat',
        'rambut',
        'muka',
        'kulit',
        'ciri',
        'cacat',
        'hobby1',
        'hobby2',
        'hobby3',
        'nik_efile',
        'nomor_akta_lahir_efile',
        'nomor_bpjs_efile',
        // 'nomor_karis_karsu_efile',
        'nomor_kartu_asn_efile',
        'nomor_kk_efile',
        'nomor_npwp_efile',
        'nomor_taspen_efile',
        // 'status_pernikahan_efile',
        'skumptk_efile',
        'surat_tidak_pindah_efile',
        'kantor_kppn'
    ];

    public $allowedNonPdmFields = [
        'nip', 
        'nip_lama', 
        'nama_ijazah',
        'tanggal_lahir'
    ];

    public $hidePppkParuhWaktuPdmFields = [
        'surat_tidak_pindah_efile',
        'skumptk_efile',
        'nomor_kartu_asn',
        'nomor_kartu_asn_efile',
        'surat_tidak_pindah_efile',
        'nomor_bapertarum',
        'nomor_taspen',
        'nomor_taspen_efile'
    ];

    protected $keyType = 'string';
    protected $casts = ['status_pegawai' => 'integer'];

    const TASPEN_EFILE_DIR = 'submission/taspen';
    const AKTA_EFILE_DIR    = 'submission/aktaLahir';
    const KK_EFILE_DIR  = 'submission/kartuKeluarga';
    const NIK_EFILE_DIR = 'submission/nomorIndukKependudukan';
    const NPWP_EFILE_DIR    = 'submission/npwp';
    const KARISU_EFILE_DIR  = 'submission/karisu';
    const BPJS_EFILE_DIR    = 'submission/bpjs';
    const KARPEG_EFILE_DIR  = 'submission/karpeg';
    const NIKAH_EFILE_DIR   = 'submission/statusNikah';
    const NIPLAMA_EFILE_DIR = 'submission/nipLama';
    const SKUMPTK_EFILE_DIR = 'submission/skumptk';
    const SURAT_TIDAK_PINDAH_EFILE_DIR = 'submission/suratTidakPindah';

    const EFILE_CODE_MAPPING = [
        '44_1' => 'nik_efile',
        '04_12' => 'nomor_kartu_asn_efile',
        // '11' => 'nomor_karis_karsu_efile',
        '35_1' => 'nomor_bpjs_efile',
        '44_2' => 'nomor_kk_efile',
        '23_1' => 'nomor_akta_lahir_efile',
        // '10' => 'status_pernikahan_efile',
        '36' => 'nomor_npwp_efile',
        '12' => 'nomor_taspen_efile',
        '49' => 'skumptk_efile',
        '50' => 'surat_tidak_pindah_efile',
    ];

    const REQUIRED_IMPORT_COLUMNS = [
        'nip', 
        'nama', 
        'email', 
        'id_sub_sub_unit', 
        'status_pegawai'
    ];

    protected static function booted()
    {
        static::updated(function ($employee) {
            Log::debug("masuk updated employee. Current Employee NIP:".$employee->nip);
            UserModel::where('id_pegawai', $employee->id)->update(['email' => $employee->email_instansi]);
        });
    }

    // Scope
    public function scopeSearch($query, $keyword)
    {
        $searchable = ['id', 'nama', 'nip'];
        return ModelSearchHandler::handle($query, $searchable, $keyword);
    }
    // Scope
    public function scopeActive($query)
    {
        return $query
            ->whereNotIn('kedudukan_pegawai', [99, 21]);
        // ->whereNotIn('tb_01.id_unit_kerja', [98, 99]);
    }
    public function scopeCpns($query)
    {
        return $query->active()->where('status_pegawai', 1);
    }
    public function scopePns($query)
    {
        return $query->active()->where('status_pegawai', 2);
    }
    public function scopePppk($query)
    {
        return $query->active()->where('status_pegawai', 3);
    }
    public function scopeGolongan($query, $golongan_id = null)
    {
        return $query->whereHas('riwayatPangkat', function ($query) use ($golongan_id) {
            // dd($golongan_id);
            $query->where('isakhir', 1)
                ->when(!empty($golongan_id), function ($query) use ($golongan_id) {
                    $query->where('id_kepangkatan', $golongan_id);
                });
        });
    }

    // Acessor
    // public function getPhotoUrlAttribute()
    // {
    //     return URL::to('/') . '/api/employee/' . $this->id . '/photo';
    // }
    public function getLokasiKerjaAttribute()
    {
        return $this->id_unit_kerja . $this->id_induk_upt . $this->id_sub_unit . $this->id_sub_unit . $this->id_sub_sub_sub_unit;
    }
    public function getCompleteNameAttribute()
    {
        $name = $this->nama;

        if (!empty($this->gelar_depan)) {
            $name = $this->gelar_depan . " " . $name;
        }

        if (!empty($this->gelar_belakang)) {
            $name = $name . " " . $this->gelar_belakang;
        }

        return $name;
    }
    public function getTmtPegawaiAttribute()
    {
        if ($this->status_pegawai == 1) {
            return $this->last_cpns->tmt_cpns;
        }
        if ($this->status_pegawai == 2) {
            return $this->last_pns->tmt_pns;
        }
        if ($this->status_pegawai == 3) {
            return $this->last_pppk->tmt_pppk;
        }
        return null;
    }

    public function getLastPnsAttribute()
    {
        return $this->riwayatPns()->orderBy('tmt_pns', 'DESC')->first();
    }
    public function getLastCpnsAttribute()
    {
        return $this->riwayatCpns()->orderBy('tmt_cpns', 'DESC')->first();
    }
    public function getLastPppkAttribute()
    {
        return $this->riwayatPppk()->where('isakhir', 1)->first();
    }
    public function getLastPangkatAttribute()
    {
        return $this->riwayatPangkat()->where('isakhir', 1)->orderBy('tmt_sk', 'DESC')->first();
    }
    public function getLastKgbAttribute()
    {
        return $this->riwayatKgb()->where('isakhir', 1)->orderBy('tmt_sk', 'DESC')->first();
    }
    public function getChildHaveAllowanceCountAttribute()
    {
        return $this->childs()->where('status_tunjangan_final', 1)->count();
    }
    public function getLastEducationAttribute()
    {
        return $this->riwayatPendidikan()->where('isakhir', 1)->orderBy('tahun_lulus', 'DESC')->first();
    }
    public function getLastSubmissionAttribute()
    {
        return $this->submissions()->where('status', 1)->orderBy('id', 'DESC')->first();
    }


    // Relation
    public function riwayatJabatan()
    {
        return $this->hasMany(RiwayatJabatanModel::class, 'id_pegawai', 'id');
    }
    public function lastJabatan()
    {
        return $this->hasOne(RiwayatJabatanModel::class, 'id_pegawai', 'id')
            ->where('isakhir', 1)
            ->orderBy('tmt_jabatan', 'desc');
    }
    public function riwayatPangkat()
    {
        return $this->hasMany(RiwayatKepangkatanModel::class, 'id_pegawai', 'id');
    }
    public function lastKepangkatan()
    {
        return $this->hasOne(RiwayatKepangkatanModel::class, 'id_pegawai', 'id')
            ->where('isakhir', 1);
    }
    public function instansiInduk()
    {
        return $this->hasOne(InstansiInduk::class, 'id', 'id_instansi_induk');
    }
    public function unitKerja()
    {
        return $this->belongsTo(Skpd::class, 'id_unit_kerja', 'id_unit_kerja')
            ->where('id_induk_upt', '00')
            ->where('id_sub_unit', '00')
            ->where('id_sub_sub_unit', '00')
            ->where('id_sub_sub_sub_unit', '00');
    }
    public function subUnitKerja()
    {
        return $this->belongsTo(Skpd::class, ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit'], ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit'])
            ->where('id_sub_sub_unit', '00')
            ->where('id_sub_sub_sub_unit', '00');
    }
    public function subSubUnitKerja()
    {
        return $this->belongsTo(
                Skpd::class, 
                ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit'],
                ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit']
            )
            ->where('id_sub_sub_sub_unit', '00');
    }
    public function subSubSubUnitKerja()
    {
        return $this->belongsTo(Skpd::class, ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit', 'id_sub_sub_sub_unit'], ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit', 'id_sub_sub_sub_unit']);
    }
    public function pendudupeg()
    {
        return $this->belongsTo(JenKeduduPeg::class, 'kedudukan_pegawai', 'id');
    }
    public function jenkepeg()
    {
        return $this->belongsTo(JenKePeg::class, 'jenis_pegawai', 'id');
    }

    public function temporary()
    {
        return $this->belongsTo(PerubahanData::class, 'id', 'id_pegawai');
    }

    public function gender()
    {
        return $this->belongsTo(Gender::class, 'jenis_kelamin', 'id')->select('id', 'name');
    }

    // public function status_pegawai()
    // {
    //     return $this->belongsTo(StsPeg::class, 'stspeg', 'idstspeg')->select('idstspeg', 'stspeg');
    // }

    public function employeeStatus()
    {
        return $this->belongsTo(StsPeg::class, 'status_pegawai', 'id')
            ->select('id', 'name', 'nmsatpeg_taspen');
    }

    public function religion()
    {
        return $this->belongsTo(Agama::class, 'agama', 'id')->select('id', 'agama');
    }

    public function maritalStatus()
    {
        return $this->belongsTo(StsKawin::class, 'status_pernikahan', 'id')->select('id', 'stskawin');
    }

    public function jenisKepegawaian()
    {
        return $this->belongsTo(JenKeduduPeg::class, 'kedudukan_pegawai', 'id')->select('id', 'name');
    }

    public function riwayatPns()
    {
        return $this->hasMany(RiwayatPnsModel::class, 'id_pegawai', 'id');
    }
    public function riwayatCpns()
    {
        return $this->hasMany(RiwayatCpnsModel::class, 'id_pegawai', 'id');
    }
    public function riwayatPppk()
    {
        return $this->hasMany(RiwayatPpkModel::class, 'id_pegawai', 'id');
    }
    public function riwayatKgb()
    {
        return $this->hasMany(RiwayatKgbModel::class, 'id_pegawai', 'id');
    }
    public function childs()
    {
        return $this->hasMany(KeluargaAnakModel::class, 'id_pegawai', 'id');
    }
    public function riwayatPendidikan()
    {
        return $this->hasMany(RiwayatPendidikanModel::class, 'id_pegawai', 'id');
    }
    public function submissions()
    {
        return $this->hasMany(PerubahanData::class, 'id_pegawai', 'id');
    }


    // Function
    public function setAttribute($key, $value)
    {
        if ($value == '') {
            $value = '';
        }
        return parent::setAttribute($key, $value);
    }

    public function currentJabatan()
    {
        return $this->riwayatJabatan()
            ->where('isakhir', 1)
            ->orderBy('tmt_jabatan', 'DESC')
            ->first();
    }

    public function isSekda()
    {
        return
            $this->id_unit_kerja == 'A2' &&
            $this->id_induk_upt == '00' &&
            $this->id_sub_unit == '00' &&
            $this->id_sub_sub_unit == '00' &&
            $this->id_sub_sub_sub_unit == '00' &&
            (
                $this->currentJabatan() != null &&
                $this->currentJabatan()->id_jenis_jabatan == 1
            );
    }

    public function isSekdaEmployee()
    {
        return
            $this->id_unit_kerja == 'A2' &&
            (
                $this->currentJabatan() != null &&
                $this->currentJabatan()->id_jenis_jabatan != 1
            );
    }

    public function isAsisten()
    {
        return
            $this->id_unit_kerja == 'A2' &&
            (
                $this->currentJabatan() != null &&
                $this->currentJabatan()->id_jenis_jabatan == 1
            ) &&
            stripos($this->subSubSubUnitKerja->skpd, 'ASISTEN') == 0;
    }

    public function isBalaiUptSatkerCabdin()
    {
        if ($this->id_unit_kerja != 'D0' && $this->id_induk_upt != '00' && !empty($this->id_induk_upt)) {
            return true;
        }
        if ($this->id_unit_kerja == 'D0' && $this->id_induk_upt != '00' && !empty($this->id_induk_upt) && ($this->id_sub_sub_unit <= 30 && is_numeric($this->id_sub_sub_unit))) {
            return true;
        }

        return false;
    }

    public function isSekolah()
    {
        if ($this->id_unit_kerja == 'D0' && $this->id_induk_upt != '00' && !empty($this->id_induk_upt) && ($this->id_sub_sub_unit > 30 || !is_numeric($this->id_sub_sub_unit))) {
            return true;
        }
        return false;
    }

    public function getLastSubmissionIndexedField()
    {
        if (!empty($this->last_submission)) {
            return $this->last_submission->fields->mapWithKeys(function ($item) {
                return [$item->field => $item->toArray()];
            })->toArray();
        } else {
            return [];
        }
    }

    public function handleEfileUpdate($payload, $idJenisAksi)
    {
        try {
            $nip = $this->nip;
            $oldId = $this->old_id;

            $fileMappings = array_flip(self::EFILE_CODE_MAPPING);
            unset($payload['status_pernikahan_efile']);
            unset($payload['nomor_karis_karsu_efile']);

            Log::debug('EFILE PAYLOAD');
            Log::debug($payload);

            foreach ($payload as $key => $value) {
                $value = (string) $value;
                if (array_key_exists($key, $fileMappings)) {
                    $fileCode = $fileMappings[(string) $key];
                    (new EfileRepository())->storeFile(nip: $nip, code: $fileCode, filepath: $value, oldRiwayatId: $oldId);
                }
            }
            foreach ($payload as $key => $value) {
                FileHandler::delete($value);
            }
        } catch (Exception $err) {
            Log::error('An error occurred while handle update efile: ' . $err->getMessage());
            throw $err;
        }
    }
    public function getEfileByCode($fileCode)
    {
        try {
            $nip = $this->nip;
            $efiles = (new EfileRepository())
                ->getFileByNipAndCodes($nip, implode(",", [$fileCode]));

            if (!empty($efiles) && isset($efiles[0]['efile']) && !empty($efiles[0]['efile'])) {
                $efile = $efiles[0]['efile'][0];
                $efile['name'] = $efiles[0]['nama_jenis'];
                $efile['code'] = $efiles[0]['id_jenis'];
                return $efile;
            }
        } catch (Exception $err) {
            Log::error('An error occurred while fetching efile: ' . $err->getMessage());
        }
    }

    // public function getNomorTaspenEfileAttribute()
    // {
    //     return $this->getEfileByCode("12");
    // }

    // public function getNomorAktaLahirEfileAttribute()
    // {
    //     return $this->getEfileByCode("23_1");
    // }

    // public function getNomorKkEfileAttribute()
    // {
    //     return $this->getEfileByCode("44_2");
    // }

    // public function getNikEfileAttribute()
    // {
    //     return $this->getEfileByCode("44_1");
    // }

    // public function getNomorNpwpEfileAttribute()
    // {
    //     return $this->getEfileByCode("36");
    // }

    // public function getNomorKarisKarsuEfileAttribute()
    // {
    //     return $this->getEfileByCode("11");
    // }

    // public function getNomorBpjsEfileAttribute()
    // {
    //     return $this->getEfileByCode("35_1");
    // }

    // public function getNomorKartuAsnEfileAttribute()
    // {
    //     return $this->getEfileByCode("04_12");
    // }

    // public function getStatusPernikahanEfileAttribute()
    // {
    //     return $this->getEfileByCode("10");
    // }

    // public function getNipLamaEfileAttribute()
    // {
    //     return $this->getEfileByCode("04_11");
    // }

    public function withEfile()
    {
        $item = $this;

        $nip = $this->nip;

        $fileCode = array_keys(self::EFILE_CODE_MAPPING);
        $fileCode = array_map('strval', $fileCode);

        try {
            $efiles = (new EfileRepository())
                ->getFileByNipAndCodes($nip, implode(",", $fileCode));
            foreach ($efiles as $efile) {
                $key = EmployeeModel::EFILE_CODE_MAPPING[$efile['id_jenis']];

                if (!empty($efile['efile'])) {
                    $fileData = $efile['efile'][0];
                    $fileData['name'] = $efile['nama_jenis'];
                    $fileData['id_jenis'] = $efile['id_jenis'];
                    $item->{$key} = $fileData;
                } else {
                    $item->{$key} = null;
                }
            }
        } catch (Exception $err) {
            foreach ($fileCode as $code) {
                $key = EmployeeModel::EFILE_CODE_MAPPING[$code];
                $item->{$key} = null;
            }
        }

        return $item;
    }

}
