<?php

namespace App\Console\Commands;

use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Employee\Repositories\EmployeeRepository;
use App\Modules\EmployeeStatus\Repositories\EmployeeStatusRepository;
use App\Modules\ExecutiveSummary\Repositories\ValiditasRepository;
use Illuminate\Console\Command;

class GenerateEmployeePeriodicData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:employeePeriodicData';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        EmployeeModel::active()
            ->chunk(1000, function ($employees) {
                foreach ($employees as $employee) {
                    echo "Processing " . $employee->nama;
                    EmployeeRepository::generateEmployeePeriodicData($employee->id);
                }
            });

        EmployeeRepository::generateEmployeePeriodicData("5d297d8d-2821-11ef-8e1c-0242ac130006");
    }
}
