<!DOCTYPE html>
<html lang="en">
<head>
<style>
    body {
        font-family: Arial, sans-serif;
        font-size: 12pt;
    }

    .table-custom {
        width: 100%;
        border-collapse: collapse;
        font-size: 12px;
    }

    .table-custom th, .table-custom td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    .table-custom th {
        background-color: #f2f2f2;
        text-align: left;
    }
</style>
</head>

<body>
    <!-- kop -->
    <table border="0" cellpadding="0" align="center" width="100%">
        <tr valign="top">
            <td width='15%' valign="middle" align="center">
                <img src="{{ public_path('img/logo-ct-dark.png') }}" width="100">
            </td>
            <td width="80%" valign="top" align="center">
                <div style="font-weight: bold; font-size: 15pt; padding: 2px; letter-spacing: 2px;">
                    PEMERINTAH PROVINSI JAWA TENGAH
                </div>
                <div style="font-weight: bold; font-size: 13pt; padding-bottom: 2px; letter-spacing:2px;">
                    BADAN KEPEGAWAIAN DAERAH
                </div>
                <div style="font-size: 9pt;">
                    Jl. Stadion Selatan No.1, Karangkidul, Kec. Semarang Tengah, Telp. (024) 8415813<br>
                    Website : bkd.jatengprov.go.id, Email : <EMAIL><br>
                </div>
            </td>
        </tr>
    </table>
    <br><hr></hr>
    <!-- isi -->
    <br><h1 align="center" style="font-size: 10pt;"><b>BIODATA PEGAWAI</b></h1><br>
    <table width="100%">
        <tr><td colspan="4" bgcolor="lightblue"><div style="font-weight: bold; font-size: 14px;">LOKASI KERJA</div></td></tr>
        <tr>
            <td width="">UNIT KERJA</td>
            <td width="">:</td>
            <td width="">{{$item->unitKerja->path}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">SUB UNIT KERJA</td>
            <td width="">:</td>
            <td width="">{{$item->subunitKerja->skpd}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">SUB SUB UNIT KERJA</td>
            <td width="">:</td>
            <td width="">{{$item->subSubUnitKerja->skpd}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td colspan="4" bgcolor="lightblue">
                <div style="font-weight: bold; font-size: 14px;">IDENTITAS PEGAWAI</div>
            </td>
        </tr>
        <tr>
            <td width="250">NIP</td>
            <td width="5">:</td>
            <td width="">{{$item->nip}}</td>
            
            {{-- foto profile --}}
            <td width="150" rowspan="8">
                @if (!empty($profilePhoto))
                    <img src="data:{{ $profilePhoto['mime'] }};base64,{{ $profilePhoto['base64'] }}" width="130" height="170">
                @else
                    <img src="" width="130" height="170">
                @endif
            </td>
        </tr>
        <tr>
            <td width="">NAMA</td>
            <td width="">:</td>
            <td width="">{{$item->nama}}</td>
        </tr>
        <tr>
            <td width="">TEMPAT LAHIR</td>
            <td width="">:</td>
            <td width="">{{$item->tempat_lahir}}</td>
        </tr>
        <tr>
            <td width="">TANGGAL LAHIR</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->tanggal_lahir))}}</td>
        </tr>
        <tr>
            <td width="">JENIS KELAMIN</td>
            <td width="">:</td>
            <td width="">{{$item->gender->name}}</td>
        </tr>
        <tr>
            <td width="">AGAMA</td>
            <td width="">:</td>
            <td width="">{{$item->religion->agama}}</td>
        </tr>
        <tr>
            <td width="">STATUS PEGAWAI</td>
            <td width="">:</td>
            <td width="">{{$item->status_pegawai}}</td>
        </tr>
        <tr>
            <td width="">JENIS KEPEGAWAIAN</td>
            <td width="">:</td>
            <td width="">{{$item->jenkepeg->jenkepeg}}</td>
        </tr>
        <tr>
            <td width="">STATUS PERKAWINAN</td>
            <td width="">:</td>
            <td width="">{{$item->maritalStatus->stskawin}}</td>
        </tr>
        <tr>
            <td width="">KEDUDUKAN PEGAWAI</td>
            <td width="">:</td>
            <td width="">{{$item->pendudupeg->name}}</td>
        </tr>
        <tr>
            <td width="">ALAMAT</td>
            <td width="">:</td>
            <td width="">
                {{$item->ktp_alamat}}
            </td>
            <td>&nbsp;</td>
        </tr>
        <tr><td colspan="4">&nbsp;</td></tr>
        <tr>
            <td width="">TELEPON</td>
            <td width="">:</td>
            <td width="">{{$item->nomor_hp}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">NO KARTU ASN</td>
            <td width="">:</td>
            <td width="">{{$item->nomor_kartu_asn}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">NO BPJS</td>
            <td width="">:</td>
            <td width="">{{$item->nomor_bpjs}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">KARTU TASPEN</td>
            <td width="">:</td>
            <td width="">{{$item->nomor_taspen}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">KARTU KARIS/KARSU</td>
            <td width="">:</td>
            <td width="">{{$item->nokaris}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">NPWP</td>
            <td width="">:</td>
            <td width="">{{$item->nomor_npwp}}</td>
            <td>&nbsp;</td>
        </tr>

        <tr><td colspan="4" bgcolor="lightblue"><div style="font-weight: bold; font-size: 14px;">PENGANGKATAN SEBAGAI CPNS</div></td></tr>
        <tr>
            <td width="">NO SK CPNS</td>
            <td width="">:</td>
            <td width="">{{$item->cpns->nomor_sk_cpns}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TANGGAL SK CPNS</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->cpns->tanggal_sk_cpns))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">PANGKAT CPNS</td>
            <td width="">:</td>
            <td width="">{{$item->cpns->golruCpns->name." ".$item->cpns->golruCpns->pangkat}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TMT CPNS</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->cpns->tmt_cpns))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">MASA KERJA</td>
            <td width="">:</td>
            <td width="">{{$item->cpns->masa_kerja_tahun." tahun ".$item->cpns->masa_kerja_bulan." bulan"}}</td>
            <td>&nbsp;</td>
        </tr>

        <tr><td colspan="4" bgcolor="lightblue"><div style="font-weight: bold; font-size: 14px;">PENGANGKATAN SEBAGAI PNS</div></td></tr>
        <tr>
            <td width="">NO SK PNS</td>
            <td width="">:</td>
            <td width="">{{$item->pns->nomor_sk_pns}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TANGGAL SK PNS</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->pns->tanggal_sk_pns))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">PANGKAT PNS</td>
            <td width="">:</td>
            <td width="">{{$item->pns->golruPns->name." ".$item->pns->golruPns->pangkat}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TMT PNS</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->pns->tmt_pns))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">MASA KERJA</td>
            <td width="">:</td>
            <td width="">{{$item->pns->masa_kerja_tahun." tahun ".$item->pns->masa_kerja_bulan." bulan"}}</td>
            <td>&nbsp;</td>
        </tr>

        <tr><td colspan="4" bgcolor="lightblue"><div style="font-weight: bold; font-size: 14px;">KENAIKAN PANGKAT TERAKHIR</div></td></tr>
        <tr>
            <td width="">NO SK</td>
            <td width="">:</td>
            <td width="">{{$item->kepangkatan->nomor_sk}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TANGGAL SK</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->kepangkatan->tanggal_sk))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">PANGKAT</td>
            <td width="">:</td>
            <td width="">{{$item->kepangkatan->golru->name." ".$item->kepangkatan->golru->pangkat}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TMT</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->kepangkatan->tmt_sk))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">MASA KERJA</td>
            <td width="">:</td>
            <td width="">{{$item->kepangkatan->masa_kerja_tahun." tahun ".$item->kepangkatan->masa_kerja_bulan." bulan"}}</td>
            <td>&nbsp;</td>
        </tr>

        <tr><td colspan="4" bgcolor="lightblue"><div style="font-weight: bold; font-size: 14px;">KENAIKAN GAJI BERKALA (KGB) TERAKHIR</div></td></tr>
        <tr>    
            <td width="">TAHUN KGB</td>
            <td width="">:</td>
            <td width="">{{$item->kgbTerakhir->tahun_kgb}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">NO SK</td>
            <td width="">:</td>
            <td width="">{{$item->kgbTerakhir->nomor_sk}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TANGGAL SK</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->kgbTerakhir->tanggal_sk))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TMT</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->kgbTerakhir->tmt_sk))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">MASA KERJA</td>
            <td width="">:</td>
            <td width="">{{$item->kgbTerakhir->masa_kerja_concated}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">GAJI POKOK</td>
            <td width="">:</td>
            <td width="">Rp {{number_format($item->kgbTerakhir->gaji, 0, ',', '.')}}</td>
            <td>&nbsp;</td>
        </tr>

        <tr><td colspan="4" bgcolor="lightblue"><div style="font-weight: bold; font-size: 14px;">PENDIDIKAN UMUM TERAKHIR</div></td></tr>
        <tr>
            <td width="">NO IJAZAH</td>
            <td width="">:</td>
            <td width="">{{$item->pendidikanTerakhir->ijazah_nomor}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TANGGAL IJAZAH</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->pendidikanTerakhir->ijazah_tanggal))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TINGKAT PENDIDIKAN</td>
            <td width="">:</td>
            <td width="">{{$item->pendidikanTerakhir->jenjang}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">JURUSAN PENDIDIKAN</td>
            <td width="">:</td>
            <td width="">{{$item->pendidikanTerakhir->jurusan}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">SEKOLAH / UNIVERSITAS</td>
            <td width="">:</td>
            <td width="">{{$item->pendidikanTerakhir->nama_sekolah}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TEMPAT</td>
            <td width="">:</td>
            <td width="">{{$item->pendidikanTerakhir->lokasi_sekolah}}</td>
            <td>&nbsp;</td>
        </tr>

        <tr><td colspan="4" bgcolor="lightblue"><div style="font-weight: bold; font-size: 14px;">JABATAN TERAKHIR</div></td></tr>
        <tr>
            <td width="">NO SK</td>
            <td width="">:</td>
            <td width="">{{$item->jabatanTerakhir->nomor_sk}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TANGGAL SK</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->jabatanTerakhir->tanggal_sk))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">TMT</td>
            <td width="">:</td>
            <td width="">{{date('d-m-Y', strtotime($item->jabatanTerakhir->tmt_jabatan))}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">ESELON</td>
            <td width="">:</td>
            <td width="">{{$item->jabatanTerakhir->eselon}}</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td width="">NAMA JABATAN</td>
            <td width="">:</td>
            <td width="">{{$item->jabatanTerakhir->jabatan}}</td>
            <td>&nbsp;</td>
        </tr>
    </table>
    <br><h1 align="center" style="font-size: 10pt;"><b>DAFTAR RIWAYAT</b></h1><br>
    <b>A. Riwayat Jabatan</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">INSTANSI INDUK</div></th>
            <th><div class="text-center">NAMA JABATAN</div></th>
            <th><div class="text-center">NOMOR SK</div></th>
            <th width="15%"><div class="text-center">TANGGAL SK</div></th>
            <th width="15%"><div class="text-center">TMT JABATAN</div></th>
            <th><div class="text-center">PAK</div></th>
            <th><div class="text-center">UNIT KERJA</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($riwayatJabatan) > 0)
                @foreach($riwayatJabatan as $key => $rjab)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$rjab->instansi_induk}}</td>
                    <td>{{$rjab->jabatan_detail->jabatan ?? $rjab->jabatan}}</td>
                    <td>{{$rjab->nomor_sk}}</td>
                    <td>{{$rjab->tanggal_sk}}</td>
                    <td>{{$rjab->tmt_jabatan}}</td>
                    <td>{{$rjab->id_pak}}</td>
                    <td>{{$rjab->skpd}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>B. Riwayat Pangkat</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">GOLONGAN RUANG</div></th>
            <th><div class="text-center">DITETAPKAN OLEH</div></th>
            <th><div class="text-center">NOMOR SK</div></th>
            <th><div class="text-center">TANGGAL SK</div></th>
            <th><div class="text-center">TMT SK</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($riwayatPangkat) > 0)
                @foreach($riwayatPangkat as $key => $rpangkat)
                    <td>{{$key + 1}}</td>
                    <td>{{@$rpangkat->golru->name}} {{@$rpangkat->golru->pangkat}}</td>
                    <td>{{@$rpangkat->penetap->jabatan}}</td>
                    <td>{{$rpangkat->nomor_sk}}</td>
                    <td>{{$rpangkat->tanggal_sk}}</td>
                    <td>{{$rpangkat->tmt_sk}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>C. Riwayat Kenaikan Gaji Berkala</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NOMOR SK</div></th>
            <th><div class="text-center">TANGGAL SK</div></th>
            <th><div class="text-center">TMT</div></th>
            <th><div class="text-center">MASA KERJA</div></th>
            <th><div class="text-center">GAJI</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($riwayatKgb) > 0)
                @foreach($riwayatKgb as $key => $rkgb)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$rkgb->nomor_sk}}</td>
                    <td>{{$rkgb->tanggal_sk}}</td>
                    <td>{{$rkgb->tmt_sk}}</td>
                    <td>{{$rkgb->masa_kerja_tahun." tahun ".$rkgb->masa_kerja_bulan." bulan"}}</td>
                    <td>{{$rkgb->gaji}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>D. Riwayat Pendidikan Formal</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">TINGKAT</div></th>
            <th><div class="text-center">JURUSAN</div></th>
            <th><div class="text-center">NAMA SEKOLAH</div></th>
            <th><div class="text-center">NOMOR IJAZAH</div></th>
            <th><div class="text-center">TANGGAL IJAZAH</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($riwayatPendidikanFormal) > 0)
                @foreach($riwayatPendidikanFormal as $key => $rpendidikan_formal)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$rpendidikan_formal->jenjang}}</td>
                    <td>{{$rpendidikan_formal->jurusan}}</td>
                    <td>{{$rpendidikan_formal->nama_sekolah}}</td>
                    <td>{{$rpendidikan_formal->ijazah_nomor}}</td>
                    <td>{{$rpendidikan_formal->ijazah_tanggal}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>E. Riwayat Pendidikan Non Formal</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA DIKLAT</div></th>
            <th><div class="text-center">TEMPAT DIKLAT</div></th>
            <th><div class="text-center">PENYELENGGARA</div></th>
            <th><div class="text-center">TANGGAL MULAI</div></th>
            <th><div class="text-center">TANGGAL SELESAI</div></th>
            <th><div class="text-center">NOMOR SERTIFIKAT</div></th>
            <th><div class="text-center">TANGGAL SERTIFIKAT</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($riwayatPendidikanNonFormal) > 0)
                @foreach($riwayatPendidikanNonFormal as $key => $rpendidikan_non_formal)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$rpendidikan_non_formal->nama_diklat}}</td>
                    <td>{{$rpendidikan_non_formal->tempat_diklat}}</td>
                    <td>{{$rpendidikan_non_formal->penyelenggara}}</td>
                    <td>{{$rpendidikan_non_formal->tanggal_mulai}}</td>
                    <td>{{$rpendidikan_non_formal->tanggal_selesai}}</td>
                    <td>{{$rpendidikan_non_formal->nomor_sertifikat}}</td>
                    <td>{{$rpendidikan_non_formal->tanggal_sertifikat}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>F. Tanda Jasa</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">TANDA JASA</div></th>
            <th><div class="text-center">JENIS</div></th>
            <th><div class="text-center">TAHUN</div></th>
            <th><div class="text-center">NOMOR SK</div></th>
            <th><div class="text-center">TANGGAL SK</div></th>
        </tr>
        </thead>
        <tbody>
            <?php $n  = 0;?>
            @if(count($rtandajasa) > 0)
                @foreach($rtandajasa as $rtandajasa)
                <?php $n++; ?>
                <tr>
                    <td>{{$n}}</td>
                    <td>{{$rtandajasa->tanda_jasa}}</td>
                    <td>{{$rtandajasa->jenis}}</td>
                    <td>{{$rtandajasa->tahun}}</td>
                    <td>{{$rtandajasa->nomor_sk}}</td>
                    <td>{{$rtandajasa->tanggal_sk}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>G. Penguasaan Bahasa</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA BAHASA</div></th>
            <th><div class="text-center">JENIS BAHASA</div></th>
            <th><div class="text-center">KEMAMPUAN</div></th>
        </tr>
        </thead>
        <tbody>
            <?php $n  = 0;?>
            @if(count($rbahasa) > 0)
                @foreach($rbahasa as $rbahasa)
                <?php $n++; ?>
                <tr>
                    <td>{{$n}}</td>
                    <td>{{$rbahasa->nama_bahasa}}</td>
                    <td>{{$rbahasa->jenis_bahasa}}</td>
                    <td>{{$rbahasa->kemampuan}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>H. Hukuman Disiplin</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">JENIS HUKUM DISIPLIN</div></th>
            <th><div class="text-center">TINGKAT</div></th>
            <th><div class="text-center">PEJABAT</div></th>
            <th><div class="text-center">NOMOR SK</div></th>
            <th><div class="text-center">TANGGAL MULAI</div></th>
            <th><div class="text-center">TANGGAL SELESAI</div></th>
        </tr>
        </thead>
        <tbody>
            <?php $n  = 0;?>
            @if(count($rhukuman_disiplin) > 0)
                @foreach($rhukuman_disiplin as $rhukuman_disiplin)
                <?php $n++; ?>
                <tr>
                    <td>{{$n}}</td>
                    <td>{{$rhukuman_disiplin->jenisHukum->jenhukum}}</td>
                    <td>{{$rhukuman_disiplin->tingkat->kathukdis}}</td>
                    <td>{{$rhukuman_disiplin->id_penetap}}</td>
                    <td>{{$rhukuman_disiplin->nomor_sk}}</td>
                    <td>{{$rhukuman_disiplin->tanggal_mulai}}</td>
                    <td>{{$rhukuman_disiplin->tanggal_selesai}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>I. Angka Kredit</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA JABATAN</div></th>
            <th><div class="text-center">NOMOR SK</div></th>
            <th><div class="text-center">TANGGAL SK</div></th>
            <th><div class="text-center">TANGGAL MULAI</div></th>
            <th><div class="text-center">TANGGAL SELESAI</div></th>
            <th><div class="text-center">KREDIT UTAMA BARU</div></th>
            <th><div class="text-center">KREDIT PENUNJANG BARU</div></th>
            <th><div class="text-center">KREDIT BARU TOTAL</div></th>
        </tr>
        </thead>
        <tbody>
            <?php $n  = 0;?>
            @if(count($rangka_kredit) > 0)
                @foreach($rangka_kredit as $rangka_kredit)
                <?php $n++; ?>
                <tr>
                    <td>{{$n}}</td>
                    <td>{{$rangka_kredit->jabatan}}</td>
                    <td>{{$rangka_kredit->nomor_sk}}</td>
                    <td>{{$rangka_kredit->tanggal_sk}}</td>
                    <td>{{$rangka_kredit->tanggal_mulai}}</td>
                    <td>{{$rangka_kredit->tanggal_selesai}}</td>
                    <td>{{$rangka_kredit->angka_kredit_utama}}</td>
                    <td>{{$rangka_kredit->angka_kredit_penunjang}}</td>
                    <td>{{$rangka_kredit->nilai_angka_kredit}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>J. DATA ANAK</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA</div></th>
            <th><div class="text-center">TEMPAT LAHIR</div></th>
            <th><div class="text-center">TANGGAL LAHIR</div></th>
            <th><div class="text-center">STATUS ANAK</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($riwayatAnak) > 0)
                @foreach($riwayatAnak as $key => $ranak)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$ranak->nama_anak}}</td>
                    <td>{{$ranak->tempat_lahir}}</td>
                    <td>{{$ranak->tanggal_lahir}}</td>
                    <td>{{$ranak->status_anak_name}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>K. DATA ISTRI/SUAMI</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA</div></th>
            <th><div class="text-center">TEMPAT LAHIR</div></th>
            <th><div class="text-center">TANGGAL LAHIR</div></th>
            <th><div class="text-center">NOMOR AKTA NIKAH</div></th>
            <th><div class="text-center">TANGGAL NIKAH</div></th>
            <th><div class="text-center">STATUS PASANGAN</div></th>
            <th><div class="text-center">NIP/NRP</div></th>
            <th><div class="text-center">PEKERJAAN</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($riwayatPasangan) > 0)
                @foreach($riwayatPasangan as $key => $rpasangan)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$rpasangan->nama_pasangan}}</td>
                    <td>{{$rpasangan->tempat_lahir}}</td>
                    <td>{{$rpasangan->tanggal_lahir}}</td>
                    <td>{{$rpasangan->nikah_nomor}}</td>
                    <td>{{$rpasangan->nikah_tanggal}}</td>
                    <td>{{$rpasangan->status_perkawinan_name}}</td>
                    <td>{{$rpasangan->pekerjaan_nip}}</td>
                    <td>{{$rpasangan->pekerjaan_name}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>L. DATA KERABAT</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA</div></th>
            <th><div class="text-center">STATUS KERABAT</div></th>
            <th><div class="text-center">TEMPAT LAHIR</div></th>
            <th><div class="text-center">TANGGAL LAHIR</div></th>
            <th><div class="text-center">PEKERJAAN</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($rkerabat) > 0)
                @foreach($rkerabat as $key => $kerabat)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$kerabat->nama_kekerabatan}}</td>
                    <td>{{$kerabat->kerabat?->hubungan}}</td>
                    <td>{{$kerabat->tempat_lahir}}</td>
                    <td>{{$kerabat->tanggal_lahir}}</td>
                    <td>{{$kerabat->pekerjaanDetail?->pekerjaan}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>M. DATA ORANG TUA</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA ORANG TUA</div></th>
            <th><div class="text-center">STATUS</div></th>
            <th><div class="text-center">TEMPAT LAHIR</div></th>
            <th><div class="text-center">TANGGAL LAHIR</div></th>
            <th><div class="text-center">ALAMAT</div></th>
            <th><div class="text-center">PEKERJAAN</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($rortu) > 0)
                @foreach($rortu as $key => $ortu)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$ortu->nama_orangtua}}</td>
                    <td>{{$ortu->status_orangtua_name}}</td>
                    <td>{{$ortu->tempat_lahir}}</td>
                    <td>{{$ortu->tanggal_lahir}}</td>
                    <td>{{$ortu->alamat}}</td>
                    <td>{{$ortu->pekerjaanDetail?->pekerjaan}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>

    <b>N. DATA MERTUA</b>
    <table border="1" class="table-custom">
        <thead>
        <tr>
            <th width="2%"><div class="text-center">NO</div></th>
            <th><div class="text-center">NAMA MERTUA</div></th>
            <th><div class="text-center">STATUS</div></th>
            <th><div class="text-center">MERTUA DARI PASANGAN</div></th>
            <th><div class="text-center">TEMPAT LAHIR</div></th>
            <th><div class="text-center">TANGGAL LAHIR</div></th>
            <th><div class="text-center">ALAMAT</div></th>
            <th><div class="text-center">PEKERJAAN</div></th>
        </tr>
        </thead>
        <tbody>
            @if(count($rmertua) > 0)
                @foreach($rmertua as $key => $mertua)
                <tr>
                    <td>{{$key + 1}}</td>
                    <td>{{$mertua->nama_mertua}}</td>
                    <td>{{$mertua->status_mertua_name}}</td>
                    <td>{{$mertua->pasangan?->nama_pasangan}}</td>
                    <td>{{$mertua->tempat_lahir}}</td>
                    <td>{{$mertua->tanggal_lahir}}</td>
                    <td>{{$mertua->alamat}}</td>
                    <td>{{$mertua->pekerjaanDetail?->pekerjaan}}</td>
                </tr>
                @endforeach
            @else
            <tr>
                <td colspan="12">Riwayat belum tersedia.</td>
            </tr>
            @endif
        </tbody>
    </table><br>
</body>
</html>
