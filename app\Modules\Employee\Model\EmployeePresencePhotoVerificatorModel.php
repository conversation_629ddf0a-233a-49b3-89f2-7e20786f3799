<?php

namespace App\Modules\Employee\Model;

use App\Modules\Role\Model\RoleModel;
use App\Modules\User\Model\UserModel;
use Illuminate\Database\Eloquent\Model;

class EmployeePresencePhotoVerificatorModel extends Model
{
    // use SoftDeletes;
    protected $table = 'presence_photo_temp_verificator';
    protected $guarded = [];

    public function role()
    {
        return $this->belongsTo(RoleModel::class, 'role_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }
}