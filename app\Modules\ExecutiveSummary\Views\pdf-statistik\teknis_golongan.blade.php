<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee List</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }

        th,
        td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
        }

        th {
            background-color: #f2f2f2;
        }

        .table-header {
            text-align: center;
            font-weight: bold;
            padding: 10px;
        }
    </style>
</head>

<body>
    <div class="table-header">
        DAFTAR PEGAWAI<br>
    </div>
    <table>
        <tr>
            <th rowspan="2">NO.</th>
            <th rowspan="2">NAMA<br>TEMPAT, TANGGAL LAHIR</th>
            <th rowspan="2">NIP<br>KARPEG</th>
            <th rowspan="2">GOL.<br>TMT</th>
            <th rowspan="2">JABATAN<br>UNIT KERJA<br>TMT</th>
            <th colspan="2">MASA KERJA</th>
            <th rowspan="2">USIA</th>
        </tr>
        <tr>
            <th>THN</th>
            <th>BLN</th>
        </tr>
        <?php $number = 0;?>
            @foreach($employee as $peg)
            <?php
                $number++;
                $tanggal_lahir = new DateTime($peg->tanggal_lahir); 
                $today = new DateTime('today');
                $age = $tanggal_lahir->diff($today)->y;
            ?>
            <tr>
                <td>{{$number}}</td>
                <td>{{$peg->nama}}<br>{{$peg->tempat_lahir}}, {{$peg->tanggal_lahir}}</td>
                <td>{{$peg->nip}}<br>{{$peg->nomor_kartu_asn}}</td>
                <td>{{$peg->name}}/{{$peg->pangkat}}<br>{{$peg->tmt_sk}}</td>
                <td>{{$peg->jabatan}}<br>{{$peg->path}}<br>{{$peg->tmt_jabatan}}</td>
                <td>{{$peg->masa_kerja_tahun}}</td>
                <td>{{$peg->masa_kerja_bulan}}</td>
                <td>{{$age}}</td>
            </tr>
            @endforeach
    </table>
</body>

</html>
