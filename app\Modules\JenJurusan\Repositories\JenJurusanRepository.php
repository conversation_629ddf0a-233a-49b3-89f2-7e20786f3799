<?php

namespace App\Modules\JenJurusan\Repositories;

use App\Modules\JenJurusan\Models\JenJurusan;
use Illuminate\Support\Facades\Auth;

class JenJurusanRepository
{
    public static function datatable($keyword, $per_page = 15)
    {
        $data = JenJurusan::search($keyword)
            ->select('idjenjurusan as id', 'jenjurusan', 'idtkpendid', 'idrumpunpendid')
            ->paginate($per_page);
        return $data;
    }
    public static function get($jenjurusan_id)
    {
        $jenjurusan = JenJurusan::where('id', $jenjurusan_id)->first();
        return $jenjurusan;
    }
    public static function create($jenjurusan)
    {
        $jenjurusan['idjenjurusan'] = JenJurusan::where('idjenjurusan', 'like', $jenjurusan['idtkpendid'] . '%')
            ->orderBy('idjenjurusan', 'desc')
            ->first()
            ->idjen<PERSON><PERSON>an + 1;

        $jenjurusan['user_id'] = Auth::user()->id;
        $jenjurusan['role_id'] = Auth::user()['roles']->toArray()[0]['id'];

        $jenjurusan = JenJurusan::create($jenjurusan);
        return $jenjurusan;
    }

    public static function update($jenjurusan_id, $jenjurusan)
    {
        $jenjurusan['user_id'] = Auth::user()->id;
        $jenjurusan['role_id'] = Auth::user()['roles']->toArray()[0]['id'];

        JenJurusan::find($jenjurusan_id)->update($jenjurusan);
        $jenjurusan = JenJurusan::find($jenjurusan_id);
        return $jenjurusan;
    }

    public static function delete($jenjurusan_id)
    {
        $delete = JenJurusan::where('id', $jenjurusan_id)->delete();
        return $delete;
    }
}
