<?php

namespace App\Modules\JenJabatan\Repositories;

use App\Modules\JenJabatan\Models\JenJabatanModel;

class JenJabatanRepository
{
    public static function datatable($per_page = 15)
    {
        $data =  JenJabatanModel::select(
            'idjenjab as id', 'jenjab', 'order'
        )->paginate($per_page);
        return $data;
    }
    public static function get($jenjabatan_id)
    {
        $jenjabatan = JenJabatanModel::where('id', $jenjabatan_id)->first();
        return $jenjabatan;
    }
    public static function create($jenjabatan)
    {
        $latestJenjabID = JenJabatanModel::pluck('idjenjab')->toArray();
        $jenjabatan['idjenjab'] = max($latestJenjabID) + 1;

        $jenjabatan = JenJabatanModel::insert($jenjabatan);
        return $jenjabatan;
    }

    public static function update($jenjabatan_id, $jenjabatan)
    {
        JenJabatanModel::where('idjenjab', $jenjabatan_id)->update($jenjabatan);
        $jenjabatan = JenJabatanModel::where('idjenjab', $jenjabatan_id)->first();
        return $jenjabatan;
    }

    public static function delete($jenjabatan_id)
    {
        $delete = JenJabatanModel::where('id', $jenjabatan_id)->delete();
        return $delete;
    }
}
