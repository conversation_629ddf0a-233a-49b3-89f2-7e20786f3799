<?php

namespace App\Modules\KatHukDis\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\KatHukDis\Models\KatHukDisModel;
use App\Modules\KatHukDis\Repositories\KatHukDisRepository;
// use App\Modules\KatHukDis\Requests\KatHukDisCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class KatHukDisController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = KatHukDisRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function index(Request $request)
    {
        $permissions = KatHukDisModel::get();
        return JsonResponseHandler::setResult($permissions)->send();

    }
}
