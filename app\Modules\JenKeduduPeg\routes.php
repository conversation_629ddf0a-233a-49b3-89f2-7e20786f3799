<?php
namespace App\Modules\JenKeduduPeg;

use App\Modules\JenKeduduPeg\Controllers\JenKeduduPegController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenkedudupeg')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenKeduduPegController::class, 'index'])->middleware('authorize:read-jenkedudupeg');
    Route::get('/datatable', [JenKeduduPegController::class, 'datatable'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/create', [JenKeduduPegController::class, 'create'])->middleware('authorize:create-jenkedudupeg');
    Route::post('/', [JenKeduduPegController::class, 'store'])->middleware('authorize:create-jenkedudupeg');
    Route::get('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'show'])->middleware('authorize:read-jenkedudupeg');
    Route::get('/{jenkedudupeg_id}/detail', [JenKeduduPegController::class, 'detail'])->middleware('authorize:read-jenkedudupeg');
    Route::get('/{jenkedudupeg_id}/edit', [JenKeduduPegController::class, 'edit'])->middleware('authorize:update-jenkedudupeg');
    Route::put('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'update'])->middleware('authorize:update-jenkedudupeg');
    Route::patch('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'update'])->middleware('authorize:update-jenkedudupeg');
    Route::delete('/{jenkedudupeg_id}', [JenKeduduPegController::class, 'destroy'])->middleware('authorize:delete-jenkedudupeg');
});