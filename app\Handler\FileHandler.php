<?php

namespace App\Handler;

use App\Exceptions\AppException;
use App\Type\JsonResponseType;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class FileHandler
{
    /**
     * Handles file upload and returns the path where the file was stored
     *
     * @param Request $file The request data from user
     * @param string $targetDir [optional] The target directory where the file will be stored
     * @param string $fileName [optional] The name of the file will be stored
     * @param array $allowedExtensions [optional] An array of allowed file extensions
     * @return string The path where the file was stored
     * @throws AppException If the file upload fails
     */
    public static function getFile($path = '', $access = 'private')
    {
        $storage = Storage::disk($access == 'private' ? 'local' : 'public');
        if (!$storage->exists($path)) {
            throw new AppException('File not found', JsonResponseType::NOT_FOUND, [], 404);
        }

        $file = $storage->get($path);
        $mime = $storage->mimeType($path);

        return response($file, 200)->header('Content-Type', $mime);
    }

    public static function getDefaultPhoto()
    {
        $photo = File::get(public_path('avatar.jpeg'));
        return response($photo, 200)->header('Content-Type', 'image/jpeg');
    }

    public static function validateEfile($file, $fileType) 
    {
        $ext = strtolower($file->extension());
        $fileType = str_replace(['_', 'efile'], [' ', ''], $fileType);
        if ($ext != 'pdf') {
            throw new AppException('Format file ' . $fileType . ' tidak sesuai. Format File yang diterima: PDF');
        }
        self::validateFileSize($file, $fileType);
    }

    public static function validatePhotoAndGetMimeExt($file) 
    {
        $mimeType = $file->getMimeType();
        $ext = $file->extension();
        if (!str_contains($mimeType, 'image')) {
            throw new AppException('Format file bukan berupa foto');
        }
        self::validateFileSize($file);
        return compact('mimeType', 'ext');
    }

    public static function validateFileSize($file, $fileType = '')
    {

        $maxSize = 1024 * 1024;
        $fileSize = $file->getSize();
        if ($fileSize > $maxSize) {
            throw new AppException('Ukuran file ' . $fileType . ' tidak boleh lebih dari 1 MB');
        }
    }

    public static function store(
        $file = null, 
        $targetDir = "", 
        $fileName = "", 
        $access = 'private', 
        $allowedExtensions = ['pdf'], 
        $isNeedException = true, 
        $allowedMime = '', 
        $isNeedExtension = true
    )
    {
        if (!$file) {
            throw new AppException('File belum diupload', errors: [$fileName]);
        }
        if (!empty($allowedMime)) {
            $mime = explode('/', $file->getMimeType())[0];
            if ($mime != $allowedMime) {
                throw new AppException("Format file tidak sesuai", $code = JsonResponseType::INTERNAL_SERVER_ERROR, 400);
            }; 
        }
        Log::debug('Storing file to submission ' . $targetDir);
        $extension = strtolower($file->getClientOriginalExtension());
        $dotExtension = $isNeedExtension ? '.' . $extension : '';
        $targetDir = $targetDir === "" ? 'uploads' : $targetDir;
        $fileName = str_replace(":", "_",
            $fileName === "" ? uniqid('', true) . '.' . $extension : $fileName . $dotExtension
        );
        // $messages = "Upload file activity on directory: " . $targetDir . " with filename: " . $fileName;
        // Checking for malicious activity by file extensions
        if (!empty($allowedExtensions) && !in_array($extension, $allowedExtensions)) {
            if ($isNeedException) {
                throw new AppException("Format file tidak diizinkan", $code = JsonResponseType::INTERNAL_SERVER_ERROR, 400);
            }
            else {
                return [
                    'message' => 'Format file tidak diizinkan', 
                    'code' => JsonResponseType::VALIDATION_ERROR, 
                    'status' => 422,
                    'success' => false
                ];
            }
        }

        $maxFileSize = 1024 * 1024; // 1MB
        if ($file->getSize() > $maxFileSize) {
            if ($isNeedException) {
                throw new AppException("Ukuran file melebihi maksimal ukuran yang diizinkan (1 MB)", JsonResponseType::INTERNAL_SERVER_ERROR, 400);
            }
            else {
                return [
                    'message' => "Ukuran file melebihi maksimal ukuran yang diizinkan (1 MB)", 
                    'code' => JsonResponseType::VALIDATION_ERROR, 
                    'status' => 422,
                    'success' => false
                ];
            }
        }

        // Create a log for monitoring file uploads on server
        // $result = $file->storeAs($targetDir, $fileName, $access == 'private' ? 'local' : 'public');
        Storage::disk($access == 'private' ? 'local' : 'public')
            ->putFileAs($targetDir, $file, $fileName);
        // dd($targetDir . "/" . $fileName);
        return $targetDir . "/" . $fileName;
    }

    public static function delete($path)
    {
        // dd($path);
        Log::debug("Deleting . " . $path);
        // Construct the full path to the file
        // Check if the file exists
        if (!Storage::exists($path)) {
            Log::debug("[FILE DELETE] - File not exist");
        }

        // Attempt to delete the file
        try {
            Storage::delete($path);
            return "File deleted successfully.";
        } 
        catch (Exception $e) {
            Log::debug("[FILE DELETE] - Failed Deleting File" . $e->getMessage());
            return "Error deleting file - File not found";
        }

    }
}
