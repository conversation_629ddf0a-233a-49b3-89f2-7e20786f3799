<?php

namespace App\Modules\InstansiInduk\Repositories;

use App\Modules\InstansiInduk\Models\InstansiInduk;

class InstansiIndukRepository
{
    public static function datatable($per_page = 15)
    {
        $data = InstansiInduk::paginate($per_page);
        return $data;
    }
    public static function get($instansi_induk_id)
    {
        $instansi_induk = InstansiInduk::where('id', $instansi_induk_id)->first();
        return $instansi_induk;
    }
    public static function create($instansi_induk)
    {
        $instansi_induk = InstansiInduk::create($instansi_induk);
        return $instansi_induk;
    }

    public static function update($instansi_induk_id, $instansi_induk)
    {
        InstansiInduk::where('id', $instansi_induk_id)->update($instansi_induk);
        $instansi_induk = InstansiInduk::where('id', $instansi_induk_id)->first();
        return $instansi_induk;
    }

    public static function delete($instansi_induk_id)
    {
        $delete = InstansiInduk::where('id', $instansi_induk_id)->delete();
        return $delete;
    }
}
