<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Handler\JsonResponseHandler;
use Illuminate\Support\Facades\Auth;
use App\Modules\User\Model\UserModel;
use App\Modules\Employee\Model\EmployeeModel;

class CheckPegawaiDataWithAuthenticatedPegawai
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // $XRoleId = $request->header('X-Role-Id');
        // if (empty($XRoleId)) {
        //     return JsonResponseHandler::denied();
        // }

        // $user = UserModel::find(Auth::user()->id);
        // $roles = $user->roles->toArray();
        // $employeeID = $request->route('employee_id');
        
        // foreach ($roles as $role) {
        //     if (($role['id'] == 13 && $XRoleId == 13)) {

        //         if (Auth::user()->id_pegawai != $employeeID) {
        //             return JsonResponseHandler::denied();
        //         }
        //     }
        // }

        return $next($request);
    }
}
