<?php

namespace App\Modules\JenKeduduPeg\Models;
    
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Handler\ModelSearchHandler;

class JenKeduduPeg extends Model
{
    use SoftDeletes;
    protected $table = 'jenkedudupeg';
    protected $guarded = [];
    protected $keyType = "string";
    protected $primaryKey = "id";

    public function scopeSearch($query, $keyword)
    {
        $searchable = ['name'];
        return ModelSearchHandler::handle($query, $searchable, $keyword);
    }
}