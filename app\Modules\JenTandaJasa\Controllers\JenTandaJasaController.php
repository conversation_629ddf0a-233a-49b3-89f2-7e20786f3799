<?php

namespace App\Modules\JenTandaJasa\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenTandaJasa\Models\JenTandaJasaModel;
use App\Modules\JenTandaJasa\Repositories\JenTandaJasaRepository;
// use App\Modules\JenTandaJasa\Requests\JenTandaJasaCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenTandaJasaController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenTandaJasaRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function index(Request $request)
    {
        $permissions = JenTandaJasaModel::get();
        return JsonResponseHandler::setResult($permissions)->send();

    }
}
