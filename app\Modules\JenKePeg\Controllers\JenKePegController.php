<?php

namespace App\Modules\JenKePeg\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenKePeg\Models\JenKePeg;
use App\Modules\JenKePeg\Repositories\JenKePegRepository;
use App\Modules\JenKePeg\Requests\JenKePegCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenKePegController extends Controller
{
    public function index(Request $request)
    {
        $jenkepeg = JenKePeg::get();
        return JsonResponseHandler::setResult($jenkepeg)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenKePegRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $jenkepeg_id)
    {
        $jenkepeg = JenKePeg::where('id', $jenkepeg_id)->first();
        return JsonResponseHandler::setResult($jenkepeg)->send();
    }

    public function create()
    {
        return view('JenKePeg::create');
    }

    public function store(JenKePegCreateRequest $request)
    {
        $payload = $request->all();
        $jenkepeg = JenKePegRepository::create($payload);
        return JsonResponseHandler::setResult($jenkepeg)->send();
    }

    public function show(Request $request, $id)
    {
        $jenkepeg = JenKePegRepository::get($id);
        return JsonResponseHandler::setResult($jenkepeg)->send();
    }

    public function edit($id)
    {
        return view('JenKePeg::edit', ['jenkepeg_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jenkepeg = JenKePegRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jenkepeg)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JenKePegRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
