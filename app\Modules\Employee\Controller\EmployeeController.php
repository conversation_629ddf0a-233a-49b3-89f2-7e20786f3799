<?php

namespace App\Modules\Employee\Controller;

use App\Exceptions\AppException;
use App\Handler\FileHandler;
use Illuminate\Http\Request;
use App\Handler\JsonResponseHandler;
use App\Type\JsonResponseType;
use App\Http\Controllers\AppController;
use App\Jobs\EmployeeCreationJob;
use App\Modules\Efile\Repositories\EfileRepository;
use App\Modules\Siasn\Repositories\SiasnRepositories;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\RiwayatPns\Models\RiwayatPnsModel;
use App\Modules\RiwayatCpns\Models\RiwayatCpnsModel;
use App\Modules\Employee\Request\EmployeeCreateRequest;
use App\Modules\Employee\Interfaces\IEmployeeRepository;
// use App\Modules\Employee\Model\EmployeePhotoEps;
use App\Modules\Employee\Repositories\EmployeeRepository;
use App\Modules\ImportProcess\Models\ImportProcessModel;
use App\Modules\PerubahanData\Models\PerubahanData;
use App\Modules\RiwayatAngkaKredit\Models\RiwayatAngkaKreditModel;
use App\Modules\KeluargaAnak\Models\KeluargaAnakModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiModel;
use App\Modules\KeluargaKekerabatan\Models\KeluargaKekerabatanModel;
use App\Modules\KeluargaMertua\Models\KeluargaMertuaModel;
use App\Modules\KeluargaOrangTua\Models\KeluargaOrangTuaModel;
use App\Modules\RiwayatCpns\Models\RiwayatCpnsTempModel;
use App\Modules\RiwayatJabatan\Models\RiwayatJabatanModel;
use App\Modules\RiwayatPenugasan\Models\RiwayatPenugasanModel;
use App\Modules\RiwayatKepangkatan\Models\RiwayatKepangkatanModel;
use App\Modules\RiwayatKgb\Models\RiwayatKgbModel;
use App\Modules\RiwayatPendidikan\Models\RiwayatPendidikanModel;
use App\Modules\RiwayatPendidikanNonFormal\Models\RiwayatPendidikanNonFormalModel;
use App\Modules\RiwayatTandaJasa\Models\RiwayatTandaJasaModel;
use App\Modules\RiwayatPenguasaanBahasa\Models\RiwayatPenguasaanBahasaModel;
use App\Modules\RiwayatHukumDisiplin\Models\RiwayatHukumDisiplinModel;
use App\Modules\RiwayatPns\Models\RiwayatPnsTempModel;
use App\Modules\RiwayatPppk\Models\RiwayatPppkModel;
use App\Modules\Employee\Repositories\PhotoRepository;


use Illuminate\Support\Facades\Storage;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
class EmployeeController extends AppController
{
    public $repository;

    public function __construct(IEmployeeRepository $repository)
    {
        parent::__construct(module_name: "Employee", repository: $repository);
        $this->repository = $repository;
    }

    public function detail(Request $request, $employee_id)
    {
        $requestOnly = !empty($request->input('only')) ? explode(",", $request->input('only')) : [];
        $withEfile = (int) $request->input('withEfile') ?? 0;

        $employee = EmployeeModel::select(
                '*',
                DB::raw('
                    concat(
                        tb_01.gelar_depan,
                        IF(LENGTH(tb_01.gelar_depan)>0,". ",""),
                        tb_01.nama,
                        IF(LENGTH(tb_01.gelar_belakang)>0,", ",""),
                        tb_01.gelar_belakang
                    ) as namalengkap
                '),
                DB::raw("
                    concat(
                        id_unit_kerja,'.',
                        id_induk_upt,'.',
                        id_sub_unit,'.',
                        id_sub_sub_unit,'.',
                        id_sub_sub_sub_unit
                    ) as idskpd
                ")
            )
            ->where('id', $employee_id)
            ->when(empty($requestOnly) || in_array('employee', $requestOnly), function ($query) {
                $query->with([
                    'gender',
                    'religion',
                    'maritalStatus',
                    'jenisKepegawaian',
                    'jenkepeg',
                    'pendudupeg',
                    'instansiInduk',
                    'employeeStatus'
                ]);
            })
            ->when(empty($requestOnly) || in_array('jabatan_terakhir', $requestOnly), function ($query) {
                $query->with([
                    'unitKerja' => function ($query) {
                        $query->select('idskpd', 'skpd', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit');
                    },
                    'subUnitKerja' => function ($query) {
                        $query->select('idskpd', 'skpd', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit');
                    },
                    'subSubSubUnitKerja' => function ($query) {
                        $query->select('idskpd', 'skpd', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit', 'id_sub_sub_sub_unit');
                    },
                ]);
            })
            ->first();

        if (empty($requestOnly) || in_array('pendidikan_terakhir', $requestOnly)) {
            $employee->setAppends(['last_education']);
        }

        $jabatanTerakhir = null;
        if (empty($requestOnly) || in_array('jabatan_terakhir', $requestOnly)) {
            $jabatanTerakhir = RiwayatJabatanModel::where('id_pegawai', $employee_id)
                // ->select('id', 'id_pegawai', 'nip', 'id_jenis_jabatan', 'id_jabatan', 'jabatan', 'id_penetap')
                ->where('isakhir', 1)
                ->with([
                    'jenisJabatan',
                    'penetap' => function ($query) {
                        $query->select('id', 'jabatan', 'namalengkap', 'pangkat');
                    },
                    'jabatanStruktural',
                    'jabatanTertentu' => function ($query) {
                        $query->select('idjabfung', 'jabfung', 'jabfung2', 'jenjang', 'tingkat2');
                    },
                    'jabatanPelaksana'
                ])
                ->first();
            try {
                $efiles = (new EfileRepository())->getFileByNipAndCodes($employee->nip, '07_71');
                if (count($efiles) > 0 && !empty($efiles[0]['efile'])) {
                    $jabatanTerakhir->efile = collect($efiles[0]['efile'])
                        ->filter(function ($value) use ($jabatanTerakhir) {
                            return $value['id_riwayat'] == $jabatanTerakhir->old_id || $value['id_riwayat'] == $jabatanTerakhir->id;
                        })
                        ->sortByDesc('modified_at')
                        ->first();
                }
                else {
                    $jabatanTerakhir->efile = null;
                }
            } catch (\Throwable $th) {
                if (!empty($jabatanTerakhir)) {
                    $jabatanTerakhir->efile = null;
                }
            }
            $employee->jabatanTerakhir = $jabatanTerakhir;
        }

        if (empty($requestOnly) || in_array('penugasan', $requestOnly)) {
            $penugasan = RiwayatPenugasanModel::where('id_pegawai', $employee_id)
                ->where('is_active', 1)
                // ->whereDate('periode_awal', '<=', $today)->whereDate('periode_akhir', '>=', $today)
                ->with(['jenis_penugasan', 'lokasiUnitKerja'])
                ->get();
            $employee->penugasan = $penugasan;
        }

        if (empty($requestOnly) || in_array('nilai_angka_kredit', $requestOnly)) {
            $nilaiAngkaKredit = RiwayatAngkaKreditModel::where('id_pegawai', $employee_id)->pluck('nilai_angka_kredit')->first();
            $employee->nilaiAngkaKredit = $nilaiAngkaKredit;
        }

        $kepangkatan = null;
        if (empty($requestOnly) || in_array('nilai_angka_kredit', $requestOnly)) {
            $statusPegawai = (int) EmployeeModel::select('status_pegawai')
                ->where('id', $employee_id)
                ->first()
                ->status_pegawai;
                
            if (in_array($statusPegawai, [3, 4])) {
                $kepangkatan = RiwayatPppkModel::where('id_pegawai', $employee_id)->with(['golru'])->first();
            } else {
                $kepangkatan = RiwayatKepangkatanModel::where('id_pegawai', $employee_id)
                    ->select('id', 'id_pegawai', 'nip', 'id_kepangkatan', 'id_penetap', 'nomor_sk', 'jenis_kp')
                    ->with([
                        'penetap' => function ($query) {
                            $query->select('id', 'jabatan', 'namalengkap', 'pangkat');
                        },
                        'golru' => function ($query) {
                            $query->select('id', 'name', 'pangkat');
                        }
                    ])
                    ->where('isakhir', 1)
                    ->first();
            }
            $employee->kepangkatan = $kepangkatan;
        }

        if (empty($requestOnly) || in_array('pendidikan_awal', $requestOnly)) {
            $pendidikanAwal = RiwayatPendidikanModel::where('id_pegawai', $employee_id)->with(['tkpendid' => function ($query) {
                $query->select('idtkpendid', 'tkpendid');
            }, 'jenjurusan' => function ($query) {
                $query->select('idjenjurusan', 'jenjurusan');
            }])->where('isawal', 1)->orderBy('id', 'asc')->first();
            $employee->pendidikanAwal = $pendidikanAwal;
        }

        if (empty($requestOnly) || in_array('pendidikan_akhir', $requestOnly)) {
            $pendidikanTerakhir = RiwayatPendidikanModel::where('id_pegawai', $employee_id)->with(['tkpendid' => function ($query) {
                $query->select('idtkpendid', 'tkpendid');
            }, 'jenjurusan' => function ($query) {
                $query->with(['tkpendid'])->select('idjenjurusan', 'jenjurusan');
            }])->where('isakhir', 1)->orderBy('id', 'desc')->first();
            $employee->pendidikanTerakhir = $pendidikanTerakhir;
        }

        if (empty($requestOnly) || in_array('is_perubahan', $requestOnly)) {
            $isperubahan = PerubahanData::where('id_pegawai', $employee_id)->whereNot('status', 1)->whereNot('status', 2)->count();
            $employee->isperubahan = $isperubahan;
        }


        if ($withEfile == 1 || in_array('efile', $requestOnly)) {
            try {
                // File
                $golru_id = $kepangkatan?->golru->id ?? '';
                $efileCodes = "44_1,04_12,11,35_1,04_11,44_2,23_1,10,07_71,14_{$golru_id},36,12,50,49";
                $fileMetadata = (new EfileRepository())->getFileByNipAndCodes($employee->nip, $efileCodes);
                // throw new AppException('dsaf', JsonResponseType::ERROR, $fileMetadata);
                // dd($fileMetadata);
                foreach ($fileMetadata as $file) {
                    // Log::debug($file);
                    try {
                        if ($file['response'] == 'success' && !empty($file['efile'])) {
                            if ($file['id_jenis'] == '44_1') { //ktp
                                $employee->fileKtpUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '04_12') {
                                $employee->fileKarpegUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '11') {
                                $employee->fileKarisUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '35_1') {
                                $employee->fileBpjsUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '36') {
                                $employee->fileNpwpUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '12') { //taspen ga bisa, jenis dokumen tidak ada pada sistem
                                $employee->fileTaspenUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '04_11') {
                                $employee->fileNipLamaUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '44_2') {
                                $employee->fileNomorKKUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '23_1') {
                                $employee->fileAktaLahirUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '10') {
                                $employee->fileStatusNikahUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '07_71') {
                                $employee->fileJabatanUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '14_' . $golru_id) {
                                $employee->fileKepangkatanUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '50') { // Surat tidak pindah
                                $employee->fileSuratTidakPindahUrl = $file['efile'][0]['url'];
                            } elseif ($file['id_jenis'] == '49') { // SKUMPTK
                                $employee->fileSkumptkUrl = $file['efile'][0]['url'];
                            }
                        }
                    } catch (Exception $err) {
                    }
                }
            } catch (Exception $err) {
            }
        }

        return JsonResponseHandler::setResult($employee)->send();
    }

    public function sinkronGelar(Request $request, $employeeID)
    {
        try {
            
            $employee = EmployeeModel::where('id', $employeeID)->first();
            $api = (new SiasnRepositories())->getDataUtama($employee->nip);
            $api['gelarDepan'] = $api['gelarDepan'] == '-' ? '' : $api['gelarDepan'];
            $api['gelarBelakang'] = $api['gelarBelakang'] == '-' ? '' : $api['gelarBelakang'];
            $payload = [
                'gelar_depan' => $api['gelarDepan'],
                'gelar_belakang' => $api['gelarBelakang'],
            ];
            EmployeeModel::find($employeeID)->update($payload);
            return JsonResponseHandler::setResult($payload)->send();
        }
        catch (Exception $e) {
            throw new AppException(
                $e->getMessage() ?? 'Gagal melakukan sinkronisasi',
                JsonResponseType::INTERNAL_SERVER_ERROR,
                $e->getMessage(),
                500
            );
        }
    }

    public function attkeluarga(Request $request, $nik)
    {
        $employee = EmployeeModel::select(
            DB::raw('tb_01.id as id_pegawai,CONCAT(tb_01.gelar_depan,IF(LENGTH(tb_01.gelar_depan)>0,". ",""),tb_01.nama,IF(LENGTH(tb_01.gelar_belakang)>0,", ",""),tb_01.gelar_belakang) as nama_lengkap, TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) AS umur'),
            DB::raw('nip as pekerjaan_nip, nik, tempat_lahir, tanggal_lahir, nomor_karis_karsu, nomor_bpjs, status_pernikahan as status_perkawinan, jenis_kelamin, nomor_hp, domisili_provinsi as alamat_provinsi, domisili_kabupaten as alamat_kabupaten, domisili_kecamatan as alamat_kecamatan, domisili_kelurahan as alamat_kelurahan, domisili_alamat as alamat, domisili_rt as alamat_rt, domisili_rw as alamat_rw, domisili_kode_pos as alamat_kode_pos, 1 as pekerjaan, if(kedudukan_pegawai=1,1,2) as pekerjaan_status, if(kedudukan_pegawai=1,1,if(id_unit_kerja = "99" AND id_induk_upt = "00" AND id_sub_unit = "40",2,"")) as status_kedudukan'),
            DB::raw("CONCAT(id_unit_kerja,'.',id_induk_upt,'.',id_sub_unit,'.',id_sub_sub_unit,'.',id_sub_sub_sub_unit) as idskpd, id_unit_kerja, id_induk_upt, id_sub_unit, id_sub_sub_unit, id_sub_sub_sub_unit, nomor_akta_lahir, tanggal_akta_lahir")
        )
            ->where('nik', $nik)
            ->with([
                'gender',
                'religion',
                'maritalStatus',
                'jenisKepegawaian',
                'unitKerja' => function ($query) {
                    $query->select('idskpd', 'skpd', 'path', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit');
                },
                'subunitKerja' => function ($query) {
                    $query->select('idskpd', 'skpd', 'path', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit');
                },
                'subSubSubUnitKerja' => function ($query) {
                    $query->select('idskpd', 'skpd', 'path', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit', 'id_sub_sub_sub_unit');
                },
                'jenkepeg',
                'pendudupeg'
            ])->first();

        if ($employee) {
            $pendidikanTerakhir = RiwayatPendidikanModel::select('id_jenjang', 'jenjang')
                ->where('id_pegawai', $employee->id_pegawai)
                ->where('isakhir', 1)->orderBy('id', 'desc')
                ->first();
            $employee->pendidikanTerakhir = $pendidikanTerakhir;
        }

        return JsonResponseHandler::setResult($employee)->send();
    }

    public function attkeluargapasangan(Request $request, $nik, $id_pegawai)
    {
        $nikSplit[0] = substr($nik, 0, 2);
        $nikSplit[1] = substr($nik, 2, 4);
        $nikSplit[2] = substr($nik, 6, 6);
        $nikSplit[3] = substr($nik, 12, 6);

        $formattedNik = implode('.', $nikSplit);

        $array = EmployeeModel::select(
            DB::raw('tb_01.id as id_pegawai,CONCAT(tb_01.gelar_depan,IF(LENGTH(tb_01.gelar_depan)>0,". ",""),tb_01.nama,IF(LENGTH(tb_01.gelar_belakang)>0,", ",""),tb_01.gelar_belakang) as nama_lengkap, TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) AS umur'),
            DB::raw('nip as pekerjaan_nip, nik, tempat_lahir, tanggal_lahir, nomor_karis_karsu, nomor_bpjs, status_pernikahan as status_perkawinan, jenis_kelamin, nomor_hp, domisili_provinsi as alamat_provinsi, domisili_kabupaten as alamat_kabupaten, domisili_kecamatan as alamat_kecamatan, domisili_kelurahan as alamat_kelurahan, domisili_alamat as alamat, domisili_rt as alamat_rt, domisili_rw as alamat_rw, domisili_kode_pos as alamat_kode_pos, 1 as pekerjaan, if(kedudukan_pegawai=1,1,2) as pekerjaan_status, if(kedudukan_pegawai=1,1,if(id_unit_kerja = "99" AND id_induk_upt = "00" AND id_sub_unit = "40",2,"")) as status_kedudukan'),
            DB::raw("CONCAT(id_unit_kerja,'.',id_induk_upt,'.',id_sub_unit,'.',id_sub_sub_unit,'.',id_sub_sub_sub_unit) as idskpd, id_unit_kerja, id_induk_upt, id_sub_unit, id_sub_sub_unit, id_sub_sub_sub_unit, nomor_akta_lahir, tanggal_akta_lahir")
        )
            ->whereIn('nik', [$nik, $formattedNik])
            ->with([
                'gender',
                'religion',
                'maritalStatus',
                'jenisKepegawaian',
                'unitKerja' => function ($query) {
                    $query->select('idskpd', 'skpd', 'path', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit');
                },
                'subunitKerja' => function ($query) {
                    $query->select('idskpd', 'skpd', 'path', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit');
                },
                'subSubSubUnitKerja' => function ($query) {
                    $query->select('idskpd', 'skpd', 'path', 'id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit', 'id_sub_sub_sub_unit');
                },
                'jenkepeg',
                'pendudupeg'
            ])->first();

        $cekpasangan = KeluargaIstriSuamiModel::whereIn('nik', [$nik, $formattedNik])
                        ->where('id_pegawai', '!=', $id_pegawai)
                        ->where('status_perkawinan', '=', 1)
                        ->count();

        if ($cekpasangan > 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'NIK Pasangan tercatat masih berstatus pasangan aktif dari pegawai lain.',
                'data' => '',
            ], JsonResponse::HTTP_NOT_FOUND);
        } else {
            if ($array) {
                $employee = $array;
                $pendidikanTerakhir = RiwayatPendidikanModel::select('id_jenjang', 'jenjang')
                    ->where('id_pegawai', $employee->id_pegawai)
                    ->where('isakhir', 1)
                    ->orderBy('id', 'desc')
                    ->first();
                $employee->pendidikanTerakhir = $pendidikanTerakhir;
                return JsonResponseHandler::setResult($employee)->send();
            }
        }
    }

    // public function detailPangkat(Request $request, $employee_id)
    // {
    //     $kepangkatan = RiwayatKepangkatanModel::where('id_pegawai', $employee_id)->with(['penetap', 'golru'])->where('isakhir', 1)->first();
    //     $employee = EmployeeModel::select('*', DB::raw("concat(id_unit_kerja,'.',id_induk_upt,'.',id_sub_unit,'.',id_sub_sub_unit,'.',id_sub_sub_sub_unit) as idskpd"))
    //         ->where('id', $employee_id)->with(['gender', 'religion', 'maritalStatus', 'jenisKepegawaian', 'unitKerja', 'subunitKerja', 'jenkepeg', 'pendudupeg'])->first();


    //     try {
    //         $fileMetadata = (new EfileRepository())->getFileByNipAndCodes($employee->nip, "14_{$kepangkatan->golru->id}");

    //         if (!empty($fileMetadata)) {
    //             $employee->fileKepangkatanUrl = @$fileMetadata[0]['efile'][0]['url'];
    //         }
    //     } catch (Exception $err) {
    //         $employee->fileKepangkatanUrl = null;
    //     }
    //     $employee->kepangkatan = $kepangkatan;

    //     return JsonResponseHandler::setResult($employee)->send();
    // }

    public function detailPangkat(Request $request, $employeeId)
    {
        $kepangkatan = RiwayatKepangkatanModel::where('id_pegawai', $employeeId)
                        ->where('isakhir', 1)
                        ->with(['penetap', 'golru'])
                        ->first();

        try {
            $fileCode = $kepangkatan->isawal == 1
                ? "02"
                : "14_{$kepangkatan->golru?->id}";

            if ($kepangkatan->isawal != 1 && !isset($kepangkatan->golru)) {
                throw new AppException('');
            }

            $employee = EmployeeModel::find($employeeId);
            $fileMetadata = (new EfileRepository())->getFileByNipAndCodes($employee->nip, $fileCode);
            $kepangkatan->kepangkatan_efile = $fileMetadata[0]['response'] == 'success'
                ? $fileMetadata[0]['efile'][0]
                : null;
        }
        catch (AppException $e) {
            $kepangkatan->kepangkatan_efile = null;
        }

        return JsonResponseHandler::setResult($kepangkatan)->send();

    }

    public function detailPppk(Request $request, $employee_id)
    {
        $employee = EmployeeModel::select('*', DB::raw("concat(id_unit_kerja,'.',id_induk_upt,'.',id_sub_unit,'.',id_sub_sub_unit,'.',id_sub_sub_sub_unit) as idskpd"))
            ->where('id', $employee_id)->with(['gender', 'religion', 'maritalStatus', 'jenisKepegawaian', 'unitKerja', 'subunitKerja', 'jenkepeg', 'pendudupeg'])->first();

        $pppk = RiwayatPppkModel::where('id_pegawai', $employee_id)
            ->with(['pangkat'])
            ->where('isakhir', 1)
            ->first();

        if (!$pppk) {
            $pppk = (object) [
                'fileSkPppkUrl' => null,
                'fileSpmtUrl' => null,
                'pangkat' => null
            ];
        }

        if (empty($requestOnly) || in_array('efile', $requestOnly)) {
            try {
                $fileMetadata = (new EfileRepository())->getFileByNipAndCodes($employee->nip, "51_1,07_78");
                foreach ($fileMetadata as $file) {
                    if ($file['id_jenis'] == '51_1') {
                        $pppk->fileSkPppkUrl = $file['efile'][0]['url'];
                    } elseif ($file['id_jenis'] == '07_78') {
                        $pppk->fileSpmtUrl = $file['efile'][0]['url'];
                    }
                }
            } catch (Exception $err) {
                Log::error($err);
            }
        }


        if (!isset($pppk->fileSkPppkUrl)) {
            $pppk->fileSkPppkUrl = null;
        }
        if (!isset($pppk->fileSpmtUrl)) {
            $pppk->fileSpmtUrl = null;
        }

        $employee->pppk = $pppk;

        return JsonResponseHandler::setResult($employee)->send();
    }

    public function detailPendidikan($employee_id)
    {
        $pendidikanAwal = RiwayatPendidikanModel::where('id_pegawai', $employee_id)
            ->with([
                'tkpendid' => function ($query) {
                    $query->select('idtkpendid', 'tkpendid', 'tingkat');
                }, 
                'jenjurusan' => function ($query) {
                    $query->select('idjenjurusan', 'jenjurusan');
                }
            ])
            ->where('isawal', 1)
            ->orderBy('id', 'asc')
            ->first();

        $pendidikanTerakhir = RiwayatPendidikanModel::where('id_pegawai', $employee_id)
            ->with([
                'tkpendid' => function ($query) {
                    $query->select('idtkpendid', 'tkpendid', 'tingkat');
                },
                'jenjurusan' => function ($query) {
                    $query->select('idjenjurusan', 'jenjurusan');
                }
            ])
            ->where('isakhir', 1)
            ->orderBy('id', 'desc')
            ->first();

        $employee = EmployeeModel::where('id', $employee_id)->first();

        $filePendidikanAwalCode = isset($pendidikanAwal->tkpendid->tingkat) 
            ? '09_' . $pendidikanAwal->tkpendid->tingkat 
            : null;
                
        $filePendidikanAkhirCode = isset($pendidikanTerakhir->tkpendid->tingkat) 
            ? '09_' . $pendidikanTerakhir->tkpendid->tingkat 
            : null;

        $concatedCodes = $filePendidikanAwalCode . (!empty($filePendidikanAwalCode) ? ',' : '') . $filePendidikanAkhirCode;
        
        if (!empty($concatedCodes)) {
            $efileData = (new EfileRepository())->getFileByNipAndCodes($employee->nip, $concatedCodes);
            if (!empty($efileData)) {
                foreach ($efileData as $efile) {
                    if ($efile['response'] == 'success') {
                        if ($efile['id_jenis'] == $filePendidikanAwalCode) {
                            $pendidikanAwal->fileijazah_efileUrl = $efile['efile'][0]['url'];
                        }
                        if ($efile['id_jenis'] == $filePendidikanAkhirCode) {
                            $pendidikanTerakhir->fileijazah_efileUrl = $efile['efile'][0]['url'];
                        }
                    }
                }
            }
        }

        $data = [
            'pendidikanAwal' => $pendidikanAwal,
            'pendidikanTerakhir' => $pendidikanTerakhir
        ];

        return JsonResponseHandler::setResult($data)->send();
    }

    public function cpnsPns(Request $request, $employee_id)
    {
        $employee = EmployeeModel::where('id', $employee_id)->first();
        $requestOnly = !empty($request->input('only')) ? explode(",", $request->input('only')) : [];

        $cpns = RiwayatCpnsModel::where('id_pegawai', $employee_id)->with(['golruCpns'])->first();
        $pns = RiwayatPnsModel::where('id_pegawai', $employee_id)->with(['golruPns'])->first();

        $cpns = $cpns ? $cpns->toArray() : [];
        $pns = $pns ? $pns->toArray() : [];

        $cpnsSubmission = RiwayatCpnsTempModel::where('id_pegawai', $employee_id)->whereNotIn('status', [1, 2])->first();
        $pnsSubmission = RiwayatPnsTempModel::where('id_pegawai', $employee_id)->whereNotIn('status', [1, 2])->first();

        if (empty($requestOnly) || in_array('efile', $requestOnly)) {
            $fileMetadata = (new EfileRepository())->getFileByNipAndCodes($employee->nip, "02,28_10,07_78,06,03,05");
            if (!empty($fileMetadata)) {
                foreach ($fileMetadata as $file) {
                    if ($file['id_jenis'] == '02' && $file['response'] == 'success') {
                        $cpns['sk_cpns_efile'] = $file['efile'][0] ? $file['efile'][0]['url'] : '';
                    } 
                    elseif ($file['id_jenis'] == '28_10' && $file['response'] == 'success') {
                        $cpns['nomor_ujikes_efile'] = $file['efile'][0] ? $file['efile'][0]['url'] : '';
                    } 
                    elseif ($file['id_jenis'] == '07_78' && $file['response'] == 'success') {
                        $cpns['spmt_cpns_efile'] = $file['efile'][0] ? $file['efile'][0]['url'] : '';
                    } 
                    elseif ($file['id_jenis'] == '06' && $file['response'] == 'success') {
                        $cpns['sttpl_cpns_efile'] = $file['efile'][0] ? $file['efile'][0]['url'] : '';
                    } 
                    elseif ($file['id_jenis'] == '03' && $file['response'] == 'success') {
                        $pns['sk_pns_efile'] = $file['efile'][0] ? $file['efile'][0]['url'] : '';
                    } 
                    elseif ($file['id_jenis'] == '05' && $file['response'] == 'success') {
                        $pns['berita_acara_efile'] = $file['efile'][0] ? $file['efile'][0]['url'] : '';
                    }
                }
            }
        }

        return JsonResponseHandler::setResult([
            'cpns' => $cpns,
            'pns' => $pns,
            'cpnsSubmissionId' => empty($cpnsSubmission) ? null : $cpnsSubmission->id,
            'pnsSubmissionId' => empty($pnsSubmission) ? null : $pnsSubmission->id
        ])->send();
    }

    public function kgb(Request $request, $employee_id)
    {
        $kgb = RiwayatKgbModel::where('id_pegawai', $employee_id)
            ->where('isakhir', 1)
            ->with(['penetap', 'golru'])
            ->first();

        if (!empty($kgb)) {
            $kgb->setAppends(['dokumen_efile']);
        }

        return JsonResponseHandler::setResult($kgb)->send();
    }

    public function jabatan(Request $request, $employee_id)
    {
        $employeeJabatan = EmployeeModel::where('id', $employee_id)->first()->jabatan()
            ->with([
                'instansiInduk',
                'instansiKerja'
            ])->first();
        return JsonResponseHandler::setResult($employeeJabatan)->send();
    }


    public function datatable(Request $request)
    {
        $params = $request->only([
            'nama',
            'nip',
            'keyword',
            'per_page'
        ]);

        $per_page = isset($params['per_page']) && !empty($params['per_page']) 
                        ? $params['per_page'] 
                        : 15;

        $unorParams = $request->only([
            'id_instansi_induk',
            'id_unit_kerja',
            'id_sub_unit',
            'id_sub_sub_unit'
        ]);

        foreach ($unorParams as $key => $unor) {
            if (empty($unor)) {
                unset($unorParams[$key]);
                continue;
            }
            if ($key == 'id_sub_unit') {
                $unorParams['id_sub_unit'] = substr($unorParams['id_sub_unit'], 6, 2);
            }
            if ($key == 'id_sub_sub_unit') {
                $unorParams['id_sub_sub_unit'] = substr($unorParams['id_sub_sub_unit'], 9, 2);
            }
        }

        $pegawaiParams = $request->only([
            'status_pegawai',
            'kedudukan_pegawai',
            'jenis_pegawai'
        ]);

        foreach ($pegawaiParams as $key => $pegawai) {
            if (empty($pegawai)) {
                unset($pegawaiParams[$key]);
            }
        }

        $employee = EmployeeModel::with([
                'lastJabatan' => function ($q) {
                    $q->select('id_pegawai', 'id_jenis_jabatan', 'id_jabatan', 'id_unit_kerja');
                },
                'unitKerja' => function ($q) {
                    $q->select('id_unit_kerja', 'path');
                },
                'lastKepangkatan' => function ($q) {
                    $q->select('id_pegawai', 'id_kepangkatan', 'tmt_sk', 'masa_kerja_tahun', 'masa_kerja_bulan');
                },
                'lastKepangkatan.golru' => function ($q) {
                    $q->select('id', 'name', 'pangkat', 'golru_p3k');
                },
                'jenisKepegawaian',
                'employeeStatus'
            ])
            ->when(isset($params['nama']) && !empty($params['nama']), function ($q) use ($params) {
                $q->where('nama', 'like', '%' . $params['nama'] . '%');
            })
            ->when(isset($params['nip']) && !empty($params['nip']), function ($q) use ($params) {
                $q->where(function ($q) use ($params) {
                    $q->where('nip', 'like', '%' . $params['nip'] . '%')
                        ->orWhere('nip_lama', 'like', '%' . $params['nip'] . '%')
                        ->orWhere('nik', 'like', '%' . $params['nip'] . '%');
                });
            })
            ->when(!empty($unorParams), function ($q) use ($unorParams) {
                $q->where(function ($q) use ($unorParams) {
                    foreach ($unorParams as $key => $unor) {
                        if (!empty($unor)) {
                            $q->where($key, $unor);
                        }
                    }
                });
            })
            ->when(!empty($pegawaiParams), function ($q) use ($pegawaiParams) {
                $q->where(function ($q) use ($pegawaiParams) {
                    foreach ($pegawaiParams as $key => $pegawai) {
                        $q->where($key, $pegawai);
                    }
                });
            })
            ->when(isset($params['keyword']) && !empty($params['keyword']), function ($q) use ($params) {
                $q->where(function ($query) use ($params) {
                    $query->whereRaw("
                        nama LIKE ?
                        or nip LIKE ?
                        or nip_lama LIKE ?
                        or id LIKE ?
                    ", array_fill(0, 4, '%' . $params['keyword'] . '%'));
                });
            })
            ->selectRaw("
                id,
                concat(
                    gelar_depan,
                    IF(LENGTH(gelar_depan) > 0, '. ', ''),
                    nama,
                    IF(LENGTH(gelar_belakang) > 0, ', ', ''),
                    gelar_belakang
                ) as namalengkap,
                concat(
                    tempat_lahir,
                    if(tempat_lahir != '' and tanggal_lahir is not null, ', ', ''),
                    tanggal_lahir
                ) as dob,
                nip,
                nip_lama,
                status_pegawai,
                kedudukan_pegawai,
                id_unit_kerja
            ")
            // ->active()
            ->whereUserHaveAccess('tb_01')
            ->orderBy('nama', 'asc')
            ->orderBy('tanggal_lahir', 'asc')
            ->paginate($per_page);
            
        return JsonResponseHandler::setResult($employee)->send();
    }

    public function store(EmployeeCreateRequest $request)
    {
        $payload = $request->all();
        $employee = $this->repository::create($payload);
        return JsonResponseHandler::setResult($employee)->send();
    }

    public function import(Request $request)
    {
        if (!$request->hasFile('csv_file')) {
            throw new AppException('csv file required');
        }
        if ($request->file('csv_file')->getClientOriginalExtension() != 'csv') {
            throw new AppException('Format file tidak sesuai. File yang diterima: CSV');
        }

        $path = $request->file('csv_file')->store('temp');

        // Read the CSV file
        $file = Storage::path($path);
        $header = null;

        $jobs = [];
        $importProcess = ImportProcessModel::create([
            'name' => 'Import Pegawai ' . date('Y-m-d H:i:s'),
            'remark' => date("Y-m-d H:i:s")
        ]);
        $importTotalCount = 0;
        if (($handle = fopen($file, 'r')) !== false) {

            $header = fgetcsv($handle, null, ';'); // Read the header row
            $requiredColumns = EmployeeModel::REQUIRED_IMPORT_COLUMNS;

            // Check if all required columns are present
            $missingColumns = array_diff($requiredColumns, $header);
            Log::debug($requiredColumns);
            Log::debug($header);
            Log::debug($missingColumns);
            if (!empty($missingColumns)) {
                throw new AppException('CSV file is missing required columns: ' . implode(', ', $missingColumns),);
            }

            // Process each row and dispatch a job
            while (($row = fgetcsv($handle, null, ';')) !== false) {
                Log::debug($row);
                if (empty(array_filter($row, 'trim'))) {
                    continue;
                }

                $data = array_combine($header, $row);
                $jobs[] = new EmployeeCreationJob($data, $importProcess->id);
                $importTotalCount += 1;
            }

            fclose($handle);
        }

        Storage::delete($path);

        Bus::chain($jobs)->dispatch();
        ImportProcessModel::where('id', $importProcess->id)->update([
            'import_count' => $importTotalCount
        ]);
        return JsonResponseHandler::setResult(['is_success' => true, 'message' => 'Import process will be queued'])->send();
    }

    // public function updateAvatar(Request $request, $employeeId)
    // {
    //     if (!($request->hasFile('photo'))) {
    //         return JsonResponseHandler::setResult(['photo' => 'No Photo Uploaded'])->send();
    //     }

    //     $payload['photo'] = FileHandler::store($request->file('photo'), EmployeeModel::PHOTO_DIR, $employeeId);
    //     $this->repository->update($employeeId, $payload);

    //     $appUrl = URL::to('/');
    //     return JsonResponseHandler::setResult(['photo' => "{$appUrl}/api/employee/{$employeeId}/photo"])->send();
    // }

    // public function showAvatar($id)
    // {
    //     $employee = EmployeeRepository::get($id);
    //     if ($employee->photo != null) {
    //         return Storage::download($employee->photo);
    //     }

    //     $epsEmployeePhoto = EmployeePhotoEps::where('NIP', $employee->nip)
    //         ->orWhere('NIP', $employee->nip_lama)
    //         ->where('NIP', '!=', '')
    //         ->first();
    //     if (!empty($epsEmployeePhoto)) {
    //         // Log::debug("masok foto eps");
    //         return response($epsEmployeePhoto->DATA)
    //             ->header('Content-Type', 'image/jpeg');
    //     }
    //     return Storage::download('employee/photo/default.jpeg');
    // }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        foreach ($payload as $key => $data) {
            if (str_contains($key, 'efile') && $request->hasFile($key)) {
                FileHandler::validateEfile($data, $key);
            }
        }

        $fileMappings = [
            'nomor_taspen_efile' => EmployeeModel::TASPEN_EFILE_DIR,
            'nomor_akta_lahir_efile' => EmployeeModel::AKTA_EFILE_DIR,
            'nomor_kk_efile' => EmployeeModel::KK_EFILE_DIR,
            'nik_efile' => EmployeeModel::NIK_EFILE_DIR,
            'nomor_npwp_efile' => EmployeeModel::NPWP_EFILE_DIR,
            'nomor_karis_karsu_efile' => EmployeeModel::KARISU_EFILE_DIR,
            'nomor_bpjs_efile' => EmployeeModel::BPJS_EFILE_DIR,
            'nomor_kartu_asn_efile' => EmployeeModel::KARPEG_EFILE_DIR,
            'status_pernikahan_efile' => EmployeeModel::NIKAH_EFILE_DIR,
            'nip_lama_efile' => EmployeeModel::NIPLAMA_EFILE_DIR,
            'skumptk_efile' => EmployeeModel::SKUMPTK_EFILE_DIR,
            'surat_tidak_pindah_efile' => EmployeeModel::SURAT_TIDAK_PINDAH_EFILE_DIR
        ];

        $employee = EmployeeModel::where('id', $id)->first();
        if (empty($employee)) {
            throw new AppException("Employee Not Found");
        }
        $nip = $employee->nip;
        foreach ($fileMappings as $fileKey => $directory) {
            if ($request->hasFile($fileKey)) {
                Log::debug($fileKey);
                $payload[$fileKey] = FileHandler::store(
                    $request->file($fileKey),
                    $directory,
                    "Dokumen_{$nip}_Submission_" . date("Y_m_d_His")
                );
                // Log::debug('ini store file ' . $payload[$fileKey]);
            }
        }
        $employee = EmployeeRepository::update($id, $payload);

        return JsonResponseHandler::setResult($employee)->send();
    }

    public function printPdfEmployee($employee_id)
    {
        $employee = EmployeeModel::with([
                'gender', 
                'religion', 
                'maritalStatus', 
                'jenisKepegawaian', 
                'unitKerja', 
                'subunitKerja', 
                'subSubUnitKerja',
                'jenkepeg', 
                'pendudupeg'
            ])
            ->selectRaw("
                *,
                concat(
                    gelar_depan,
                    IF(LENGTH(gelar_depan) > 0, '. ', ''),
                    nama,
                    IF(LENGTH(gelar_belakang) > 0, ', ', ''),
                    gelar_belakang
                ) as nama
            ")
            ->where('id', $employee_id)
            ->first();

        $jabatanTerakhir = RiwayatJabatanModel::where('id_pegawai', $employee_id)
            ->where('isakhir', 1)
            ->with([
                'jenisJabatan', 
                'penetap', 
                'jabatanStruktural', 
                'jabatanTertentu', 
                'jabatanTertentu', 
                'eselon'
            ]
            )->first();

        $cpns = RiwayatCpnsModel::where('id_pegawai', $employee_id)
            ->with(['golruCpns'])
            ->first();

        $pns = RiwayatPnsModel::where('id_pegawai', $employee_id)
            ->with(['golruPns'])
            ->first();

        $nilaiAngkaKredit = RiwayatAngkaKreditModel::where('id_pegawai', $employee_id)
            ->pluck('nilai_angka_kredit')
            ->first();

        $kepangkatan = RiwayatKepangkatanModel::where('id_pegawai', $employee_id)
            ->with(['penetap', 'golru'])
            ->where('isakhir', 1)
            ->first();

        $pendidikanTerakhir = RiwayatPendidikanModel::where('id_pegawai', $employee_id)
            ->where('isakhir', 1)
            ->first();

        $kgbTerakhir = RiwayatKgbModel::selectRaw("
                *, 
                year(tmt_sk) as tahun_kgb,
                concat(
                    masa_kerja_tahun,
                    ' tahun ',
                    masa_kerja_bulan,
                    ' bulan'
                ) as masa_kerja_concated
            ")
            ->where('id_pegawai', $employee_id)
            ->where('isakhir', 1)
            ->first();

        $employee->jabatanTerakhir = $jabatanTerakhir;
        $employee->cpns = $cpns;
        $employee->pns = $pns;
        $employee->nilaiAngkaKredit = $nilaiAngkaKredit;
        $employee->kepangkatan = $kepangkatan;
        $employee->pendidikanTerakhir = $pendidikanTerakhir;
        $employee->kgbTerakhir = $kgbTerakhir;

        $rjab = RiwayatJabatanModel::select(
                'r_jabatan.*', 
                'a_instansi_induk.instansi_induk',
                'a_skpd.path as skpd', 
                'a_skpd.id_bkn as id_bkn_skpd', 
                'a_jabfung.idsapk as id_bkn_jabfung', 
                'a_jabstruk.id_bkn as id_bkn_jabstruk', 
                'a_jabfungum.idsapk as id_bkn_jabfungum',
            )
            ->where('id_pegawai', $employee_id)
            ->leftJoin('a_instansi_induk', function ($join) {
                $join->on('a_instansi_induk.id', '=', 'r_jabatan.id_instansi_induk');
            })
            ->leftJoin('a_skpd', function ($join) {
                $join->on('a_skpd.id_unit_kerja', '=', 'r_jabatan.id_unit_kerja')
                    ->on('a_skpd.id_induk_upt', '=', 'r_jabatan.id_induk_upt')
                    ->on('a_skpd.id_sub_unit', '=', 'r_jabatan.id_sub_unit')
                    ->on('a_skpd.id_sub_sub_unit', '=', 'r_jabatan.id_sub_sub_unit')
                    ->on('a_skpd.id_sub_sub_sub_unit', '=', 'r_jabatan.id_sub_sub_sub_unit');
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join->on('a_jabfung.idjabfung', '=', 'r_jabatan.id_jabatan');
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join->on('a_jabfungum.idjabfungum', '=', 'r_jabatan.id_jabatan');
            })
            ->leftJoin('a_skpd as a_jabstruk', function ($join) {
                $join->on('a_jabstruk.idskpd', '=', 'r_jabatan.id_jabatan');
            })
            ->get();

        $rpangkat = RiwayatKepangkatanModel::where('id_pegawai', $employee_id)
            ->with(['penetap', 'golru'])
            ->get();
        
        $rkgb = RiwayatKgbModel::where('id_pegawai', $employee_id)
            ->with(['golru'])
            ->get();

        $rpendidikan_formal = RiwayatPendidikanModel::leftJoin(
                'a_tkpendid',
                'a_tkpendid.idtkpendid', '=', 'r_pendidikan_formal.id_jenjang'
            )
            ->select('a_tkpendid.*', 'r_pendidikan_formal.*')
            ->where('r_pendidikan_formal.id_pegawai', $employee_id)
            ->orderBy('a_tkpendid.tingkat', 'asc')
            ->orderBy('r_pendidikan_formal.ijazah_tanggal', 'asc')
            ->get();

        $rpendidikan_non_formal = RiwayatPendidikanNonFormalModel::where('id_pegawai', $employee_id)->get();
        $rtandajasa = RiwayatTandaJasaModel::where('id_pegawai', $employee_id)->with(['jenis'])->get();
        $rbahasa = RiwayatPenguasaanBahasaModel::where('id_pegawai', $employee_id)->get();
        $rhukuman_disiplin = RiwayatHukumDisiplinModel::where('id_pegawai', $employee_id)->with(['jenisHukum', 'tingkat'])->get();
        $rangka_kredit = RiwayatAngkaKreditModel::where('id_pegawai', $employee_id)->get();

        $ranak = KeluargaAnakModel::where('id_pegawai', $employee_id)->get();
        $rpasangan = KeluargaIstriSuamiModel::where('id_pegawai', $employee_id)->get();
        $rkerabat = KeluargaKekerabatanModel::where('id_pegawai', $employee_id)->get();
        $rortu = KeluargaOrangTuaModel::where('id_pegawai', $employee_id)->get();
        $rmertua = KeluargaMertuaModel::where('id_pegawai', $employee_id)
            ->with(['pasangan', 'pekerjaanDetail'])
            ->get();

        $photoDir = PhotoRepository::PROFILE_PHOTO_DIR . '/' . $employee_id;
        $profilePhoto = [];

        if (Storage::exists($photoDir)) {
            $profilePhoto = [
                'base64' => base64_encode(Storage::get($photoDir)),
                'mime' => Storage::mimeType($photoDir)
            ];
        }

        $pdf = PDF::loadView(
            'Employee::print_pdf',
            [
                'item' => $employee,
                'riwayatJabatan' => $rjab,
                'riwayatPangkat' => $rpangkat,
                'riwayatKgb' => $rkgb,
                'riwayatPendidikanFormal' => $rpendidikan_formal,
                'riwayatPendidikanNonFormal' => $rpendidikan_non_formal,
                'rtandajasa' => $rtandajasa,
                'rbahasa' => $rbahasa,
                'rhukuman_disiplin' => $rhukuman_disiplin,
                'rangka_kredit' => $rangka_kredit,
                'riwayatAnak' => $ranak,
                'riwayatPasangan' => $rpasangan,
                'rkerabat' => $rkerabat,
                'rortu' => $rortu,
                'rmertua' => $rmertua,
                'profilePhoto' => $profilePhoto
            ]
        );
        return $pdf->download('employeePrint.pdf');
    }

    // fungsi untuk menampilkan hukdis
    public function checkStatusHukdis($employeeId) 
    {
        $is_hukdis = (bool) RiwayatHukumDisiplinModel::where('id_pegawai', $employeeId)
            ->whereRaw('now() between tanggal_mulai and tanggal_selesai')
            ->exists();

        return JsonResponseHandler::setResult(compact('is_hukdis'))->send();
    }

    // public function detailStatusHukdis($employee_id)
    // {
    //     $employees = RiwayatHukumDisiplinModel::where('id_pegawai', $employee_id)
    //         ->with(['jenisHukum', 'tingkat'])
    //         ->get();

    //     $status = 0;
    //     foreach ($employees as $employee) {
    //         $tanggalMulai = $employee->tanggal_mulai;
    //         $tanggalSelesai = $employee->tanggal_selesai;
    //         $tanggalSekarang = date('Y-m-d');

    //         if ($tanggalSekarang >= $tanggalMulai && $tanggalSekarang <= $tanggalSelesai) {
    //             // $status="Sedang menjalani hukuman disiplin ".$jenis_hukuman;
    //             $status = 1;
    //             break;
    //             // $color = "red";
    //             // $background = "yellow";
    //         } else {
    //             // $status = 'Tidak sedang menjalani hukuman disiplin';
    //             $status = 0;
    //             // $color = "green";
    //             // $background = "white";
    //         }
    //     }

    //     return response()->json(['status' => $status]);
    // }

    public function submissionStatus(Request $request, $employeeId)
    {
        $submissionStatus = PerubahanData::where('id_pegawai', $employeeId)
            ->where('status', 0)
            ->select('id')
            ->first();

        return JsonResponseHandler::setResult(['submissionId' => !empty($submissionStatus) ? $submissionStatus->id : null])->send();
    }
    public function emergencyname($employeeId, $hubunganId)
    {
        // Mapping hubunganId ke model terkait
        $modelMapping = [
            1 => KeluargaOrangTuaModel::class,
            2 => KeluargaKekerabatanModel::class,
            3 => KeluargaIstriSuamiModel::class,
            4 => KeluargaAnakModel::class,
        ];

        // Validasi hubunganId
        if (!isset($modelMapping[$hubunganId])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid hubunganId provided.'
            ], 400);
        }

        // Dapatkan model berdasarkan hubunganId
        $model = $modelMapping[$hubunganId];

        // Query data berdasarkan id_pegawai
        try {
            $submissionStatus = $model::where('id_pegawai', $employeeId)->get();
            return JsonResponseHandler::setResult($submissionStatus)->send();
        } catch (\Exception $e) {
            // Tangani error jika query gagal
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch data.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
    public function emergencynumber($employeeId, $hubunganId, $familyId)
    {
        // Mapping hubunganId ke model terkait
        $modelMapping = [
            1 => KeluargaOrangTuaModel::class,
            2 => KeluargaKekerabatanModel::class,
            3 => KeluargaIstriSuamiModel::class,
            4 => KeluargaAnakModel::class,
        ];

        // Validasi hubunganId
        if (!array_key_exists($hubunganId, $modelMapping)) {
            throw new AppException('Invalid hubunganId provided.');
        }

        // Dapatkan model berdasarkan hubunganId
        $model = $modelMapping[$hubunganId];

        $family = $model::where('id', $familyId)->first();
        $phone = !empty($family) ? $family->nomor_hp : null;

        if (!empty($family) && !empty($family->pekerjaan_nip) && !empty($family->status_asn === 1)) {
            $employee = EmployeeModel::where('nip', $family->pekerjaan_nip)
                ->orWhere('nip_lama', $family->pekerjaan_nip)
                ->first();
            if (!empty($employee) && (!empty($employee->nomor_hp) || !empty($employee->nomor_telepon))) {
                $phone = !empty($employee->nomor_hp) ? $employee->nomor_hp : $employee->nomor_telepon;
            }
        }


        if (empty($family)) {
            throw new AppException("Data Keluarga tidak ditemukan");
        }

        return JsonResponseHandler::setResult($phone)->send();
    }
}
