<?php

namespace App\Modules\GolDarah\Repositories;

use App\Modules\GolDarah\Models\GolDarah;

class GolDarahRepository
{
    public static function datatable($per_page = 15)
    {
        $data = GolDarah::paginate($per_page);
        return $data;
    }
    public static function get($goldarah_id)
    {
        $goldarah = GolDarah::where('id', $goldarah_id)->first();
        return $goldarah;
    }
    public static function create($goldarah)
    {
        $goldarah = GolDarah::create($goldarah);
        return $goldarah;
    }

    public static function update($goldarah_id, $goldarah)
    {
        GolDarah::where('id', $goldarah_id)->update($goldarah);
        $goldarah = GolDarah::where('id', $goldarah_id)->first();
        return $goldarah;
    }

    public static function delete($goldarah_id)
    {
        $delete = GolDarah::where('id', $goldarah_id)->delete();
        return $delete;
    }
}
