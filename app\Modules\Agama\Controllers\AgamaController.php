<?php

namespace App\Modules\Agama\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Agama\Models\Agama;
use App\Modules\Agama\Repositories\AgamaRepository;
use App\Modules\Agama\Requests\AgamaCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class AgamaController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('Agama::index', ['permissions' => $permissions]);
    }

    public function get()
    {
        $data = Agama::get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = AgamaRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $agama_id)
    {
        $agama = Agama::where('id', $agama_id)->first();
        return JsonResponseHandler::setResult($agama)->send();
    }

    public function create()
    {
        return view('Agama::create');
    }

    public function store(AgamaCreateRequest $request)
    {
        $payload = $request->all();
        $agama = AgamaRepository::create($payload);
        return JsonResponseHandler::setResult($agama)->send();
    }

    public function show(Request $request, $id)
    {
        $agama = AgamaRepository::get($id);
        return JsonResponseHandler::setResult($agama)->send();
    }

    public function edit($id)
    {
        return view('Agama::edit', ['agama_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $agama = AgamaRepository::update($id, $payload);
        return JsonResponseHandler::setResult($agama)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = AgamaRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
