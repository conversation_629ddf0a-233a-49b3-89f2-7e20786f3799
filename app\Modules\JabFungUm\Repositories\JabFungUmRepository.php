<?php

namespace App\Modules\JabFungUm\Repositories;

use App\Exceptions\AppException;
use App\Modules\JabFungUm\Models\JabFungUm;
use Illuminate\Pagination\LengthAwarePaginator;

class JabFungUmRepository
{
    public static function datatable($per_page = 15, $keyword)
    {
        $data = JabFungUm::search($keyword)
            ->select(
                'idjabfungum as id',
                'jabfungum',
                'flag'
            )
            ->paginate($per_page);

        // $data = $originalData->items();
        // $data = array_map(function ($item) {
        //     $item->id = (string) $item->idjabfungum;
        //     return $item;
        // }, $data);
       
        // $data = new LengthAwarePaginator(
        //     $data,
        //     $originalData->total(),
        //     $originalData->perPage(),
        //     $originalData->currentPage(),
        //     ['path' => $originalData->path()]
        // );

        return $data;
    }
    public static function get($jabfungum_id)
    {
        $jabfungum = JabFungUm::where('id', $jabfungum_id)->first();
        return $jabfungum;
    }
    public static function create($payload)
    {
        if (!isset($payload['idjabfungum']) || empty($payload['idjabfungum'])) {
            $payload['idjabfungum'] = (int) JabfungUm::withTrashed()
                ->orderBy('idjabfungum', 'desc')
                ->first()
                ->idjabfungum;
            $payload['idjabfungum'] += 1;
        }
        else {
            $check = JabFungUm::withTrashed()
                ->where('idjabfungum', $payload['idjabfungum'])
                ->first();
            if (!empty($check)) {
                throw new AppException("ID sudah digunakan untuk jabatan <b>$check->jabfungum.</b>");
            }

        }
        $payload['flag'] = (int) isset($payload['flag']) && $payload['flag'] == true;
        $jabfungum = JabFungUm::create($payload);
        return $jabfungum;
    }

    public static function update($jabfungum_id, $payload)
    {
        $payload['flag'] = (int) isset($payload['flag']) && $payload['flag'] == true;
        JabFungUm::where('idjabfungum', $jabfungum_id)->update($payload);
        $jabfungum = JabFungUm::where('idjabfungum', $jabfungum_id)->first();
        return $jabfungum;
    }

    public static function delete($jabfungum_id)
    {
        $delete = JabFungUm::where('idjabfungum', $jabfungum_id)->delete();
        return $delete;
    }
}
