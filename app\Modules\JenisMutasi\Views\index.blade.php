@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jenis-mutasi">
    <default-datatable title="JenisMutasi" url="{!! url('jenis-mutasi') !!}" :headers="headers" :can-add="{{ $permissions['create-jenis_mutasi'] }}" :can-edit="{{ $permissions['update-jenis_mutasi'] }}" :can-delete="{{ $permissions['delete-jenis_mutasi'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jenis-mutasi');
</script>
@endsection