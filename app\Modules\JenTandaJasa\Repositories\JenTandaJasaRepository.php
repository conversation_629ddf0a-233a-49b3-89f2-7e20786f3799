<?php

namespace App\Modules\JenTandaJasa\Repositories;

use App\Modules\JenTandaJasa\Models\JenTandaJasaModel;

class JenTandaJasaRepository
{
    public static function datatable($per_page = 15)
    {
        $data =  JenTandaJasaModel::paginate($per_page);
        return $data;
    }
    public static function get($jeniskp_id)
    {
        $jeniskp = JenTandaJasaModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }
    public static function create($jeniskp)
    {
        $jeniskp = JenTandaJasaModel::create($jeniskp);
        return $jeniskp;
    }

    public static function update($jeniskp_id, $jeniskp)
    {
        JenTandaJasaModel::where('id', $jeniskp_id)->update($jeniskp);
        $jeniskp = JenTandaJasaModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }

    public static function delete($jeniskp_id)
    {
        $delete = JenTandaJasaModel::where('id', $jeniskp_id)->delete();
        return $delete;
    }
}
