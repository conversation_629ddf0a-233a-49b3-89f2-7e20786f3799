<?php

namespace App\Http\Middleware;

use App\Exceptions\AppException;
use App\Type\JsonResponseType;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */

    protected function unauthenticated($request, array $guards)
    {
        if (!$request->expectsJson()) {
            if (in_array('oauth', $guards)) {
                return route('user.login.sso');
            }
            if (in_array('api', $guards)) {
                throw new AppException("Unauthecticated", JsonResponseType::INTERNAL_SERVER_ERROR, [], 401);
                // return response()->json(['error' => 'Unauthorized'], 401);
            }
            return route('user.login');
        }
    }
}
