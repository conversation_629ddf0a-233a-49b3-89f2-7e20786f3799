<?php

namespace App\Console\Commands;

use App\Modules\Kepangkatan\Repositories\KepangkatanRepository;
use App\Modules\RiwayatKgb\Repositories\RiwayatKgbRepository;
use Illuminate\Console\Command;

// class SyngKgbToRKgb extends Command
// {
//     /**
//      * The name and signature of the console command.
//      *
//      * @var string
//      */
//     protected $signature = 'sync:simgaji_kgb';

//     /**
//      * The console command description.
//      *
//      * @var string
//      */
//     protected $description = 'Command description';

//     /**
//      * Execute the console command.
//      */
//     public function handle()
//     {
//         RiwayatKgbRepository::syncKgbFromSimGaji();
//     }
// }
