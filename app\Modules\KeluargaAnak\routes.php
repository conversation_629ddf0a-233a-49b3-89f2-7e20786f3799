<?php

namespace App\Modules\KeluargaAnak;

use App\Modules\KeluargaAnak\Controllers\KeluargaAnakController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/anak')->group(function () {
    Route::prefix('/submission')->group(function () {
        Route::withoutMiddleware(['deny.pegawai'])->group(function () {
            Route::get('/datatable', [KeluargaAnakController::class, 'submissionDatatable']);
            Route::get('/{perubahan_data_id}/field', [KeluargaAnakController::class, 'fieldDetail']);
            Route::get('/{perubahan_data_id}/timeline', [KeluargaAnakController::class, 'timeline']);
        });

        Route::get('/{submissionId}/detail', [KeluargaAnakController::class, 'submissionDetail']);
        Route::post('/{submissionId}/approve', [KeluargaAnakController::class, 'submissionApprove']);
        Route::put('/{perubahan_data_id}/field', [KeluargaAnakController::class, 'updateField']);
        Route::post('/{perubahan_data_id}/process', [KeluargaAnakController::class, 'processSubmission']);
    });
});
