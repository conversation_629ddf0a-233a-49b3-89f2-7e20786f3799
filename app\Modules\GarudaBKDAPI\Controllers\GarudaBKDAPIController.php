<?php

namespace App\Modules\GarudaBKDAPI\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\GarudaBKDAPI\Repositories\GarudaBKDAPIRepository;
use App\Modules\Employee\Model\EmployeeModel;
use App\Handler\JsonResponseHandler;
use App\Type\JsonResponseType;

class GarudaBKDAPIController extends Controller
{
    private $repository;

    public function __construct()
    {
        $this->repository = new GarudaBKDAPIRepository();
    }

    public function getTunjanganKeluarga($nip)
    {
        $isUuid = (bool) preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i', $nip);
        // return response()->json($isUuid);
        if ($isUuid) {
            $employee = EmployeeModel::find($nip);
            if (empty($employee))   {
                return JsonResponseHandler::setMessage('Data pegawai tidak ditemukan')
                    ->setStatus(404)
                    ->setCode(JsonResponseType::NOT_FOUND)
                    ->send();
            }  
            $nip = $employee->nip;
        }
        $data = $this->repository->getTunjanganKeluarga($nip);

        if (!((bool) $data['status'])) {
            return JsonResponseHandler::setMessage($data['message'])
                    ->setStatus(404)
                    ->setCode(JsonResponseType::NOT_FOUND)
                    ->send();
        }

        return JsonResponseHandler::setMessage($data['message'])
            ->setResult($data['data'])
            ->send();
    }
}
