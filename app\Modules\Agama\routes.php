<?php
namespace App\Modules\Agama;

use App\Modules\Agama\Controllers\AgamaController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/agama')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [AgamaController::class, 'get'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/datatable', [AgamaController::class, 'datatable']);
    Route::get('/create', [AgamaController::class, 'create'])->middleware('authorize:create-agama');
    Route::post('/', [AgamaController::class, 'store'])->middleware('authorize:create-agama');
    Route::get('/{agama_id}', [AgamaController::class, 'show'])->middleware('authorize:read-agama');
    Route::get('/{agama_id}/detail', [AgamaController::class, 'detail'])->middleware('authorize:read-agama');
    Route::get('/{agama_id}/edit', [AgamaController::class, 'edit'])->middleware('authorize:update-agama');
    Route::put('/{agama_id}', [AgamaController::class, 'update'])->middleware('authorize:update-agama');
    Route::patch('/{agama_id}', [AgamaController::class, 'update'])->middleware('authorize:update-agama');
    Route::delete('/{agama_id}', [AgamaController::class, 'destroy'])->middleware('authorize:delete-agama');
});