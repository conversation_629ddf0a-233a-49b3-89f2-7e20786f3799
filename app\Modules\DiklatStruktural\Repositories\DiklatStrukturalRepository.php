<?php

namespace App\Modules\DiklatStruktural\Repositories;

use App\Modules\DiklatStruktural\Models\DiklatStruktural;

class DiklatStrukturalRepository
{
    public static function datatable($per_page = 15)
    {
        $data = DiklatStruktural::select(
            'iddikstru as id', 'dikstru', 'dikstrusingkat', 'idsapk'
        )->paginate($per_page);
        return $data;
    }
    public static function get($diklat_struktural_id)
    {
        $diklat_struktural = DiklatStruktural::where('iddikstru', $diklat_struktural_id)->first();
        return $diklat_struktural;
    }
    public static function create($diklat_struktural)
    {
        $latest_dikstru_id = DiklatStruktural::pluck('iddikstru')->toArray();
        $diklat_struktural['iddikstru'] = max($latest_dikstru_id) + 1;

        $diklat_struktural = DiklatStruktural::insert($diklat_struktural);
        return $diklat_struktural;
    }

    public static function update($diklat_struktural_id, $diklat_struktural)
    {
        DiklatStruktural::where('iddikstru', $diklat_struktural_id)->update($diklat_struktural);
        $diklat_struktural = DiklatStruktural::where('iddikstru', $diklat_struktural_id)->first();
        return $diklat_struktural;
    }

    public static function delete($diklat_struktural_id)
    {
        $delete = DiklatStruktural::where('iddikstru', $diklat_struktural_id)->delete();
        return $delete;
    }
}
