<?php

namespace App\Modules\ExecutiveSummary\Constants;

class ValidityConstant
{
    const PERSONAL_FIELD_TO_CHECK = [
        'nip' => 'NIP Baru',
        'nip_lama' => 'N<PERSON> Lama',
        'nama' => '<PERSON>a <PERSON>',
        'nama_ijazah' => '<PERSON>a <PERSON>',
        'gelar_depan' => 'G<PERSON>r Depan',
        'gelar_belakang' => 'Gelar Belakang',
        'tempat_lahir' => 'Tempat Lahir',
        'tanggal_lahir' => 'Tanggal Lahir',
        'jenis_kelamin' => '<PERSON><PERSON>',
        'golongan_darah' => 'Golongan Darah',
        'agama' => 'Agama',
        'email' => 'Email Pribadi',
        'email_instansi' => 'Email <PERSON> (go.id)',
        'status_pernikahan' => 'Status Pernikahan',
        'nomor_kartu_asn' => 'No Kartu ASN',
        'domisili_alamat' => '<PERSON><PERSON>t Domisili',
        'domisili_rt' => 'Nama <PERSON> / RT /RW',
        'domisili_long' => 'Koordinat',
        'domisili_kelurahan' => 'Kelurahan / Desa',
        'domisili_kecamatan' => 'Kecamatan',
        'domisili_kabupaten' => 'Kabupaten',
        'domisili_provinsi' => 'Provinsi',
        'domisili_kode_pos' => 'Kode Pos',
        'ktp_alamat' => 'Alamat sesuai KTP',
        'ktp_rt' => 'Nama Jalan / RT /RW',
        'ktp_long' => 'Koordinat',
        'ktp_kelurahan' => 'Kelurahan / Desa',
        'ktp_kecamatan' => 'Kecamatan',
        'ktp_kabupaten' => 'Kabupaten',
        'ktp_provinsi' => 'Provinsi',
        'ktp_kode_pos' => 'Kode Pos',
        'nomor_hp' => 'No HP',
        'nomor_telepon' => 'No Telepon',
        'sosial_media' => 'Sosial Media',
        'emergensi_nomor' => 'No Kontak Emergensi',
        'emergensi_hubungan' => 'Hubungan',
        'emergensi_nama' => 'Nama',
        'nomor_kk' => 'Nomor KK',
        'nik' => 'NIK',
        'nomor_akta_lahir' => 'Nomor Akta Kelahiran ybs',
        'tanggal_akta_lahir' => 'Tanggal Akta Kelahiran ybs',
        'nomor_bpjs' => 'BPJS',
        'nomor_npwp' => 'NPWP',
        'nomor_taspen' => 'Taspen'
    ];

    const PASANGAN_FIELD_TO_CHECK = [
        'status_pasangan' => 'Suami/Istri',
        'pernikahan_ke' => 'Pernikahan ke',
        'nama_pasangan' => 'Nama Pasangan',
        'tempat_lahir' => 'Tempat Lahir',
        'tanggal_lahir' => 'Tanggal Lahir',
        'alamat' => 'Alamat',
        'alamat_rt,alamat_rw' => 'RT /RW',
        // 'alamat_lat,alamat_long,' => 'Koordinat',
        'alamat_kelurahan' => 'Kelurahan / Desa',
        'alamat_kecamatan' => 'Kecamatan',
        'alamat_kabupaten' => 'Kabupaten',
        'alamat_provinsi' => 'Provinsi',
        'alamat_kode_pos' => 'Kode pos',
        'status_perkawinan' => 'Status Perkawinan [Menikah, Janda / Duda]',
        'nik' => 'NIK',
        'pekerjaan' => 'Pekerjaan',
        'status_tunjangan' => 'Status Tunjangan [Dapat / Tidak]',
        'karis_karsu' => 'Karis / Karsu',
        'nomor_hp' => 'NO HP Pasangan',
        'nomor_bpjs' => 'BPJS',
        'nomor_akta_lahir' => 'Nomor Akta Kelahiran',
        'tanggal_akta_lahir' => 'Tanggal Akta Kelahiran'
    ];

    const ANAK_FIELD_TO_CHECK = [
        'nama_anak' => 'Nama Anak',
        'tempat_lahir' => 'Tempat Lahir',
        'tanggal_lahir' => 'Tanggal Lahir',
        'status_anak' => 'Status Anak',
        'alamat' => 'Alamat',
        'alamat_rt,alamat_rw' => 'RT /RW',
        // 'alamat_lat,alamat_long,' => 'Koordinat',
        'alamat_kelurahan' => 'Kelurahan / Desa',
        'alamat_kecamatan' => 'Kecamatan',
        'alamat_kabupaten' => 'Kabupaten',
        'alamat_provinsi' => 'Provinsi',
        'alamat_kode_pos' => 'Kode pos',
        'status_tunjangan' => 'Status Tunjangan [Dapat / Tidak]',
        'karis_karsu' => 'Status Perkawinan Anak',
        'nomor_bpjs' => 'BPJS',
        'nomor_akta_lahir' => 'Nomor Akta Kelahiran',
        'tanggal_akta_lahir' => 'Tanggal Akta Kelahiran',
        'status_hidup' => 'Status Hidup',
        'pekerjaan_status' => 'Status Pekerjaan'
    ];

    const ORTU_FIELD_TO_CHECK = [
        'nama_orangtua' => 'Nama',
        'tempat_lahir' => 'Tempat Lahir',
        'tanggal_lahir' => 'Tanggal Lahir',
        'alamat' => 'Alamat',
        'alamat_rt,alamat_rw' => 'RT /RW',
        // 'alamat_lat,alamat_long,' => 'Koordinat',
        'alamat_kelurahan' => 'Kelurahan / Desa',
        'alamat_kecamatan' => 'Kecamatan',
        'alamat_kabupaten' => 'Kabupaten',
        'alamat_provinsi' => 'Provinsi',
        'alamat_kode_pos' => 'Kode pos',
        'status_perkawinan' => 'Status Perkawinan',
        'nik' => 'NIK',
        'pekerjaan_status' => 'Status Pekerjaan',
        'nomor_hp' => 'No HP',
        'status_hidup' => 'Status Hidup'
    ];

    const MERTUA_FIELD_TO_CHECK = [
        'nama_mertua' => 'Nama',
        'tempat_lahir' => 'Tempat Lahir',
        'tanggal_lahir' => 'Tanggal Lahir',
        'alamat' => 'Alamat',
        'alamat_rt,alamat_rw' => 'RT /RW',
        // 'alamat_lat,alamat_long,' => 'Koordinat',
        'alamat_kelurahan' => 'Kelurahan / Desa',
        'alamat_kecamatan' => 'Kecamatan',
        'alamat_kabupaten' => 'Kabupaten',
        'alamat_provinsi' => 'Provinsi',
        'alamat_kode_pos' => 'Kode Pos',
        'status_perkawinan' => 'Status Perkawinan',
        'nik' => 'NIK',
        'pekerjaan_status' => 'Status Pekerjaan',
        'nomor_hp' => 'No HP',
        'status_hidup' => 'Status Hidup'
    ];

    const CPNS_FIELD_TO_CHECK = [
        'tanggal_sk_cpns' => 'Tanggal SK CPNS',
        'tmt_cpns' => 'TMT CPNS',
        'nomor_sk_cpns' => 'Nomor SK CPNS',
        'id_golongan_ruang' => 'Golongan / Ruang',
        'masa_kerja_tahun' => 'Masa Kerja',
        'rekomtek_cpns' => 'Rekomtek CPNS',
        'tmt_spmt' => 'TMT SPMT',
        'nomor_spmt' => 'Nomor SPMT',
        'tanggal_spmt' => 'Tanggal SPMT',
        'nomor_sttpl' => 'Nomor STTPL',
        'tanggal_sttpl' => 'Tanggal STTPL',
        'nomor_ujikes' => 'Nomor Surat Keterangan Tim Penguji Kesehatan',
        'tanggal_ujikes' => 'Tanggal Surat Keterangan Tim Penguji Kesehatan',
        'nomor_c2th' => 'Nomor Surat Pertek C2TH',
        'tanggal_c2th' => 'Tanggal Surat Pertek C2TH'
    ];

    const PNS_FIELD_TO_CHECK = [
        'tanggal_sk_pns' => 'Tanggal SK PNS',
        'tmt_pns' => 'TMT PNS',
        'nomor_sk_pns' => 'Nomor SK PNS',
        'id_golongan_ruang' => 'Golongan / Ruang',
        'masa_kerja_tahun' => 'Masa Kerja',
        'sumpah_janji' => 'Sumpah Janji',
        'nomor_berita_acara' => 'Nomor Berita Acara',
        'tanggal_berita_acara' => 'Tanggal Berita Acara'
    ];

    const PPPK_FIELD_TO_CHECK = [
        'tanggal_sk_pppk' => 'Tanggal SK PPPK',
        'tmt_sk_pppk' => 'TMT PPPK',
        'nomor_sk_pppk' => 'Nomor SK PPPK',
        'id_golongan_ruang' => 'Golongan / Ruang',
        'tmt_kontrak_awal' => 'TMT Awal Kontrak',
        'tmt_kontrak_akhir' => 'TMT Akhir Kontrak',
        'spmt_tmt' => 'TMT SPMT',
        'spmt_tanggal' => 'Tanggal SPMT',
        'spmt_nomor' => 'Nomor SPMT',
        'masa_kerja_tahun' => 'Masa Kerja'
    ];

    const JABATAN_FIELD_TO_CHECK = [
        'id_jenis_jabatan' => 'Jenis Jabatan',
        'id_unit_kerja' => 'Unit Kerja',
        'id_sub_unit' => 'Sub Unit Kerja',
        'id_sub_sub_unit' => 'Sub Sub Unit Kerja',
        'jabatan' => 'Nama Jabatan',
        'id_sub_jabatan' => 'Sub Jabatan',
        'tanggal_sk' => 'Tanggal SK',
        'nomor_sk' => 'Nomor SK',
        'tmt_jabatan' => 'TMT Jabatan',
        'id_pak' => 'PAK',
        'nomor_ba_jabatan' => 'Nomor BA Jabatan',
        'tanggal_ba_jabatan' => 'Tanggal BA Jabatan',
        'tmt_pelantikan' => 'TMT Pelantikan',
        'nomor_spp' => 'Nomor SPP',
        'tanggal_spp' => 'Tanggal SPP',
        'id_penugasan' => 'Penugasan'
    ];

    const PENDIDIKAN_FORMAL_FIELD_TO_CHECK = [
        'id_jenjang' => 'Jenjang (SD, SMP, SMA, Pendidikan Pengangkatan s/d Pendidikan Terakhir)',
        'fakultas' => 'Fakultas',
        'id_jurusan' => 'Jurusan',
        'spesialisasi' => 'Spesialisasi',
        'sub_spesialisasi' => 'Sub Spesialisasi',
        'nama_sekolah' => 'Nama Sekolah / Universitas',
        'tahun_lulus' => 'Tahun Lulus',
        'nama_kepsek' => 'Nama Kepala Sekolah / Rektor',
        'ijazah_nomor' => 'Nomor Ijazah',
        'ijazah_tanggal' => 'Tanggal Ijazah',
        'transkrip_nomor' => 'Nomor Transkrip',
        'transkrip_tanggal' => 'Tanggal Transkrip',
        'akreditasi' => 'Akreditasi',
        'gelar_depan' => 'Gelar Depan',
        'gelar_belakang' => 'Gelar Belakang',
        'isawal' => 'Pendidikan Pengangkatan',
        'isakhir' => 'Pendidikan Terakhir'
    ];

    const PENDIDIKAN_NON_FORMAL_FIELD_TO_CHECK = [
        'id_jenis_diklat' => 'Jenis Diklat',
        'penyelenggara' => 'Penyelenggara',
        'tempat_diklat' => 'Tempat Diklat',
        'nama_diklat' => 'Nama Diklat',
        'tanggal_mulai' => 'Tanggal Mulai',
        'tanggal_selesai' => 'Tanggal Selesai',
        'nomor_sertifikat' => 'Nomor Sertifikat',
        'tahun_diklat' => 'Tahun Diklat',
        'jumlah_jam' => 'Jumlah JP',
        'rumpun_diklat' => 'Rumpun Diklat'
    ];

    const PANGKAT_FIELD_TO_CHECK = [
        'id_kepangkatan' => 'Golongan',
        'jenis_kp' => 'Jenis KP',
        'masa_kerja_tahun' => 'Masa Kerja',
        'nomor_pertek' => 'Nomor Pertek BKN',
        'tanggal_pertek' => 'Tanggal Pertek',
        'nomor_sk' => 'Nomor SK',
        'tanggal_sk' => 'Tanggal SK',
        'tmt_sk' => 'TMT SK'
    ];

    const KGB_FIELD_TO_CHECK = [
        'nomor_sk' => 'No SK',
        'tanggal_sk' => 'Tanggal SK',
        'tmt_sk' => 'TMT SK',
        'id_golongan_ruang' => 'Golongan Ruang',
        'masa_kerja_tahun' => 'Masa Kerja',
        'gaji' => 'Gaji Pokok'
    ];

    const EKIN_FIELD_TO_CHECK = [
        'tahun' => 'Periode Ekin',
        'capaian_kinerja' => 'Nilai Ekin'
    ];

    const PERILAKU_FIELD_TO_CHECK = [
        'tahun' => 'Periode PKSP',
        'rating_perilaku' => 'Nilai PKSP'
    ];

}
