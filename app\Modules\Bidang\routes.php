<?php

namespace App\Modules\Bidang;

use App\Modules\Bidang\Controllers\BidangController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/bidang')->group(function () {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [BidangController::class, 'index'])->middleware('authorize:read-bidang');
    Route::get('/datatable', [BidangController::class, 'datatable'])->middleware('authorize:read-bidang');
    Route::get('/all', [BidangController::class, 'all']);
    Route::get('/{bidangId}/services/datatable', [BidangController::class, 'servicesDatatable'])->middleware('authorize:read-bidang');
    Route::post('/{bidangId}/services', [BidangController::class, 'serviceAdd'])->middleware('authorize:read-bidang');
    Route::delete('/{bidangId}/services/{itemId}', [BidangController::class, 'serviceDelete'])->middleware('authorize:read-bidang');
    Route::get('/create', [BidangController::class, 'create'])->middleware('authorize:create-bidang');
    Route::post('/', [BidangController::class, 'store'])->middleware('authorize:create-bidang');
    Route::get('/{bidang_id}', [BidangController::class, 'show'])->middleware('authorize:read-bidang');
    Route::get('/{bidang_id}/edit', [BidangController::class, 'edit'])->middleware('authorize:update-bidang');
    Route::put('/{bidang_id}', [BidangController::class, 'update'])->middleware('authorize:update-bidang');
    Route::delete('/{bidang_id}', [BidangController::class, 'destroy'])->middleware('authorize:delete-bidang');
});
