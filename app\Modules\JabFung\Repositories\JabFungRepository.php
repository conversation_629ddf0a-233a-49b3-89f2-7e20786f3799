<?php

namespace App\Modules\JabFung\Repositories;

use App\Modules\JabFung\Models\JabFung;

class JabFungRepository
{
    public static function datatable($per_page = 15)
    {
        $data = JabFung::select(
            'idjabfung as id',
            'jabfung',
            'jenjang',
            'jenjang2',
            'tingkat',
            'tingkat2',
            'jabfung2',
            'kelas_jabatan'
        )->paginate($per_page);
        return $data;
    }
    public static function get($jabfung_id)
    {
        $jabfung = JabFung::where('id', $jabfung_id)->first();
        return $jabfung;
    }
    public static function create($jabfung)
    {
        $jabfung = JabFung::create($jabfung);
        return $jabfung;
    }

    public static function update($jabfung_id, $jabfung)
    {
        JabFung::where('id', $jabfung_id)->update($jabfung);
        $jabfung = JabFung::where('id', $jabfung_id)->first();
        return $jabfung;
    }

    public static function delete($jabfung_id)
    {
        $delete = JabFung::where('id', $jabfung_id)->delete();
        return $delete;
    }
}
