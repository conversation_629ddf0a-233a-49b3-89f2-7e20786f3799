<?php

namespace App\Modules\JenHukDis\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenHukDis\Models\JenHukDisModel;
use App\Modules\JenHukDis\Repositories\JenHukDisRepository;
// use App\Modules\JenHukDis\Requests\JenHukDisCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenHukDisController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenHukDisRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function index(Request $request)
    {
        $permissions = JenHukDisModel::get();
        return JsonResponseHandler::setResult($permissions)->send();

    }
}
