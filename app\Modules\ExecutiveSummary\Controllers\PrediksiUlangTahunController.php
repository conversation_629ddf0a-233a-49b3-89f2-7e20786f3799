<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PrediksiUlangTahunController extends Controller
{
    public function datatable(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_waktu_ultah = $request->input('id_waktu_ultah');

        $data = DB::table('tb_01')
            ->select(
                'tb_01.id',
                'tb_01.nip',
                'tb_01.tanggal_lahir',
                'tb_01.id_unit_kerja',
                DB::raw('CONCAT(tb_01.gelar_depan,IF(LENGTH(tb_01.gelar_depan)>0," ",""),tb_01.nama,IF(LENGTH(tb_01.gelar_belakang)>0,", ",""),tb_01.gelar_belakang) as nama'),
                DB::raw("
                        DATE_FORMAT(
                            FROM_DAYS(
                                TO_DAYS(NOW()) - TO_DAYS(tb_01.tanggal_lahir)
                            ),
                            '%Y'
                        ) + 0 AS usia
                    "),
            )
            ->whereNotNull('tb_01.tanggal_lahir')
            ->whereNotIn('tb_01.kedudukan_pegawai', [99, 21])
            ->where(function ($query) {
                return (new EmployeeModel())->scopeWhereUserHaveAccess($query, 'tb_01.id_unit_kerja');
            });

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                    ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                    ->where('tb_01.id_sub_unit', $pecah_sub_unit)
                    ->where('tb_01.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('tb_01.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                    ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                    ->where('tb_01.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('status_pegawai', [$status_pegawai]);
        }

        if ($id_waktu_ultah != null && $id_waktu_ultah != "") {
            if ($id_waktu_ultah < 0) {
                $data = $data->whereRaw("RIGHT(tb_01.tanggal_lahir, 5)=RIGHT(DATE_ADD(CURDATE(), INTERVAL " . $id_waktu_ultah . " DAY), 5)");
            } else if ($id_waktu_ultah > 5) {
                $data = $data->whereRaw("MID(tb_01.tanggal_lahir, 6, 2)=MID(DATE_ADD(CURDATE(), INTERVAL +" . substr($id_waktu_ultah, 1, 1) . " MONTH), 6, 2)");
            } else {
                $data = $data->whereRaw("RIGHT(tb_01.tanggal_lahir, 5)=RIGHT(DATE_ADD(CURDATE(), INTERVAL +" . $id_waktu_ultah . " DAY), 5)");
            }
        } else {
            //default hari ini
            $data = $data->whereRaw("DAY(tb_01.tanggal_lahir) = DAY(CURDATE()) AND MONTH(tb_01.tanggal_lahir) = MONTH(CURDATE())");
        }

        $data = $data->orderBy('tb_01.tanggal_lahir', 'desc')->limit(10)->paginate();

        return JsonResponseHandler::setResult($data)->send();
    }
}
