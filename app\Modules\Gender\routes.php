<?php
namespace App\Modules\Gender;

use App\Modules\Gender\Controllers\GenderController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/gender')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [GenderController::class, 'get'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/datatable', [GenderController::class, 'datatable']);
    Route::get('/create', [GenderController::class, 'create'])->middleware('authorize:create-gender');
    Route::post('/', [GenderController::class, 'store'])->middleware('authorize:create-gender');
    Route::get('/{gender_id}', [GenderController::class, 'show'])->middleware('authorize:read-gender');
    Route::get('/{gender_id}/detail', [GenderController::class, 'detail'])->middleware('authorize:read-gender');
    Route::get('/{gender_id}/edit', [GenderController::class, 'edit'])->middleware('authorize:update-gender');
    Route::put('/{gender_id}', [GenderController::class, 'update'])->middleware('authorize:update-gender');
    Route::patch('/{gender_id}', [GenderController::class, 'update'])->middleware('authorize:update-gender');
    Route::delete('/{gender_id}', [GenderController::class, 'destroy'])->middleware('authorize:delete-gender');
});