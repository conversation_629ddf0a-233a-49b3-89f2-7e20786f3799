@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="eselon">
    <default-datatable title="Eselon" url="{!! url('eselon') !!}" :headers="headers" :can-add="{{ $permissions['create-eselon'] }}" :can-edit="{{ $permissions['update-eselon'] }}" :can-delete="{{ $permissions['delete-eselon'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'name',
        						text: 'name'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#eselon');
</script>
@endsection