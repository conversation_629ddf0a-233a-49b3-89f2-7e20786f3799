<?php

namespace App\Modules\ImportProcess\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\ImportProcess\Models\ImportProcessDetailModel;
use App\Modules\ImportProcess\Models\ImportProcessModel;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ImportProcessController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $import_process = ImportProcessModel::withCount([
            'details as successful_details_count' => function ($query) {
                $query->where('is_success', 1);
            },
            'details as unsuccessful_details_count' => function ($query) {
                $query->where('is_success', 0);
            }
        ])
            ->orderBy('id', 'DESC')
            ->paginate($per_page);

        return JsonResponseHandler::setResult($import_process)->send();
    }

    public function downloadError(Request $request, $id)
    {
        $detailData = ImportProcessDetailModel::where('import_id', $id)
            ->where('is_success', 0)
            ->get();

        $errorData = [];

        foreach ($detailData as $item) {
            $data = json_decode($item->data, true);
            $data['remark'] = $item->remark;
            $errorData[] = $data;
        }

        $response = new StreamedResponse(function () use ($errorData) {
            $handle = fopen('php://output', 'w');

            // Add the header row
            if (!empty($errorData)) {
                fputcsv($handle, array_keys($errorData[0]));
            }

            // Add the data rows
            foreach ($errorData as $row) {
                fputcsv($handle, $row);
            }

            fclose($handle);
        });

        // Set headers for the CSV download
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="data.csv"');

        return $response;
    }
}
