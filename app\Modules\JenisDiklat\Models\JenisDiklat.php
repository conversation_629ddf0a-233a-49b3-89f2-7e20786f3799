<?php

namespace App\Modules\JenisDiklat\Models;
    
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class JenisDiklat extends Model
{
    use SoftDeletes;
    protected $table = 'a_jenis_diklat';
    protected $guarded = [];
    public $timestamps = true;
    protected $appends = ['formatted_kategori'];

    protected function getFormattedKategoriAttribute()
    {
        switch($this->kategori) {
            case 1:
                return 'Klasikal';
            case 2:
                return 'Non Klasikal';
            case 3:
                return 'Blended Learning';
        }
    }
}