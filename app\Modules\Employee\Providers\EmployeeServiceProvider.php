<?php

namespace App\Modules\Employee\Providers;

use App\Modules\Employee\Interfaces\IEmployeeRepository;
use App\Modules\Employee\Repositories\EmployeeRepository;
use Illuminate\Support\ServiceProvider;

class EmployeeServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(IEmployeeRepository::class, EmployeeRepository::class);
    }

    public function boot()
    {
        $this->loadViewsFrom(__DIR__ . '/../Views', 'Employee');
    }
}
