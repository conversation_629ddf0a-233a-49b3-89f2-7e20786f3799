<?php

namespace App\Modules\Gaji\Service;

use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Gaji\Models\GajiLokasi;
use App\Modules\Gaji\Models\GajiPns;
use App\Modules\Gaji\Repositories\GajiRepository;

class GajiService
{
    public static function generateGajiPns()
    {
        EmployeeModel::active()->where(function ($query) {
            $query
                ->where('status_pegawai', 1)
                ->orWhere('status_pegawai', 2);
        })->chunk(1000, function ($employees) {
            foreach ($employees as $employee) {
                GajiRepository::buildGajiPns($employee);
            }
        });
    }
    public static function generateGajiPppk()
    {
        // self::buildGajiPns();
    }
    public static function generateTppPns()
    {
        // self::buildGajiPns();
    }
    public static function generateTppPppk()
    {
        // self::buildGajiPns();
    }
}
