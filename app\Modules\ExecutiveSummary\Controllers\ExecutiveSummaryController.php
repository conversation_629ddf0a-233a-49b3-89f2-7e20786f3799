<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\ExecutiveSummary\Requests\ExecutiveSummaryCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class ExecutiveSummaryController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('ExecutiveSummary::index', ['permissions' => $permissions]);
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = ExecutiveSummaryRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('ExecutiveSummary::create');
    }

    public function store(ExecutiveSummaryCreateRequest $request)
    {
        $payload = $request->all();
        $executive_summary = ExecutiveSummaryRepository::create($payload);
        return JsonResponseHandler::setResult($executive_summary)->send();
    }

    public function show(Request $request, $id)
    {
        $executive_summary = ExecutiveSummaryRepository::get($id);
        return JsonResponseHandler::setResult($executive_summary)->send();
    }

    public function edit($id)
    {
        return view('ExecutiveSummary::edit', ['executive_summary_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $executive_summary = ExecutiveSummaryRepository::update($id, $payload);
        return JsonResponseHandler::setResult($executive_summary)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = ExecutiveSummaryRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
