@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="goldarah">
    <default-datatable title="GolDarah" url="{!! url('goldarah') !!}" :headers="headers" :can-add="{{ $permissions['create-golongan_darah'] }}" :can-edit="{{ $permissions['update-golongan_darah'] }}" :can-delete="{{ $permissions['delete-golongan_darah'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'goldarah',
        						text: 'goldarah'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#goldarah');
</script>
@endsection