@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jenjurusan">
    <default-datatable title="JenJurusan" url="{!! url('jenjurusan') !!}" :headers="headers" :can-add="{{ $permissions['create-jenjurusan'] }}" :can-edit="{{ $permissions['update-jenjurusan'] }}" :can-delete="{{ $permissions['delete-jenjurusan'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jenjurusan');
</script>
@endsection