<?php

namespace App\Modules\KategoriPrioritasPPPK\Repositories;

use App\Modules\KategoriPrioritasPPPK\Models\KategoriPrioritasPPPK;

class KategoriPrioritasPPPKRepository
{
    public static function datatable($per_page = 15)
    {
        $data = KategoriPrioritasPPPK::paginate($per_page);
        return $data;
    }
    public static function get($kategori_prioritas_pppk_id)
    {
        $kategori_prioritas_pppk = KategoriPrioritasPPPK::where('id', $kategori_prioritas_pppk_id)->first();
        return $kategori_prioritas_pppk;
    }
    public static function create($kategori_prioritas_pppk)
    {
        $kategori_prioritas_pppk = KategoriPrioritasPPPK::create($kategori_prioritas_pppk);
        return $kategori_prioritas_pppk;
    }

    public static function update($kategori_prioritas_pppk_id, $kategori_prioritas_pppk)
    {
        KategoriPrioritasPPPK::where('id', $kategori_prioritas_pppk_id)->update($kategori_prioritas_pppk);
        $kategori_prioritas_pppk = KategoriPrioritasPPPK::where('id', $kategori_prioritas_pppk_id)->first();
        return $kategori_prioritas_pppk;
    }

    public static function delete($kategori_prioritas_pppk_id)
    {
        $delete = KategoriPrioritasPPPK::where('id', $kategori_prioritas_pppk_id)->delete();
        return $delete;
    }
}
