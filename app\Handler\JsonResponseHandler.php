<?php

namespace App\Handler;

use App\Type\JsonResponseType;

class JsonResponseHandler
{
    public static function setStatus(int $status)
    {
        return (new _JsonResponseHandler())->setStatus($status);
    }
    public static function setCode(string $code)
    {
        return (new _JsonResponseHandler())->setCode($code);
    }
    public static function setMessage(string $message)
    {
        return (new _JsonResponseHandler())->setMessage($message);
    }
    public static function setResult($result)
    {
        return (new _JsonResponseHandler())->setResult($result);
    }

    public static function denied($message = '')
    {
        return response()->json([
            'status' => 403,
            'code' => JsonResponseType::FORBIDDEN,
            'message' => 'Akses <PERSON>lak: ' . $message
        ], 403);
    }
}
class _JsonResponseHandler
{
    private int $status = 200;
    private string $code = JsonResponseType::SUCCESS;
    private string $message = '';
    private $result = null;


    public function setStatus(int $status)
    {
        $this->status = $status;
        return $this;
    }
    public function setCode(string $code)
    {
        $this->code = $code;
        return $this;
    }
    public function setMessage(string $message)
    {
        $this->message = $message;
        return $this;
    }
    public function setResult($result)
    {
        $this->result = $result;
        return $this;
    }
    public function send($cacheInSeconds = null)
    {
        $jsonData = [
            'status' => $this->status,
            'code' => $this->code,
            'message' => $this->message,
            'result' => $this->result
        ];
        return !empty($cache) ? response()->json($jsonData, $this->status)->header('Cache-Control', "max-age={$cacheInSeconds}") : response()->json($jsonData, $this->status);
    }
}
