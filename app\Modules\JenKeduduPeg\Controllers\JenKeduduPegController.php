<?php

namespace App\Modules\JenKeduduPeg\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenKeduduPeg\Models\JenKeduduPeg;
use App\Modules\JenKeduduPeg\Repositories\JenKeduduPegRepository;
use App\Modules\JenKeduduPeg\Requests\JenKeduduPegCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenKeduduPegController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('JenKeduduPeg::index', ['permissions' => $permissions]);
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 50;
        $keyword = $request->input('keyword') != null ? $request->input('keyword') : null;
        $data = JenKeduduPegRepository::datatable($per_page, $keyword);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $jenkedudupeg_id)
    {
        $jenkedudupeg = JenKeduduPeg::where('id', $jenkedudupeg_id)->first();
        return JsonResponseHandler::setResult($jenkedudupeg)->send();
    }

    public function create()
    {
        return view('JenKeduduPeg::create');
    }

    public function store(JenKeduduPegCreateRequest $request)
    {
        $payload = $request->all();
        $jenkedudupeg = JenKeduduPegRepository::create($payload);
        return JsonResponseHandler::setResult($jenkedudupeg)->send();
    }

    public function show(Request $request, $id)
    {
        $jenkedudupeg = JenKeduduPegRepository::get($id);
        return JsonResponseHandler::setResult($jenkedudupeg)->send();
    }

    public function edit($id)
    {
        return view('JenKeduduPeg::edit', ['jenkedudupeg_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jenkedudupeg = JenKeduduPegRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jenkedudupeg)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JenKeduduPegRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
