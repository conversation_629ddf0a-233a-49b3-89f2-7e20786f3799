<?php

namespace App\Modules\Employee\Repositories;

use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Employee\Model\EmployeeMastfotoEpsModel;
use App\Exceptions\AppException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Type\JsonResponseType;
use App\Modules\Employee\Model\EmployeePresencePhotoTempModel;

class PhotoRepository
{
    const PROFILE_PHOTO_DIR = 'employee/profilePhoto';
    const PRESENCE_PHOTO_DIR = 'employee/presencePhoto';
    const PRESENCE_PHOTO_PDM_DIR = 'submission/presencePhoto';

    public static function movePresencePhotoPdmToFixedPath($employeeID)
    {   
        DB::beginTransaction();
        try {
            $storage = Storage::disk('local');
            if (!$storage->exists(self::PRESENCE_PHOTO_PDM_DIR . '/' . $employeeID)) {
                throw new AppException('Foto Presensi tidak ditemukan');
            }
    
            $files = $storage->get(self::PRESENCE_PHOTO_PDM_DIR . '/' . $employeeID);
            $employee = EmployeeModel::find($employeeID);
    
            $mastfotoID = [
                'NIP' => $employee->nip_lama ?? $employee->nip
            ];
            $mastfotoPayload = [
                'NIP' => $employee->nip_lama ?? $employee->nip,
                'MIMETYPE' => explode('/', $storage->mimeType(self::PRESENCE_PHOTO_PDM_DIR . '/' . $employeeID))[1],
                'DATA' => $files,
                'updated_at' => now()
            ];
            EmployeeMastfotoEpsModel::updateOrCreate($mastfotoID, $mastfotoPayload);
    
            $move = $storage->move(
                self::PRESENCE_PHOTO_PDM_DIR . '/' . $employeeID,
                self::PRESENCE_PHOTO_DIR . '/' . $employeeID
            );
            if (!$move) {
                throw new AppException('Gagal memindahkan foto presensi dari pegawai yang diverifikasi, harap hubungi admin atau tim developer.');
            }
            DB::commit();
            return true;
        }
        catch (AppException $e) {
            DB::rollBack();
            return false;
        }
    }

    public static function getTemp($employeeId, $status, $tempId = null)
    {
        $temp = EmployeePresencePhotoTempModel::where('id_pegawai', $employeeId)
            ->when(!empty($tempId), function ($q) use ($tempId) {
                $q->where('id', $tempId);
            })
            ->where('status', $status)
            ->first();
        return $temp;
    }

    public static function getSubmissionTempId($employeeId, string $tempId)
    {
        if (in_array($tempId, ['null', 'undefined', '0', ''])) {
            $temp = self::getTemp($employeeId, 0);

            if (empty($temp)) {
                throw new AppException(
                    '<b>Gagal mengambil status pengajuan data Foto Presensi</b><br>Data pengajuan foto Presensi tidak ditemukan',
                    JsonResponseType::NOT_FOUND,
                    compact('employeeID'),
                    404
                );
            }
            $tempId = $temp->id;
        }

        return $tempId;
    }
}