<?php

namespace App\Modules\Bidang\Models;

use App\Modules\Pdm\Models\PdmModel;
use Illuminate\Database\Eloquent\Model;

class BidangLayanan extends Model
{
    protected $table = 'bidang_layanan';
    protected $guarded = [];

    protected $appends = ['service_name'];


    public function getServiceNameAttribute()
    {
        if (empty(PdmModel::TABLE_TO_RIWAYAT_TEXT[$this->service])) {
            return null;
        }
        return PdmModel::TABLE_TO_RIWAYAT_TEXT[$this->service];
    }
}
