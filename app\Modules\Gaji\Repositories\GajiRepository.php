<?php

namespace App\Modules\Gaji\Repositories;

use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Gaji\Models\GajiLokasi;
use App\Modules\Gaji\Models\GajiPns;

class GajiRepository
{
    public static function datatablePns($per_page = 15)
    {
        $data = GajiPns::paginate($per_page);
        return $data;
    }

    public static function datatable($per_page = 15)
    {
        // $data = Gaji::paginate($per_page);
        // return $data;
    }


    public static function buildGajiPns($employee)
    {
        $unitKerja = $employee->unitKerja;
        $lokasiSimGaji = GajiLokasi::where('kolok_simpeg', str_replace('.', '', $unitKerja->idskpd))->first();
        $data = [
            'lokasikerja' => $lokasiSimGaji->kode,
            'nip_lama' => $employee->nip_lama,
            'nip' => $employee->nip,
            'name' => $employee->complete_name, //tb_01->gelar_depan|nip|gelar_belangan
            'alamat' => $employee->domisili_alamat, //tb_01->domisili_alamat|domisili_rt|domisili_rw|domisili_kode_pos|domisili_provinsi|domisili_kabupaten|domisili_kecamatan|domisili_kelurahan
            'tempat_lahir' => $employee->tempat_lahir, //tb_01->tempat_lahir
            'tanggal_lahir' => $employee->tanggal_lahir, //tb_01->tanggal_lahir
            'gender_id' => $employee->jenis_kelamin, //tb_01->jenis_kelamin
            'agama_id' => $employee->agama, //tb_01->agama
            'marital_id' => $employee->status_pernikahan, //tb_01->status_pernikahan
            'tmt_pegawai' => $employee->tmt_pegawai, //r_cpns->tmt_cpns
            'status_id' => $employee->status_pegawai, //tb_01->status_pegawai
            'golongan_id' => $employee->last_pangkat != null ? $employee->last_pangkat->id_kepangkatan : null,
            'tmt_golongan' =>  $employee->last_pangkat != null ? $employee->last_pangkat->tmt_sk : null,
            'tanggal_golongan' =>  $employee->last_pangkat != null ? $employee->last_pangkat->tanggal_sk : null,
            'sk_golongan' =>  $employee->last_pangkat != null ? $employee->last_pangkat->nomor_sk : null,
            'tmt_berkala' => $employee->last_kgb != null ? $employee->last_kgb->tmt_sk : null, //r_gaji_berkala->tmt_sk (isakhir = 1)
            'tanggal_berkala' => $employee->last_kgb != null ? $employee->last_kgb->tanggal_sk : null, //r_gaji_berkala->tanggal_sk (isakhir = 1)
            'sk_berkala' => $employee->last_kgb != null ? $employee->last_kgb->nomor_sk : null, //r_gaji_berkala->nomor_sk (isakhir = 1)
            'masa_kerja' => $employee->last_kgb != null ? $employee->last_kgb->masa_kerja_tahun . $employee->last_kgb->masa_kerja_bulan : null, //r_gaji_berkala->masa_kerja_bulan|masa_kerja_tahun (isakhir = 1)
            'gaji_pokok' => $employee->last_kgb != null ? $employee->last_kgb->gaji : null, //r_gaji_berkala->gaji (isakhir = 1)
            'lokasigaji' => '11005000', //sumber dari ?
            'anak' => $employee->child_have_allowance_count, //r_anak (count)
            'eselon_id' => $employee->currentJabatan() != null ? $employee->currentJabatan()->id_eselon : null, //r_jabatan->id_eselon (isakhir = 1)
            'tmt_jabatan' => $employee->currentJabatan() != null ? $employee->currentJabatan()->tmt_jabatan : null, //r_jabatan->tmt_jabatan (isakhir = 1)
            'kedudukan_id' => '', //tb_01->kedudukan_pegawai
            'kelompok_gaji' => 'A', //sumber dari ?
            'inpres' => NULL, //sumber dari ?
            'tunjangan_umum' => 1, //sumber dari ?
            'tunjangan_istri' => 1, //sumber dari ?
            'hps' => NULL, //sumber dari ?
            'keterangan' => 'CPNS TAHUN ANGGARAN 2019 PROV JATENG\nPengangkatan jabfung', //sumber dari idskpd ?
            'date' => NULL, //sumber dari ?
            'user_id' => '0000-00-00', //sumber dari ?
            'sertifikasi_guru' => 0, //sumber dari ?
            'bebas_tugas' => 0, //sumber dari ?
            'pendidikan_id' => $employee->last_education != null ? $employee->last_education->id_jenjang : null, //r_pendidikan_formal->id_jenjang (isakhir = 1)
            'lokasi_kerja' => $lokasiSimGaji->id, //tb_01->id_unit_kerja|id_induk_upt|id_sub_unit|id_sub_sub_unit|id_sub_sub_sub_unit
            'lokasi_gaji' => 2088, //sumber dari ?
            'fungsional_id' => 314, //sumber dari ?
            'created' => NULL, //sumber dari ?
            'updated' => '2022-12-01 00:00:00', //sumber dari ?
            'npwp' => $employee->nomor_npwp, //tb_01->nomor_npwp
            'nik' => $employee->nik, //tb_01->nik
            'no_bpjskes' => $employee->nosmor_bpj, //tb_01->nomor_bpjs
            'guru' => NULL, //sumber dari ?
            'status_dikmen' => NULL, //sumber dari ? 
            'p3d_nondikmen' => NULL, //sumber dari ?
            'tmt_tb' => '0000-00-00', //sumber dari ?
            'tmt_meninggal' => '0000-00-00', //sumber dari ?
            'kompensasi_id' => 0, //sumber dari ?
            'created_at' => NULL, //sumber dari ?
            'created_by' => NULL, //sumber dari ?
            'updated_at' => NULL, //sumber dari ?
            'updated_by' => NULL, //sumber dari ?
            'deleted_at' => NULL, //sumber dari ?
            'deleted_by' => NULL, //sumber dari ?
            'kode_id' => NULL, //sumber dari ?
            'is_spesialis' => NULL //sumber dari ?
        ];

        GajiPns::updateOrCreate(['nip' => $employee->nip], $data);
    }
    public static function buildGajiPppk($employeeId)
    {
        $employee = EmployeeModel::where('id', $employeeId)->first();
        $lokasiKerja = null;
        $data = [
            'id' => 35182,
            'lokasikerja' => '34000000',
            'nip_lama' => '',
            'nip' => '199707312023212021',
            'name' => 'LUTFIA RIZKY ANANDA,S.Psi',
            'alamat' => 'JL. ARGO MULYO MUKTI IV 176 RT 01 RW 10, TLOGOMULYO, PEDURUNGAN, KOTA SEMARANG',
            'tempat_lahir' => 'SEMARANG',
            'tanggal_lahir' => '1997-07-31',
            'gender_id' => 2,
            'agama_id' => 1,
            'marital_id' => 1,
            'tmt_pegawai' => '2023-11-01',
            'status_id' => 2,
            'golongan_id' => 9,
            'tmt_golongan' => '2023-11-01',
            'tanggal_golongan' => NULL,
            'sk_golongan' => '',
            'tmt_berkala' => '2023-11-01',
            'tanggal_berkala' => NULL,
            'sk_berkala' => '',
            'masa_kerja' => '0000',
            'gaji_pokok' => 3203600,
            'lokasigaji' => '34000000',
            'anak' => 0,
            'eselon_id' => 1,
            'tmt_jabatan' => '0000-00-00',
            'kedudukan_id' => 72,
            'kelompok_gaji' => '',
            'inpres' => NULL,
            'tunjangan_umum' => 0,
            'tunjangan_istri' => 1,
            'hps' => '',
            'keterangan' => 'P3K TMT 1 NOVEMBER 2023 SPMT 1 DESEMBER 2023',
            'date' => NULL,
            'user_id' => NULL,
            'sertifikasi_guru' => 0,
            'bebas_tugas' => 0,
            'pendidikan_id' => 0,
            'lokasi_kerja' => 3028,
            'lokasi_gaji' => 3028,
            'fungsional_id' => 469,
            'created' => '2023-12-07 07:21:03',
            'updated' => '2023-12-07 07:21:03',
            'npwp' => '96.259.699.5-518.000',
            'nik' => '33.7406.710797.0001',
            'no_bpjskes' => '0000075891317',
            'guru' => NULL,
            'status_dikmen' => 0,
            'p3d_nondikmen' => 0,
            'tmt_tb' => '0000-00-00',
            'tmt_meninggal' => '0000-00-00',
            'tmt_akhir_kontrak' => '2028-10-31',
            'created_at' => NULL,
            'created_by' => NULL,
            'updated_at' => '2024-06-05 22:10:47',
            'updated_by' => NULL,
            'deleted_at' => NULL,
            'deleted_by' => NULL,
            'kode_id' => NULL,
            'is_spesialis' => NULL
        ];
    }

    public static function buildTppPns($employeeId)
    {
        $employee = EmployeeModel::where('id', $employeeId)->first();
        $lokasiKerja = null;
        $data = [
            'nip' => '199511212020122015', //tb_01->nip
            'lokasi' => '11005000', //tb_01->id_unit_kerja|id_induk_upt|id_sub_unit|id_sub_sub_unit|id_sub_sub_sub_unit
            'name' => 'NISRINA ALIFAH,A.Md.', //tb_01->gelar_depan|nip|gelar_belangan
            'golongan_id' => 24, //r_kepangkatan->id_kepangkatan (isakhir = 1)
            'kedudukan_id' => 72, //tb_01->kedudukan_pegawai
            'kode_fungsional' => '000', //sumber dari ?
            'tunjangan_jabatan' => NULL, //sumber dari ?
            'jabatan' => 'PRANATA KOMP PELAKSANA', //r_jabatan->jabatan (isakhir = 1)
            'PNIP9' => NULL, //sumber dari ?
            'kelompok_gaji' => '', //sumber dari ?
            'PDIDIK' => 0, //sumber dari ?
            'tpp_kk_stop' => 2, //sumber dari ?
            'tpp_tb_stop' => 2, //sumber dari ?
            'PDATABASE' => '', //sumber dari ?
            'PISTRI' => 0, //sumber dari ?
            'PANAK' => 0, //sumber dari ?
            'sertifikasi_guru' => 0, //sumber dari ?
            'profesi_id' => 0, //sumber dari ?
            'p3d' => 0, //sumber dari ?
            'pindahan' => 0, //sumber dari ?
            'penugasan_khusus' => 0, //sumber dari ?
            'lokasi_kerja' => '2071', //sumber dari ?
            'lokasi_gaji' => 2088, //sumber dari ?
            'fungsional_id' => 314, //sumber dari ?
            'eselon_id' => 1,
            'bebas_tugas' => 0, //sumber dari ?
            'user_id' => 101, //sumber dari ?
            'created' => '2022-11-01 00:00:00', //sumber dari ?
            'updated' => NULL, //sumber dari ?
            'id' => 159432, //sumber dari ?
            'keterangan' => 'CPNS TAHUN ANGGARAN 2019 PROV JATENG', //sumber dari ?
            'masterjfu_id' => 0, //sumber dari ?
            'masterjft_id' => 0, //sumber dari ?
            'mastersotk_id' => 0, //sumber dari ?
            'pokja_apbj' => 0, //sumber dari ?
            'A_01' => 'B2', //tb_01->id_unit_kerja
            'KOLOK' => 'B200501000', //tb_01->id_unit_kerja|id_induk_upt|id_sub_unit|id_sub_sub_unit|id_sub_sub_sub_unit
            'I_5A' => '2', //r_jabatan->id_jenis_jabatan (isakhir = 1)
            'I_05' => '00082', //sumber dari ?
            'I_07' => '11', //sumber dari ?
            'I_JB' => 'PRANATA KOMPUTER TERAMPIL', //r_jabatan->jabatan (isakhir = 1)
            'K_01' => '0', //sumber dari ?
            'kelasjab' => '6', //r_jabatan->id_jenis_jabatan&id_jenis_jabatan (isakhir = 1) (field kelas_jabatan pada referensi id_jenis_jabatan 1: a_skpd, 2: a_jabfung, 3: a_jabfungum)
            'unor' => 'B200501000', //tb_01->id_unit_kerja|id_induk_upt|id_sub_unit|id_sub_sub_unit|id_sub_sub_sub_unit
            'tb' => 0, //sumber dari ?
            'tpp_stop' => 0, //sumber dari ?
            'beban_kerja' => 3900000, //sumber dari ?
            'beban_kerja_khusus' => 0, //sumber dari ?
            'beban_kerja_khusus_stop' => 1, //sumber dari ?
            'tempat' => 0, //sumber dari ?
            'tempat_stop' => 1, //sumber dari ?
            'kondisi' => 0, //sumber dari ?
            'kondisi_stop' => 1, //sumber dari ?
            'up' => NULL, //sumber dari ?
            'up_stop' => NULL, //sumber dari ?
            'tmt_mutasi' => NULL, //sumber dari ?
            'is_spesialis' => NULL, //sumber dari ?
            'is_plt' => NULL, //sumber dari ?
            'lokasi_kerja_plt' => NULL, //sumber dari ?
            'lokasi_gaji_plt' => NULL, //sumber dari ?
            'unor_plt' => NULL, //sumber dari ?
            'nominal_plt' => NULL, //sumber dari ?
            'status_id' => NULL, //sumber dari ?
            'penyetaraan' => NULL, //sumber dari ?
            'kode_id' => NULL //sumber dari ?
        ];
    }

    public static function buildTppPppk($employeeId)
    {
        $employee = EmployeeModel::where('id', $employeeId)->first();
        $lokasiKerja = null;

        $data = [
            'id' => 35318,
            'nip' => '199707312023212021',
            'lokasi' => '34000000',
            'name' => 'LUTFIA RIZKY ANANDA,S.Psi',
            'golongan_id' => 9,
            'kedudukan_id' => 72,
            'kode_fungsional' => '1',
            'tunjangan_jabatan' => NULL,
            'jabatan' => NULL,
            'PNIP9' => NULL,
            'kelompok_gaji' => NULL,
            'PDIDIK' => NULL,
            'tpp_kk_stop' => NULL,
            'tpp_tb_stop' => NULL,
            'PDATABASE' => NULL,
            'PISTRI' => 0,
            'PANAK' => 0,
            'sertifikasi_guru' => NULL,
            'profesi_id' => NULL,
            'p3d' => NULL,
            'pindahan' => NULL,
            'penugasan_khusus' => NULL,
            'lokasi_kerja' => 3028,
            'lokasi_gaji' => 3028,
            'eselon_id' => 1,
            'user_id' => NULL,
            'created' => '2024-04-18 00:00:00',
            'updated' => NULL,
            'keterangan' => 'P3K TMT SPMT 1 Desember 2023',
            'masterjfu_id' => NULL,
            'masterjft_id' => NULL,
            'mastersotk_id' => NULL,
            'pokja_apbj' => NULL,
            'A_01' => 'D0',
            'KOLOK' => 'D000100000',
            'I_5A' => '0',
            'I_05' => '700154',
            'I_07' => '',
            'I_JB' => 'ANALIS SUMBER DAYA MANUSIA APARATUR',
            'K_01' => NULL,
            'kelasjab' => '7',
            'unor' => 'D000100000',
            'tb' => 0,
            'tpp_stop' => 0,
            'beban_kerja' => 1500000,
            'beban_kerja_khusus' => NULL,
            'beban_kerja_khusus_stop' => 1,
            'tempat' => NULL,
            'tempat_stop' => 1,
            'kondisi' => NULL,
            'kondisi_stop' => 1,
            'tmt_mutasi' => '2023-12-01',
            'is_spesialis' => NULL,
            'is_plt' => NULL,
            'lokasi_kerja_plt' => NULL,
            'lokasi_gaji_plt' => NULL,
            'unor_plt' => NULL,
            'nominal_plt' => NULL,
            'status_id' => NULL,
            'penyetaraan' => NULL,
            'tmt_spmt' => NULL,
            'tgl_spmt' => NULL,
            'no_spmt' => NULL,
            'lokasi_temp' => NULL
        ];
    }
}
