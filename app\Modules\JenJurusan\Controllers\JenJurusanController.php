<?php

namespace App\Modules\<PERSON>Jurusan\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenJurusan\Models\JenJurusan;
use App\Modules\JenJurusan\Repositories\JenJurusanRepository;
use App\Modules\JenJurusan\Requests\JenJurusanCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenJurusanController extends Controller
{
    public function index(Request $request)
    {
        $jenjurusan = JenJurusan::get();
        return JsonResponseHandler::setResult($jenjurusan)->send();
    }

    public function datatable(Request $request)
    {
        $keyword = $request->input('keyword') != null ? $request->input('keyword') : null;
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenJurusanRepository::datatable($keyword, $per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $jenjurusan_id)
    {
        $jenjurusan = JenJurusan::find($jenjurusan_id);
        return JsonResponseHandler::setResult($jenjurusan)->send();
    }

    public function filterJenjang(Request $request, $jenjurusan_id)
    {
        $keyword = $request->input('keyword');
        $jenjurusan = JenJurusan::where('idtkpendid', $jenjurusan_id)
            ->when(!empty($keyword), function ($query) use ($keyword) {
                $query->where('jenjurusan', 'like', '%' . $keyword . '%');
            })
            ->select('idjenjurusan', 'jenjurusan')
            ->get()
            ->makeHidden(['formatted_tkpendid', 'formatted_rumpunpendid']);
        return JsonResponseHandler::setResult($jenjurusan)->send();
    }

    public function create()
    {
        return view('JenJurusan::create');
    }

    public function store(JenJurusanCreateRequest $request)
    {
        $payload = $request->all();
        $jenjurusan = JenJurusanRepository::create($payload);
        return JsonResponseHandler::setResult($jenjurusan)->send();
    }

    public function show(Request $request, $id)
    {
        $jenjurusan = JenJurusanRepository::get($id);
        return JsonResponseHandler::setResult($jenjurusan)->send();
    }

    public function edit($id)
    {
        return view('JenJurusan::edit', ['jenjurusan_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jenjurusan = JenJurusanRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jenjurusan)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JenJurusanRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
