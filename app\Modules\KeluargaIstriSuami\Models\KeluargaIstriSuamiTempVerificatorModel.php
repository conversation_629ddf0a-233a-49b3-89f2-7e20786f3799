<?php

namespace App\Modules\KeluargaIstriSuami\Models;

use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Role\Model\RoleModel;
use App\Modules\User\Model\UserModel;
use Illuminate\Database\Eloquent\Model;

class KeluargaIstriSuamiTempVerificatorModel extends Model
{
    public $table = 'r_pasangan_temp_verificator';
    protected $guarded = [];

    public function role()
    {
        return $this->belongsTo(RoleModel::class, 'role_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }
}
