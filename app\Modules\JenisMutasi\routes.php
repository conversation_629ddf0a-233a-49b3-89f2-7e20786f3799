<?php
namespace App\Modules\JenisMutasi;

use App\Modules\JenisMutasi\Controllers\JenisMutasiController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenis-mutasi')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenisMutasiController::class, 'index']);
    Route::get('/datatable', [JenisMutasiController::class, 'datatable'])->middleware('authorize:read-jenis_mutasi');
    Route::get('/create', [JenisMutasiController::class, 'create'])->middleware('authorize:create-jenis_mutasi');
    Route::post('/', [JenisMutasiController::class, 'store'])->middleware('authorize:create-jenis_mutasi');
    Route::get('/{jenis_mutasi_id}', [JenisMutasiController::class, 'show'])->middleware('authorize:read-jenis_mutasi');
    Route::get('/{jenis_mutasi_id}/edit', [JenisMutasiController::class, 'edit'])->middleware('authorize:update-jenis_mutasi');
    Route::put('/{jenis_mutasi_id}', [JenisMutasiController::class, 'update'])->middleware('authorize:update-jenis_mutasi');
    Route::delete('/{jenis_mutasi_id}', [JenisMutasiController::class, 'destroy'])->middleware('authorize:delete-jenis_mutasi');
}); 