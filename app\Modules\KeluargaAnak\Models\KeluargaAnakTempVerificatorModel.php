<?php

namespace App\Modules\KeluargaAnak\Models;

use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Role\Model\RoleModel;
use App\Modules\User\Model\UserModel;
use Illuminate\Database\Eloquent\Model;

class KeluargaAnakTempVerificatorModel extends Model
{
    public $table = 'r_anak_temp_verificator';
    protected $guarded = [];

    public function role()
    {
        return $this->belongsTo(RoleModel::class, 'role_id', 'id');
    }
    public function user()
    {
        return $this->belongsTo(UserModel::class, 'user_id', 'id');
    }
}
