<?php

namespace App\Console\Commands;

use App\Mail\UserRegisterMail;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Employee\Repositories\EmployeeRepository;
use App\Modules\EmployeeStatus\Repositories\EmployeeStatusRepository;
use App\Modules\ExecutiveSummary\Repositories\ValiditasRepository;
use App\Modules\RiwayatPendidikan\Models\RiwayatPendidikanModel;
use App\Modules\User\Model\UserModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'for:testing';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Just snippet of code for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // $pendidikan = RiwayatPendidikanModel::where('id', 338764)->first();

        // $pendidikan->handleEfileUpdate([
        //     'ijazah_efile' => 'submission/ijazah/Ijazah_Submission_338764.pdf',
        // ], 2);

        $user = UserModel::where('id', 137)->first();
        Mail::to(["111111" . "@asn.jatengprov.go.id", "<EMAIL>"])->send(new UserRegisterMail($user));
    }
}
