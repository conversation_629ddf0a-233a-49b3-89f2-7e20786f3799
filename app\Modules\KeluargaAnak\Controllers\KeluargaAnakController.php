<?php

namespace App\Modules\KeluargaAnak\Controllers;

use App\Exceptions\AppException;
use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\KeluargaAnak\Models\KeluargaAnakModel;
use App\Modules\KeluargaAnak\Models\KeluargaAnakTempModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiTempModel;
use App\Modules\KeluargaAnak\Repositories\KeluargaAnakRepository;
use App\Modules\KeluargaAnak\Requests\KeluargaAnakCreateRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

use App\Modules\Efile\Repositories\EfileRepository;
use Exception;
use App\Handler\FileHandler;
use Illuminate\Support\Facades\DB;

class KeluargaAnakController extends Controller
{
    public function employeeDatatable(Request $request, $employeeId)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $datatable = KeluargaAnakRepository::employeeDatatable($employeeId, $per_page);
        $items = $datatable->items();
        $datas = [];
        
        foreach ($items as $item) {
            $datas[] = $item->toArray();
        }
        
        if (!empty($datas)) {
            $employee = EmployeeModel::find($employeeId);
            $efilesCode = KeluargaAnakModel::EFILE_CODE_MAPPING;
            $requiredEfileCodes = ["52", "35_3", "23_3", "19_3", "58"];
    
            $efilesFromApi = (new EfileRepository())->getFileByNipAndCodes($employee->nip, implode(",", $requiredEfileCodes));
        
            $efiles = collect($efilesFromApi)
                        ->keyBy('id_jenis')
                        ->all();
    
            if (!empty($efiles)) {
                foreach ($datas as $dataKey => $data) {
                    foreach ($efiles as $efileKey => $efile) {
                        if ($efile['response'] == 'success' && !empty($efile)) {
                            if ($efile['riwayat'] == 0) {
                                $datas[$dataKey][$efilesCode[$efileKey]] = $efile['efile'][0];
                            }
                            else if ($efile['riwayat'] == 1) {
                                $efileCollection = collect($efile['efile']);
                                $datas[$dataKey][$efilesCode[$efileKey]] = $efileCollection
                                    ->filter(function ($value) use ($data) {
                                        return $value['id_riwayat'] == $data['old_id'] || $value['id_riwayat'] == $data['id'];
                                    })
                                    ->sortByDesc('modified_at')
                                    ->first();
                            }
                        }
                        else {
                            $datas[$dataKey][$efilesCode[$efileKey]] = null;
                        }
                    }
                }
            }
        }

        $modifiedData = new \Illuminate\Pagination\LengthAwarePaginator(
            $datas,
            $datatable->total(),
            $datatable->perPage(),
            $datatable->currentPage(),
            ['path' => $datatable->path()]
        );
        // dd($modifiedData->toArray());
        return JsonResponseHandler::setResult($modifiedData)->send();
    }
    public function createSubmission(KeluargaAnakCreateRequest $request, $employeeId)
    {
        $payload = $request->only([
            'id',
            'nip',
            'nik',
            'id_pasangan_usul',
            'status_asn',
            'anak_ke',
            'nama_anak',
            'tempat_lahir',
            'tanggal_lahir',
            'status_anak',
            'jenis_kelamin',
            'umur',
            'alamat',
            'alamat_rt',
            'alamat_rw',
            'alamat_kode_pos',
            'alamat_provinsi',
            'alamat_kabupaten',
            'alamat_kecamatan',
            'alamat_kelurahan',
            'alamat_long',
            'alamat_lat',
            'ktp_alamat',
            'ktp_alamat_rt',
            'ktp_alamat_rw',
            'ktp_alamat_kode_pos',
            'ktp_alamat_provinsi',
            'ktp_alamat_kabupaten',
            'ktp_alamat_kecamatan',
            'ktp_alamat_kelurahan',
            'ktp_alamat_long',
            'ktp_alamat_lat',
            'status_tunjangan',
            'status_tunjangan_final',
            'status_perkawinan',
            'nomor_hp',
            'nomor_bpjs',
            'nomor_akta_lahir',
            'tanggal_akta_lahir',
            'status_hidup',
            'kematian_tanggal',
            'kematian_nomor',
            'id_pendidikan',
            'pekerjaan',
            'pekerjaan_status',
            'pekerjaan_nip',
            'pekerjaan_kantor',
            'pekerjaan_long',
            'pekerjaan_lat',
            'id_pasangan',
            'status_kedudukan',
            'status_tunjangan_efile',
            'bpjs_anak_efile',
            'nomor_akta_lahir_efile',
            'nomor_akta_mati_efile',
            'status_pegawai_efile',
            'surat_tidak_punya_penghasilan_anak_efile'
        ]);
        
        DB::beginTransaction();
        try {
            if (isset($request['id_pasangan'])) {
                $payload['id_pasangan'] = $request['id_pasangan'];
                $cekusulan = KeluargaIstriSuamiTempModel::where('id_pegawai', $employeeId)->where('id', $payload['id_pasangan'])->count();
                if($cekusulan > 0){
                    $payload['id_pasangan_usul'] = $payload['id_pasangan'];
                }
            }
            $err = 0;
            $message = "";
            if (((strlen($payload['nik']) < 16) && $payload['nik'] != '-') && ($payload['status_kedudukan'] == 1)) {
                $err = $err + 1;
                $message = "Status Anak masih hidup, tetapi NIK tidak valid. <br>";
            }
            if (($payload['status_asn'] == 2) && ($payload['status_kedudukan'] == 1) && isset($payload['pekerjaan_nip']) && isset($payload['nik'])) {
                $cekortu = EmployeeModel::where('nip', $payload['pekerjaan_nip'])
                            ->where('nik', '!=', $payload['nik'])
                            ->where('id', '!=', '')
                            ->count();
                if ($cekortu > 0) {
                    $err = $err + 1;
                    $message .= "NIP Anak tercatat sebagai ASN jawa Tengah, tetapi NIK tidak valid.";
                }
            }
            if ($err > 0) {
                return response()->json(['status' => 'error', 'message' => $message, 'data' => ''], JsonResponse::HTTP_NOT_FOUND);
            }
    
            $employee = EmployeeModel::where('id', $employeeId)->first();
            $nip = $employee->nip;
            $timestamp = date("Y_m_d_His");
            $fileName = [];
    
            foreach ($payload as $key => $value) {
                if (str_contains($key, 'efile') && $request->hasFile($key)) {
                    $prefix = str_replace(['nomor_', '_efile', 'dokumen_'], ['', '', ''], $key);
                    $docName = implode('_', array_map('ucfirst', explode('_', strtolower($prefix))));
                    $fileName[$key] = "Dokumen_" . $docName . "_" . $nip . "_" . $timestamp;
                    // dd($fileName);
                    $payload[$key] = FileHandler::store(
                        file: $request->file($key), 
                        targetDir: KeluargaAnakModel::ANAK_EFILE_DIR, 
                        fileName: $fileName[$key], 
                        isNeedException: false
                    );
                }
    
                // Error handling
                if (is_array($payload[$key]) && $payload[$key]['success'] === false) {
                    // Log::info($payload);
                    $payload[$key]['message'] = 'Terjadi kesalahan saat menyimpan Dokumen ' . str_replace('_', ' ', $docName) . ': ' . $payload[$key]['message'];
                    throw new AppException(
                        $payload[$key]['message'],
                        $payload[$key]['code'],
                        $payload[$key]['status']
                    );
                }
            }
            $employee = EmployeeModel::where('id', $employeeId)->first();
            $payload['id_pegawai'] = $employeeId;
            $payload['nip'] = $employee->nip;
            $payload['pekerjaan_long'] = "-";
            $payload['pekerjaan_lat'] = "-";
            $riwayat = KeluargaAnakRepository::createSubmission($payload);

            DB::commit();
            return JsonResponseHandler::setResult($riwayat)->send();
        }
        catch (AppException $e) {
            DB::rollBack();
            if (!empty($fileName)) {
                foreach ($payload as $key => $value) {
                    if (str_contains($key, 'efile') && is_string($payload[$key])) {
                        $ext = $request->file($key)->extension();
                        $payload[$key] = FileHandler::delete(KeluargaAnakModel::ANAK_EFILE_DIR . '/' . $fileName[$key] . '.' . $ext);
                    }
                }
            }
            
            throw $e;
        }

    }
    public function employeeRiwayat(Request $request, $employeeId, $riwayatId)
    {
        $data = KeluargaAnakModel::where('id', $riwayatId)
                    ->where('id_pegawai', $employeeId)
                    ->first();
        unset($data->umur);
        return JsonResponseHandler::setResult($data)->send();
    }
    public function createDeleteSubmission(Request $request, $employeeId, $riwayatPangkatId)
    {
        $riwayat = KeluargaAnakRepository::deleteSubmission($riwayatPangkatId);
        return JsonResponseHandler::setResult($riwayat)->send();
    }
    public function updateSubmission(Request $request, $employee_id, $riwayatId)
    {
        $payload = $request->only([
            'id',
            'nip',
            'nik',
            'id_pasangan',
            'id_pasangan_usul',
            'status_asn',
            'anak_ke',
            'nama_anak',
            'tempat_lahir',
            'tanggal_lahir',
            'status_anak',
            'jenis_kelamin',
            'umur',
            'alamat',
            'alamat_rt',
            'alamat_rw',
            'alamat_kode_pos',
            'alamat_provinsi',
            'alamat_kabupaten',
            'alamat_kecamatan',
            'alamat_kelurahan',
            'alamat_long',
            'alamat_lat',
            'ktp_alamat',
            'ktp_alamat_rt',
            'ktp_alamat_rw',
            'ktp_alamat_kode_pos',
            'ktp_alamat_provinsi',
            'ktp_alamat_kabupaten',
            'ktp_alamat_kecamatan',
            'ktp_alamat_kelurahan',
            'ktp_alamat_long',
            'ktp_alamat_lat',
            'status_tunjangan',
            'status_tunjangan_final',
            'status_perkawinan',
            'nomor_hp',
            'nomor_bpjs',
            'nomor_akta_lahir',
            'tanggal_akta_lahir',
            'status_hidup',
            'kematian_tanggal',
            'kematian_nomor',
            'id_pendidikan',
            'pekerjaan',
            'pekerjaan_status',
            'pekerjaan_nip',
            'pekerjaan_kantor',
            'pekerjaan_long',
            'pekerjaan_lat',
            'id_pasangan',
            'status_kedudukan',
            'status_tunjangan_efile',
            'bpjs_anak_efile',
            'nomor_akta_lahir_efile',
            'nomor_akta_mati_efile',
            'status_pegawai_efile',
            'surat_tidak_punya_penghasilan_anak_efile'
        ]);
        $err = 0;
        $message = "";

        DB::beginTransaction();
        try {
            if (((strlen($payload['nik']) < 16) && $payload['nik'] != '-') && ($payload['status_kedudukan'] == 1)) {
                $err = $err + 1;
                $message = "Status Anak masih hidup, tetapi NIK tidak valid. <br>";
            }
            if (($payload['status_asn'] == 2) && ($payload['status_kedudukan'] == 1) && isset($payload['pekerjaan_nip']) && isset($payload['nik'])) {
                $cekortu = EmployeeModel::where('nip', $payload['pekerjaan_nip'])
                            ->where('nik', '!=', $payload['nik'])
                            ->where('id', '!=', '');
                if ($cekortu->count() > 0) {
                    $err = $err + 1;
                    $message .= "NIP Anak tercatat sebagai ASN jawa Tengah, tetapi NIK tidak valid.";
                }
            }
            if ($err > 0) {
                throw new AppException($message, errors: @$cekortu->get());
                return response()->json(['status' => 'error', 'message' => $message, 'data' => @$cekortu->get()], JsonResponse::HTTP_NOT_FOUND);
            }
    
            unset($payload['created_at']);
            unset($payload['updated_at']);
    
            $employee = EmployeeModel::where('id', $employee_id)->first();
            $nip = $employee->nip;
            $timestamp = date("Y_m_d_His");
            $fileName = [];
    
            foreach ($payload as $key => $value) {
                if (str_contains($key, 'efile') && $request->hasFile($key)) {
                    $prefix = str_replace(['nomor_', '_efile', 'dokumen_'], ['', '', ''], $key);
                    $docName = implode('_', array_map('ucfirst', explode('_', strtolower($prefix))));
                    $fileName[$key] = "Dokumen_" . $docName . "_" . $nip . "_" . $timestamp;
                    $payload[$key] = FileHandler::store(
                        file: $request->file($key), 
                        targetDir: KeluargaAnakModel::ANAK_EFILE_DIR, 
                        fileName: $fileName[$key], 
                        isNeedException: false
                    );
                }
    
                // Error handling
                if (is_array($payload[$key]) && $payload[$key]['success'] === false) {
                    // Log::info($payload);
                    $payload[$key]['message'] = 'Terjadi kesalahan saat menyimpan Dokumen ' . str_replace('_', ' ', $docName) . ': ' . $payload[$key]['message'];
                    throw new AppException(
                        $payload[$key]['message'],
                        $payload[$key]['code'],
                        $payload[$key]['status']
                    );
                }
            }
    
            $riwayat = KeluargaAnakRepository::updateSubmission($riwayatId, $payload);

            DB::commit();
            return JsonResponseHandler::setResult($riwayat)->send();
        }
        catch (AppException $e){
            DB::rollBack();
            if (!empty($fileName)) {
                foreach ($payload as $key => $value) {
                    if (str_contains($key, 'efile') && is_string($payload[$key])) {
                        $ext = $request->file($key)->extension();
                        $payload[$key] = FileHandler::delete(KeluargaAnakModel::ANAK_EFILE_DIR . '/' . $fileName[$key] . '.' . $ext);
                    }
                }
            }
            throw $e;
        }
    }

    public function submissionDatatable(Request $request)
    {
        $employee_id = $request->input('employee_id');
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = KeluargaAnakTempModel::select("r_anak_temp.*", "a_pekerjaan.pekerjaan as pekerjaan_name", DB::raw("if(r_anak_temp.status_anak='A','Angkat',if(r_anak_temp.status_anak='K','Kandung',if(r_anak_temp.status_anak='T','Tiri','-'))) as status"))
            ->where('status', 0)
            ->leftJoin('a_pekerjaan', 'r_anak_temp.pekerjaan', '=', 'a_pekerjaan.id')
            ->with(['employee' => function ($query) {
                $query->select('id', 'nip', 'nama', 'gelar_depan', 'gelar_belakang');
            }])
            ->where('id_pegawai', $employee_id)
            ->paginate($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    // public function submissionDetail(Request $request, $submissionId)
    // {

    //     $submission = KeluargaAnakTempModel::where('id', $submissionId)->with(['initial'])->first();
    //     return JsonResponseHandler::setResult($submission)->send();
    // }

    // public function submissionApprove(Request $request, $submissionId)
    // {

    //     $submission = KeluargaAnakTempModel::where('id', $submissionId)->first();
    //     $payload = $request->only(['id','nip', 'nik', 'id_pasangan', 'id_pasangan_usul', 'status_asn', 'anak_ke', 'nama_anak', 'tempat_lahir', 'tanggal_lahir', 'status_anak', 'jenis_kelamin', 'umur', 'alamat', 'alamat_rt', 'alamat_rw', 'alamat_kode_pos', 'alamat_provinsi', 'alamat_kabupaten', 'alamat_kecamatan', 'alamat_kelurahan', 'alamat_long', 'alamat_lat', 'ktp_alamat', 'ktp_alamat_rt', 'ktp_alamat_rw', 'ktp_alamat_kode_pos', 'ktp_alamat_provinsi', 'ktp_alamat_kabupaten', 'ktp_alamat_kecamatan', 'ktp_alamat_kelurahan', 'ktp_alamat_long', 'ktp_alamat_lat', 'status_tunjangan', 'status_perkawinan', 'nomor_hp', 'nomor_bpjs', 'nomor_akta_lahir', 'tanggal_akta_lahir', 'status_hidup', 'kematian_tanggal', 'kematian_nomor', 'id_pendidikan', 'pekerjaan', 'pekerjaan_status', 'pekerjaan_nip', 'pekerjaan_kantor', 'pekerjaan_long', 'pekerjaan_lat', 'id_pasangan', 'status_kedudukan']);

    //     if ($submission->idjnsaksi == 1) {
    //         $payload['id_pegawai'] = $submission->id_pegawai;
    //         $riwayat = KeluargaAnakModel::create($payload);
    //     }
    //     if ($submission->idjnsaksi == 2) {
    //         $riwayat = KeluargaAnakModel::where('id', $submission->id_riwayat)->update($payload);
    //     }
    //     if ($submission->idjnsaksi == 3) {
    //         $riwayat = KeluargaAnakModel::where('id', $submission->id_riwayat)->delete();
    //     }
    //     KeluargaAnakTempModel::where('id', $submissionId)->update(['status' => 1]);
    //     return JsonResponseHandler::setResult(['status' => 'success'])->send();
    // }

    public function employeeSubmissionStatus(Request $request, $employeeId)
    {
        $statusCounts = KeluargaAnakTempModel::where('id_pegawai', $employeeId)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->mapWithKeys(function ($count, $status) {
                return ["status_{$status}" => $count];
            });

        return JsonResponseHandler::setResult($statusCounts)->send();
    }

    public function processSubmission(Request $request, $id)
    {
        $status = $request->input('status');
        $remark = $request->input('remark');

        $updated = (new KeluargaAnakModel())->processSubmission($id, $status, $remark);
        return JsonResponseHandler::setResult($updated)->send();
    }

    public function fieldDetail($id)
    {
        $data = (new KeluargaAnakModel())->getPdmFieldDetail($id);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function updateField(Request $request, $perubahan_data_id)
    {

        $field = $request->input('field');
        $value = $request->input('value');
        $status = $request->input('status');
        $remark = $request->input('remark');

        $updated = (new KeluargaAnakModel())->updatePdmField($perubahan_data_id, $field, $value, $status, $remark);
        return JsonResponseHandler::setResult($updated)->send();
    }

    public function timeline($id)
    {
        $data = (new KeluargaAnakModel())->getPdmTimeline($id);
        return JsonResponseHandler::setResult($data)->send();
    }
}
