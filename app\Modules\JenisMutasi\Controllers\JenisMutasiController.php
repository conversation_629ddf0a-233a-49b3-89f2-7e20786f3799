<?php

namespace App\Modules\JenisMutasi\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenisMutasi\Repositories\JenisMutasiRepository;
use App\Modules\JenisMutasi\Requests\JenisMutasiCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use App\Modules\JenisMutasi\Models\JenisMutasi;
use Illuminate\Http\Request;

class JenisMutasiController extends Controller
{
    public function index()
    {
        $data = JenisMutasi::select('id', 'jenismutasi')->get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenisMutasiRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('JenisMutasi::create');
    }

    public function store(JenisMutasiCreateRequest $request)
    {
        $payload = $request->all();
        $jenis_mutasi = JenisMutasiRepository::create($payload);
        return JsonResponseHandler::setResult($jenis_mutasi)->send();
    }

    public function show(Request $request, $id)
    {
        $jenis_mutasi = JenisMutasiRepository::get($id);
        return JsonResponseHandler::setResult($jenis_mutasi)->send();
    }

    public function edit($id)
    {
        return view('JenisMutasi::edit', ['jenis_mutasi_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jenis_mutasi = JenisMutasiRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jenis_mutasi)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JenisMutasiRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
