<?php

namespace App\Modules\Gaji;

use App\Modules\Gaji\Controllers\GajiController;
use App\Modules\Gaji\Controllers\GajiPnsController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/gaji')->group(function () {

    // SUB MENU MARKER (DONT DELETE THIS LINE)
    Route::prefix('/gaji-pns')->group(function () {
        Route::get('/datatable', [GajiPnsController::class, 'datatable']);
    });
    Route::get('/', [GajiController::class, 'index'])->middleware('authorize:read-gaji');
    Route::get('/datatable', [GajiController::class, 'datatable'])->middleware('authorize:read-gaji');
    Route::get('/create', [GajiController::class, 'create'])->middleware('authorize:create-gaji');
    Route::post('/', [GajiController::class, 'store'])->middleware('authorize:create-gaji');
    Route::get('/{gaji_id}', [GajiController::class, 'show'])->middleware('authorize:read-gaji');
    Route::get('/{gaji_id}/edit', [GajiController::class, 'edit'])->middleware('authorize:update-gaji');
    Route::put('/{gaji_id}', [GajiController::class, 'update'])->middleware('authorize:update-gaji');
    Route::delete('/{gaji_id}', [GajiController::class, 'destroy'])->middleware('authorize:delete-gaji');
});
