<?php
namespace App\Modules\GarudaBKDAPI\Repositories;

use Illuminate\Support\Facades\Http;
use App\Exceptions\AppException;

class GarudaBKDAPIRepository
{
    private $baseUrl;
    private $token;
    private $timeout;

    public function __construct()
    {
        $this->baseUrl = config('services.garuda_api.baseUrl');
        $this->token = config('services.garuda_api.token');
        $this->timeout = config('services.garuda_api.timeout');
        
        $baseUrl = $this->baseUrl;
        $timeout = $this->timeout;
        // dd($baseUrl, $timeout);

        Http::macro('garuda', function () use ($baseUrl, $timeout) {
            return Http::withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded'
            ])->timeout($timeout)
              ->connectTimeout($timeout)
              ->baseUrl($baseUrl)
              ->asForm();
        });
    }

    public function getTunjanganKeluarga($nip)
    {
        $response = Http::garuda()
            ->post("/siasnjtg/get_tunjangan", [
                "nip" => $nip,
                "token" => $this->token
            ]);
            
        if ($response->failed()) {
            throw new AppException("Gagal mendapatkan data tunjangan keluarga dari Garuda BKD API");
        }
        return $response->json();
    }
}
