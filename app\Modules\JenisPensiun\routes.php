<?php
namespace App\Modules\JenisPensiun;

use App\Modules\JenisPensiun\Controllers\JenisPensiunController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenis-pensiun')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenisPensiunController::class, 'index'])->middleware('authorize:read-jenis_pensiun');
    Route::get('/datatable', [JenisPensiunController::class, 'datatable'])->middleware('authorize:read-jenis_pensiun');
    Route::get('/create', [JenisPensiunController::class, 'create'])->middleware('authorize:create-jenis_pensiun');
    Route::post('/', [JenisPensiunController::class, 'store'])->middleware('authorize:create-jenis_pensiun');
    Route::get('/{jenis_pensiun_id}', [JenisPensiunController::class, 'show'])->middleware('authorize:read-jenis_pensiun');
    Route::get('/{jenis_pensiun_id}/detail', [JenisPensiunController::class, 'detail'])->middleware('authorize:read-jenis_pensiun');
    Route::get('/{jenis_pensiun_id}/edit', [JenisPensiunController::class, 'edit'])->middleware('authorize:update-jenis_pensiun');
    Route::put('/{jenis_pensiun_id}', [JenisPensiunController::class, 'update'])->middleware('authorize:update-jenis_pensiun');
    Route::patch('/{jenis_pensiun_id}', [JenisPensiunController::class, 'update'])->middleware('authorize:update-jenis_pensiun');
    Route::delete('/{jenis_pensiun_id}', [JenisPensiunController::class, 'destroy'])->middleware('authorize:delete-jenis_pensiun');
});