<?php
namespace App\Modules\InstansiInduk;

use App\Modules\InstansiInduk\Controllers\InstansiIndukController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/instansi-induk')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [InstansiIndukController::class, 'index'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/datatable', [InstansiIndukController::class, 'datatable']);
    Route::get('/create', [InstansiIndukController::class, 'create']);
    Route::post('/', [InstansiIndukController::class, 'store']);
    Route::get('/{instansi_induk_id}', [InstansiIndukController::class, 'show']);
    Route::get('/{instansi_induk_id}/detail', [InstansiIndukController::class, 'detail']);
    Route::get('/{instansi_induk_id}/edit', [InstansiIndukController::class, 'edit']);
    Route::put('/{instansi_induk_id}', [InstansiIndukController::class, 'update']);
    Route::patch('/{instansi_induk_id}', [InstansiIndukController::class, 'update']);
    Route::delete('/{instansi_induk_id}', [InstansiIndukController::class, 'destroy']);
});
