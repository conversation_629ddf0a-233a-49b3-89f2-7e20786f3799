<?php

namespace App\Modules\GolonganRuang\Repositories;

use App\Modules\GolonganRuang\Models\GolonganRuang;
use App\Modules\GolonganRuang\Models\RiwayatGolongan;

class GolonganRuangRepository
{
    public static function datatable($per_page = 15)
    {
        $data = GolonganRuang::paginate($per_page);
        return $data;
    }
    public static function employeeDatatable($employeeId, $per_page = 15)
    {
        $data = RiwayatGolongan::where('id_pegawai', $employeeId)->with(['golru'])->paginate($per_page);
        return $data;
    }
    public static function get($golongan_ruang_id)
    {
        $golongan_ruang = GolonganRuang::where('id', $golongan_ruang_id)->first();
        return $golongan_ruang;
    }
    public static function create($golongan_ruang)
    {
        $golongan_ruang = GolonganRuang::create($golongan_ruang);
        return $golongan_ruang;
    }

    public static function update($golongan_ruang_id, $golongan_ruang)
    {
        GolonganRuang::where('id', $golongan_ruang_id)->update($golongan_ruang);
        $golongan_ruang = GolonganRuang::where('id', $golongan_ruang_id)->first();
        return $golongan_ruang;
    }

    public static function delete($golongan_ruang_id)
    {
        $delete = GolonganRuang::where('id', $golongan_ruang_id)->delete();
        return $delete;
    }
}
