<?php

namespace App\Modules\DiklatStruktural\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\DiklatStruktural\Models\DiklatStruktural;
use App\Modules\DiklatStruktural\Repositories\DiklatStrukturalRepository;
use App\Modules\DiklatStruktural\Requests\DiklatStrukturalCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class DiklatStrukturalController extends Controller
{
    public function index(Request $request)
    {
        $diklat_struktural = DiklatStruktural::get();
        return JsonResponseHandler::setResult($diklat_struktural)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = DiklatStrukturalRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('DiklatStruktural::create');
    }

    public function store(Request $request)
    {
        $payload = $request->all();
        $diklat_struktural = DiklatStrukturalRepository::create($payload);
        return JsonResponseHandler::setResult($diklat_struktural)->send();
    }

    public function show(Request $request, $id)
    {
        $diklat_struktural = DiklatStrukturalRepository::get($id);
        return JsonResponseHandler::setResult($diklat_struktural)->send();
    }

    public function edit($id)
    {
        return view('DiklatStruktural::edit', ['diklat_struktural_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $diklat_struktural = DiklatStrukturalRepository::update($id, $payload);
        return JsonResponseHandler::setResult($diklat_struktural)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = DiklatStrukturalRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
