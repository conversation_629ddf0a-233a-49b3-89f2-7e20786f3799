<?php

namespace App\Helpers;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Config;

class JWTHelper
{
    public static function generateToken($data, $expiryMinutes = 60 * 8)
    {
        $payload = [
            'iss' => ('app.url'), // Issuer
            'aud' => ('app.url'), // Audience
            'iat' => time(), // Issued at
            'exp' => time() + ($expiryMinutes * 60), // Expiry
            'data' => $data // Data
        ];

        return JWT::encode($payload, env('JWT_SECRET'), 'HS256');
    }

    public static function generateTokenWithExpiry($data, $expiryMinutes = 60 * 8)
{
    $issuedAt = time();
    $expiresAt = $issuedAt + ($expiryMinutes * 60);

    $payload = [
        'iss' => env('app.url'), // Issuer
        'aud' => env('app.url'), // Audience
        'iat' => $issuedAt, // Issued at
        'exp' => $expiresAt, // Expiry
        'data' => $data // Data
    ];

    $token = JWT::encode($payload, env('JWT_SECRET'), 'HS256');

    return [
        'access_token' => $token,
        'expires_in' => $expiryMinutes * 60, // seconds until expiry
        'expires_at' => $expiresAt, // unix timestamp
        'expires_at_formatted' => date('Y-m-d H:i:s', $expiresAt), // human readable
        'token_type' => 'Bearer'
    ];
}

    public static function generateLimitedToken($data, $expiryMinutes = 60)
    {
        $payload = [
            'iss' => ('app.url'), // Issuer
            'aud' => ('app.url'), // Audience
            'iat' => time(), // Issued at
            'exp' => time() + ($expiryMinutes * 60), // Expiry
            'data' => $data // Data
        ];

        return JWT::encode($payload, env('JWT_LIMITED_SECRET'), 'HS256');
    }

    public static function validateToken($token)
    {
        try {
            $decoded = JWT::decode($token, new Key(env('JWT_SECRET'), 'HS256'));
            return (array) $decoded->data;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function validateLimitedToken($token)
    {
        try {
            $decoded = JWT::decode($token, new Key(env('JWT_LIMITED_SECRET'), 'HS256'));
            return (array) $decoded->data;
        } catch (\Exception $e) {
            return null;
        }
    }
}
