<?php

namespace App\Modules\JenJurusan\Models;
    
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\TkPendid\Models\TkPendid;
use App\Modules\RumpunPendidikan\Models\RumpunPendidikan;
use App\Handler\ModelSearchHandler;

class JenJurusan extends Model
{
    use SoftDeletes;
    protected $table = 'a_jenjurusan';
    protected $guarded = [];
    protected $appends = ['formatted_tkpendid', 'formatted_rumpunpendid'];
    protected $primaryKey = 'idjenjurusan';
    protected $casts = ['idjenjurusan' => 'string'];
    public $timestamps = true;

    public function scopeSearch($query, $keyword)
    {
        $searchable = ['jenjurusan', 'tkpendid.tkpendid', 'rumpunpendid.rumpunpendid'];
        return ModelSearchHandler::handle($query, $searchable, $keyword);
    }

    public function tkpendid()
    {
        return $this->hasOne(TkPendid::class, 'idtkpendid', 'idtkpendid');
    }

    public function rumpunpendid()
    {
        return $this->hasOne(RumpunPendidikan::class, 'idrumpunpendid', 'idrumpunpendid');
    }

    protected function getFormattedTkpendidAttribute()
    {
        $tkpendid = $this->tkpendid()->first();
        return @$tkpendid->tkpendid;
    }

    protected function getFormattedRumpunPendidAttribute()
    {
        $rumpunpendid = $this->rumpunpendid()->first();
        return @$rumpunpendid->rumpunpendid;
    }
}