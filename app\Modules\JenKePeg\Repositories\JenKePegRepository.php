<?php

namespace App\Modules\JenKePeg\Repositories;

use App\Modules\JenKePeg\Models\JenKePeg;

class JenKePegRepository
{
    public static function datatable($per_page = 15)
    {
        $data = JenKePeg::paginate($per_page);
        return $data;
    }
    public static function get($jenkepeg_id)
    {
        $jenkepeg = JenKePeg::where('id', $jenkepeg_id)->first();
        return $jenkepeg;
    }
    public static function create($jenkepeg)
    {
        $jenkepeg = JenKePeg::create($jenkepeg);
        return $jenkepeg;
    }

    public static function update($jenkepeg_id, $jenkepeg)
    {
        JenKePeg::where('id', $jenkepeg_id)->update($jenkepeg);
        $jenkepeg = JenKePeg::where('id', $jenkepeg_id)->first();
        return $jenkepeg;
    }

    public static function delete($jenkepeg_id)
    {
        $delete = JenKePeg::where('id', $jenkepeg_id)->delete();
        return $delete;
    }
}
