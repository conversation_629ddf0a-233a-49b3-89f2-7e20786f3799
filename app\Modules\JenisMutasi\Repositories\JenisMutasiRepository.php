<?php

namespace App\Modules\JenisMutasi\Repositories;

use App\Modules\JenisMutasi\Models\JenisMutasi;

class JenisMutasiRepository
{
    public static function datatable($per_page = 15)
    {
        $data = JenisMutasi::paginate($per_page);
        return $data;
    }
    public static function get($jenis_mutasi_id)
    {
        $jenis_mutasi = JenisMutasi::where('id', $jenis_mutasi_id)->first();
        return $jenis_mutasi;
    }
    public static function create($jenis_mutasi)
    {
        $jenis_mutasi = JenisMutasi::create($jenis_mutasi);
        return $jenis_mutasi;
    }

    public static function update($jenis_mutasi_id, $jenis_mutasi)
    {
        JenisMutasi::where('id', $jenis_mutasi_id)->update($jenis_mutasi);
        $jenis_mutasi = JenisMutasi::where('id', $jenis_mutasi_id)->first();
        return $jenis_mutasi;
    }

    public static function delete($jenis_mutasi_id)
    {
        $delete = JenisMutasi::where('id', $jenis_mutasi_id)->delete();
        return $delete;
    }
}
