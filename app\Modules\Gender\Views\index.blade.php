@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="gender">
    <default-datatable title="Gender" url="{!! url('gender') !!}" :headers="headers" :can-add="{{ $permissions['create-gender'] }}" :can-edit="{{ $permissions['update-gender'] }}" :can-delete="{{ $permissions['delete-gender'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'name',
        						text: 'name'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#gender');
</script>
@endsection