@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="child-status">
    <default-datatable title="ChildStatus" url="{!! url('child-status') !!}" :headers="headers" :can-add="{{ $permissions['create-child_status'] }}" :can-edit="{{ $permissions['update-child_status'] }}" :can-delete="{{ $permissions['delete-child_status'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'name',
        						text: 'name'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#child-status');
</script>
@endsection