<?php

namespace App\Modules\GolDarah\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\GolDarah\Models\GolDarah;
use App\Modules\GolDarah\Repositories\GolDarahRepository;
use App\Modules\GolDarah\Requests\GolDarahCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class GolDarahController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('GolDarah::index', ['permissions' => $permissions]);
    }

    public function get()
    {
        $data = GolDarah::get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = GolDarahRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $goldarah_id)
    {
        $goldarah = GolDarah::where('id', $goldarah_id)->first();
        return JsonResponseHandler::setResult($goldarah)->send();
    }

    public function create()
    {
        return view('GolDarah::create');
    }

    public function store(GolDarahCreateRequest $request)
    {
        $payload = $request->all();
        $goldarah = GolDarahRepository::create($payload);
        return JsonResponseHandler::setResult($goldarah)->send();
    }

    public function show(Request $request, $id)
    {
        $goldarah = GolDarahRepository::get($id);
        return JsonResponseHandler::setResult($goldarah)->send();
    }

    public function edit($id)
    {
        return view('GolDarah::edit', ['goldarah_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $goldarah = GolDarahRepository::update($id, $payload);
        return JsonResponseHandler::setResult($goldarah)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = GolDarahRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
