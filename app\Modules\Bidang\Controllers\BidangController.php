<?php

namespace App\Modules\Bidang\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Bidang\Models\Bidang;
use App\Modules\Bidang\Models\BidangLayanan;
use App\Modules\Bidang\Repositories\BidangRepository;
use App\Modules\Bidang\Requests\BidangCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class BidangController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('Bidang::index', ['permissions' => $permissions]);
    }

    public function all(Request $request)
    {
        $data = Bidang::get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = BidangRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function servicesDatatable(Request $request, $bidangId)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = BidangLayanan::where('bidang_id', $bidangId)->paginate($per_page);

        return JsonResponseHandler::setResult($data)->send();
    }
    public function serviceAdd(Request $request, $bidangId)
    {
        $service = $request->input('service');
        $existingData = BidangLayanan::where('bidang_id', $bidangId)
            ->where('service', $service)
            ->first();
        if (!empty($existingData)) {
            return JsonResponseHandler::setResult($existingData)->send();
        }
        $data = BidangLayanan::create([
            'bidang_id' => $bidangId,
            'service' => $service
        ]);
        return JsonResponseHandler::setResult($data)->send();
    }
    public function serviceDelete(Request $request, $bidangId, $itemId)
    {
        $delete = BidangLayanan::where('id', $itemId)
            ->delete();

        return JsonResponseHandler::setResult($delete)->send();
    }

    public function create()
    {
        return view('Bidang::create');
    }

    public function store(BidangCreateRequest $request)
    {
        $payload = $request->all();
        $bidang = BidangRepository::create($payload);
        return JsonResponseHandler::setResult($bidang)->send();
    }

    public function show(Request $request, $id)
    {
        $bidang = BidangRepository::get($id);
        return JsonResponseHandler::setResult($bidang)->send();
    }

    public function edit($id)
    {
        return view('Bidang::edit', ['bidang_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $bidang = BidangRepository::update($id, $payload);
        return JsonResponseHandler::setResult($bidang)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = BidangRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
