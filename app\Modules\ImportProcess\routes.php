<?php

namespace App\Modules\ImportProcess;

use App\Modules\ImportProcess\Controllers\ImportProcessController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/import-process')->group(function () {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/datatable', [ImportProcessController::class, 'datatable']);
    Route::get('/{id}/download-error', [ImportProcessController::class, 'downloadError']);
});
