<?php

namespace App\Modules\ExecutiveSummary\Repositories;

use App\Modules\ExecutiveSummary\Models\ExecutiveSummary;

class ExecutiveSummaryRepository
{
    public static function datatable($per_page = 15)
    {
        $data = ExecutiveSummary::paginate($per_page);
        return $data;
    }
    public static function get($executive_summary_id)
    {
        $executive_summary = ExecutiveSummary::where('id', $executive_summary_id)->first();
        return $executive_summary;
    }
    public static function create($executive_summary)
    {
        $executive_summary = ExecutiveSummary::create($executive_summary);
        return $executive_summary;
    }

    public static function update($executive_summary_id, $executive_summary)
    {
        ExecutiveSummary::where('id', $executive_summary_id)->update($executive_summary);
        $executive_summary = ExecutiveSummary::where('id', $executive_summary_id)->first();
        return $executive_summary;
    }

    public static function delete($executive_summary_id)
    {
        $delete = ExecutiveSummary::where('id', $executive_summary_id)->delete();
        return $delete;
    }
}
