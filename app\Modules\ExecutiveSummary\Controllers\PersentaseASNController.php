<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PersentaseASNController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('ExecutiveSummary::persentase-asn.index', ['permissions' => $permissions]);
    }

    // public function datatable(Request $request)
    // {
    //     $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
    //     $data = ExecutiveSummaryRepository::datatable($per_page);
    //     return JsonResponseHandler::setResult($data)->send();
    // }

    public function datatable(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja');
        $status_pegawai = $request->input('status_pegawai');
        $id_waktu_ultah = $request->input('id_waktu_ultah');
        
        $data = DB::table('tb_01')
                ->select(
                    'tb_01.id','tb_01.nip','tb_01.nama','tb_01.tanggal_lahir',
                    DB::raw("
                        DATE_FORMAT(
                            FROM_DAYS(
                                TO_DAYS(NOW()) - TO_DAYS(tb_01.tanggal_lahir)
                            ), 
                            '%Y'
                        ) + 0 AS usia
                    "),
                )
                ->whereNotNull('tb_01.tanggal_lahir')
                ->whereNotIn('tb_01.kedudukan_pegawai', [99, 21]);

                if ($id_unit_kerja != null && $id_unit_kerja != "") {
                    list($id_unit_kerja, $id_induk_upt, $id_sub_unit, $id_sub_sub_unit, $id_sub_sub_sub_unit)=explode(".", $id_unit_kerja);
                    if ($id_unit_kerja != null && $id_unit_kerja != "") { //opd besar
                        $data = $data->where('tb_01.id_unit_kerja', [$id_unit_kerja]);
                    }
        
                    if ($id_induk_upt != '00') {
                        $data = $data->where('tb_01.id_induk_upt', [$id_induk_upt]);
                    }
        
                    if ($id_sub_unit != '00') { 
                        $data = $data->where('tb_01.id_sub_unit', [$id_sub_unit]);
                    }
        
                    if ($id_sub_sub_unit != '00') {
                        $data = $data->where('tb_01.id_sub_sub_unit', [$id_sub_sub_unit]);
                    }
        
                    if ($id_sub_sub_sub_unit != '00') {
                        $data = $data->where('tb_01.id_sub_sub_sub_unit', [$id_sub_sub_sub_unit]);
                    }
                }

                if ($status_pegawai != null && $status_pegawai != "") {
                    $data = $data->where('status_pegawai', [$status_pegawai]);
                }

                if($id_waktu_ultah != null && $id_waktu_ultah != ""){
                    if($id_waktu_ultah < 0){
                        $data = $data->whereRaw("RIGHT(tb_01.tanggal_lahir, 5)=RIGHT(DATE_ADD(CURDATE(), INTERVAL ".$id_waktu_ultah." DAY), 5)");
                    }else if($id_waktu_ultah > 5){
                        $data = $data->whereRaw("MID(tb_01.tanggal_lahir, 6, 2)=MID(DATE_ADD(CURDATE(), INTERVAL +".substr($id_waktu_ultah, 1,1)." MONTH), 6, 2)");
                    }else {
                        $data = $data->whereRaw("RIGHT(tb_01.tanggal_lahir, 5)=RIGHT(DATE_ADD(CURDATE(), INTERVAL +".$id_waktu_ultah." DAY), 5)");
                    }
            
                }else{
                    //default hari ini
                    $data = $data->whereRaw("DAY(tb_01.tanggal_lahir) = DAY(CURDATE()) AND MONTH(tb_01.tanggal_lahir) = MONTH(CURDATE())");
                }

        $data = $data->orderBy('tb_01.tanggal_lahir', 'desc')->limit(10)->paginate();

        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable_hukdis(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('komp_kasus_sample as kks')
            ->join('tb_01', 'kks.nip', '=', 'tb_01.nip')
            ->join('tabhkdis as th', 'kks.JENISHKM', '=', 'th.hkdis')
            ->join('tabpelanggaran as tp', 'kks.JNSKASUS', '=', 'tp.id_langgar')                
            ->select(
                'kks.JNSKASUS', 'tp.pelanggaran',                    
                DB::raw("sum(if(kks.JENISHKM=1,1,0)) teguran_lisan"),
                DB::raw("sum(if(kks.JENISHKM=2,1,0)) teguran_tulis"),
                DB::raw("sum(if(kks.JENISHKM=3,1,0)) tidak_puas"),
                DB::raw("sum(if(kks.JENISHKM=4,1,0)) penundaan_kgb_1thn"),
                DB::raw("sum(if(kks.JENISHKM=5,1,0)) penundaan_kp_1thn"),
                DB::raw("sum(if(kks.JENISHKM=6,1,0)) penurunan_kp_1thn"),
                DB::raw("sum(if(kks.JENISHKM=7,1,0)) penurunan_kp_3thn"),
                DB::raw("sum(if(kks.JENISHKM=8,1,0)) pemindahan_penurunan"),
                DB::raw("sum(if(kks.JENISHKM=9,1,0)) pembebasan_pemberhentian"),
                DB::raw("sum(if(kks.JENISHKM=10,1,0)) pemberhentian_dengan_hormat"),
                DB::raw("sum(if(kks.JENISHKM=11,1,0)) pemberhentian_tidak_hormat"),
                DB::raw("sum(if(kks.JENISHKM=12,1,0)) penurunan_jabatan_setingkat"),
                DB::raw("sum(if(kks.JENISHKM=13,1,0)) pembebasan_dari_jabatan"),
                DB::raw("sum(if(kks.JENISHKM=14,1,0)) sanksi_sosial_pemajangan"),
                DB::raw("sum(if(kks.JENISHKM=15,1,0)) penurunan_golongan_setingkat"),
                DB::raw("sum(if(kks.JENISHKM=16,1,0)) pemutusan_hubungan_perjanjian"),
                DB::raw("sum(if(kks.JENISHKM=17,1,0)) pemberhentian_tidak_hormat_taps"),
                DB::raw("sum(if(kks.JENISHKM=18,1,0)) pemutusan_hubungan_perjanjian_tdh"),
                DB::raw("sum(if(kks.JENISHKM=19,1,0)) pemotongan_gaji_5persen_6bulan"),
                DB::raw("sum(if(kks.JENISHKM=20,1,0)) pemotongan_gaji_5persen_9bulan"),
                DB::raw("sum(if(kks.JENISHKM=21,1,0)) peotongan_tpp_25persen_6bulan"),
                DB::raw("sum(if(kks.JENISHKM=22,1,0)) peotongan_tpp_25persen_9bulan"),
                DB::raw("sum(if(kks.JENISHKM=23,1,0)) peotongan_tpp_25persen_bulan"),
                DB::raw("count(*) as jml")
            )
            ->groupBy('kks.JNSKASUS')
            ->orderBy('kks.JNSKASUS', 'asc');

            if (!empty($id_unit_kerja)) {
                if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                    $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                        ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                        ->where('tb_01.id_sub_unit', $pecah_sub_unit)
                        ->where('tb_01.id_sub_sub_unit', $pecah_sub_sub_unit)
                        ->where('tb_01.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                } elseif (!empty($id_sub_unit)) {
                    $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                        ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                        ->where('tb_01.id_sub_unit', $pecah_sub_unit);
                } else {
                    $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja);
                }
            }

            if (!empty($month) and !empty($year)) {
                $data = $data->whereRaw("month(TGLSK) = \"".$month."\" and year(TGLSK) = \"".$year."\"");
            }

        return JsonResponseHandler::setResult($data->paginate())->send();
    }
}
