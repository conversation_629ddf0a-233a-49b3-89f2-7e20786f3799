<?php

namespace App\Jobs;

use Exception;
use App\Exceptions\AppException;
use Illuminate\Queue\MaxAttemptsExceededException;

use App\Modules\Employee\Repositories\EmployeeRepository;
use App\Modules\ImportProcess\Models\ImportProcessDetailModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Maatwebsite\Excel\Events\ImportFailed;
use Maatwebsite\Excel\Facades\Excel;

class EmployeeCreationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $payload;
    protected $importId;

    /**
     * Create a new job instance.
     *
     * @param array $rowData
     * @return void
     */
    public function __construct(array $payload, $importId = null)
    {
        $this->payload = $payload;
        $this->importId = $importId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            EmployeeRepository::create($this->payload);
            if (!empty($this->importId)) {
                ImportProcessDetailModel::create(
                    [
                        'import_id' => $this->importId,
                        'data' => json_encode($this->payload),
                        'is_success' => true,
                        'remark' => null
                    ]
                );
            }
        } catch (Exception $err) {
            if (!empty($this->importId)) {
                ImportProcessDetailModel::create(
                    [
                        'import_id' => $this->importId,
                        'data' => json_encode($this->payload),
                        'is_success' => false,
                        'remark' => $err->getMessage()
                    ]
                );
            }
        } catch (AppException $err) {
            if (!empty($this->importId)) {
                ImportProcessDetailModel::create(
                    [
                        'import_id' => $this->importId,
                        'data' => json_encode($this->payload),
                        'is_success' => false,
                        'remark' => $err->message
                    ]
                );
            }
        } catch (MaxAttemptsExceededException $err) {
            if (!empty($this->importId)) {
                ImportProcessDetailModel::create(
                    [
                        'import_id' => $this->importId,
                        'data' => json_encode($this->payload),
                        'is_success' => false,
                        'remark' => $err->getMessage()
                    ]
                );
            }
        }
    }
}
