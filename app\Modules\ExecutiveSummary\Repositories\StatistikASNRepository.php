<?php
namespace App\Modules\ExecutiveSummary\Repositories;

use Illuminate\Http\Request;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Kepangkatan\Models\Kepangkatan;
use App\Modules\UnitKerja\Models\UnitKerjaModel;
use App\Modules\TkPendid\Models\TkPendid;
use Illuminate\Support\Facades\DB;


class StatistikASNRepository 
{
    public static function handleQueryParam(Request $request)
    {
        $param['id_unit_kerja'] = $request->input('id_unit_kerja'); //B2
        $param['id_induk_upt'] = $request->input('id_induk_upt');
        $param['id_sub_unit'] = $request->input('id_sub_unit'); //B2.00.50
        $param['id_sub_sub_unit'] = $request->input('id_sub_sub_unit'); //B2.00.50.00.00
        $param['id_sub_sub_sub_unit'] = $request->input('id_sub_sub_sub_unit');
   
        $param['id_kategori'] = $request->input('id_kategori');
        $param['id_jenjab'] = $request->input('id_jenjab');
        $param['status_pegawai'] = $request->input('status_pegawai');
        $param['gender_id'] = $request->input('gender_id');
        $param['id_jenkedudupeg'] = $request->input('id_jenkedudupeg');

        $param['pendidikan_id'] = $request->input('pendidikan_id');
        $param['golongan_id'] = $request->input('golongan_id');
        $param['is_doesnt_have_education'] = $request->input('is_doesnt_have_education');
        $param['is_doesnt_have_eselon'] = $request->input('is_doesnt_have_eselon');
        $param['is_empty'] = $request->input('is_empty');
        $param['eselon_id'] = $request->input('eselon_id');
        $param['agama_id'] = $request->input('agama_id');
        $param['age_range'] = (int) $request->input('age_range');
        $param['status_kawin'] = $request->input('status_kawin');
        $param['issek'] = $request->input('issek');
        $param['idjabfungum'] = $request->input('idjabfungum');

        if (!empty($param['id_sub_unit']) && str_contains($param['id_sub_unit'], '.')) {
            $param['id_induk_upt'] = substr($param['id_sub_unit'], 3, 2); // 00
            $param['id_sub_unit'] = substr($param['id_sub_unit'], 6, 2); // 50
            $param['id_sub_sub_unit'] = substr($param['id_sub_sub_unit'], 9, 2); // 00
            $param['id_sub_sub_sub_unit'] = substr($param['id_sub_sub_unit'], 12, 2); // 00
        }
        // dd($param);
        return $param;
    }

    public static function handleTkPendid()
    {
        $tkpendid = TkPendid::select('idtkpendid', 'tkpendid')->get()->toArray();
        $tkpendid[] = [
            'idtkpendid' => null,
            'tkpendid' => 'total'
        ];
        $tkpendid[] = [
            'idtkpendid' => '',
            'tkpendid' => 'kosong'
        ];

        foreach ($tkpendid as $key => $pend) {
            $tkpendid[$key]['tkpendid'] = str_replace(['-', ' '], ['', '_'], $pend['tkpendid']);
        }

        return $tkpendid;
    }

    public static function handleKepangkatan()
    {
        $kepangkatan = Kepangkatan::select('id', 'name')->get()->toArray();
        $kepangkatan[] = [
            'id' => null,
            'name' => 'total'
        ];
        $kepangkatan[] = [
            'id' => '',
            'name' => 'kosong'
        ];

        foreach ($kepangkatan as $key => $pangkat) {
            $kepangkatan[$key]['name'] = str_replace('/', '', $pangkat['name']);
        }

        return $kepangkatan;
    } 

    public static function getPDFQuery(Request $request, $is_skpd = false)
    {
        $skpd = $is_skpd ? 'skpd' : 'path';
        $param = self::handleQueryParam($request);

        $data = EmployeeModel::with([
            'riwayatJabatan' => function ($query) {
                $query->select('id', 'id_pegawai', 'id_eselon', 'isakhir', 'jabatan', 'tmt_jabatan')
                    ->where('isakhir', 1);
            },
            'riwayatPendidikan' => function ($query) use ($param) {
                $query->select('id', 'id_pegawai', 'id_jenjang', 'isakhir', 'jenjang')
                    ->where('isakhir', 1)
                    ->with([
                        'tkpendid' => function ($query) {
                            $query->select('idtkpendid', 'singkatan as pend');
                        }
                    ]);
            },
            'riwayatPangkat' => function ($query) {
                $query->select(
                        'id', 'id_pegawai', 'id_kepangkatan', 'isakhir', 'id_penetap',
                        'tanggal_sk', 'tmt_sk', 'masa_kerja_bulan', 'masa_kerja_tahun'
                    )
                    ->where('isakhir', 1)
                    ->with([
                        'golru' => function ($query) {
                            $query->select('id', 'name as golongan', 'pangkat', 'golru_p3k');
                        }
                    ]);
            },
        ])
        ->leftJoin('a_skpd', function ($join) {
            $join->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                ->where('a_skpd.flag', 1);
        })
        ->select(
            'tb_01.id', 'tb_01.id_unit_kerja', 'tb_01.id_induk_upt', 'tb_01.id_sub_unit', 
            'tb_01.id_sub_sub_unit', 'tb_01.id_sub_sub_sub_unit', 'tb_01.nip', 
            'tb_01.nama', 'tb_01.tempat_lahir', 'tb_01.tanggal_lahir', 'tb_01.nomor_kartu_asn', 'tb_01.agama',
            'a_skpd.'.$skpd.' as skpd', 'a_skpd.issek',
        )
        ->when(!empty($param['id_unit_kerja']), function ($query) use ($param) {
            $query->where('tb_01.id_unit_kerja', $param['id_unit_kerja']);
        })
        ->when(!empty($param['id_induk_upt'] && $param['id_induk_upt'] != '00'), function ($query) use ($param) {
            $query->where('tb_01.id_induk_upt', $param['id_induk_upt']);
        })
        ->when(!empty($param['id_sub_unit'] && $param['id_sub_unit'] != '00'), function ($query) use ($param) {
            $query->where('tb_01.id_sub_unit', $param['id_sub_unit']);
        })
        ->when(!empty($param['id_sub_sub_unit'] && $param['id_sub_sub_unit'] != '00'), function ($query) use ($param) {
            $query->where('tb_01.id_sub_sub_unit', $param['id_sub_sub_unit']);
        })
        ->when(!empty($param['id_sub_sub_sub_unit'] && $param['id_sub_sub_sub_unit'] != '00'), function ($query) use ($param) {
            $query->where('tb_01.id_sub_sub_sub_unit',  $param['id_sub_sub_sub_unit']);
        })
        ->when(!empty($param['id_jenjab']), function ($query) use ($param) {
            $query->whereHas('riwayatJabatan', function ($query) use ($param) {
                $query->where('isakhir', 1)
                    ->where('id_jenis_jabatan', $param['id_jenjab']);
            });
        })
        ->when(!empty($param['status_pegawai']), function ($query) use ($param) {
            $query->where('tb_01.status_pegawai', $param['status_pegawai']);
        })
        // ->whereNotNull('a_skpd.skpd')
        ->orderBy('tb_01.nama');

        return $data;
    }

    // Query datatable dan print pdf
    public static function queryTablePendidikanFormal(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.00.50.00.00

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_pendidikan_formal as a', function ($join) {
                $join
                    ->on('b.id', '=', 'a.id_pegawai')
                    ->where('a.isakhir', '=', 1);
            })
            ->leftJoin('a_tkpendid as d', 'a.id_jenjang', '=', 'd.idtkpendid')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id_jenjang',
                DB::raw("IF(d.tkpendid IS NULL, 1, 0) as 'is_doesnt_have_education'"),
                DB::raw("IF(d.tkpendid IS NULL, 'DATA TIDAK LENGKAP', d.tkpendid) as 'pendidikan_formal'"),
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            // ->whereNotNull('id_jenjang')
            ->groupBy('a.id_jenjang')
            ->orderBy('a.id_jenjang', 'asc');
                // dd($data->get());

        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja]);
        }

        if ($id_sub_unit != null && $id_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit]);
        }

        if ($id_sub_sub_unit != null && $id_sub_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit])
                ->where('b.id_sub_sub_unit', [$pecah_sub_sub_unit])
                ->where('b.id_sub_sub_sub_unit', [$pecah_sub_sub_sub_unit]);
        }


        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();
        return $data;
    }

    public static function queryUnitKerjaPendidikanFormal(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        $tkpendid = StatistikASNRepository::handleTkPendid();

        $employeeCount = [];
        foreach ($tkpendid as $pend) {
            $employeeCount['employees as '.str_replace(' ', '_', $pend['tkpendid'])] = function ($query) use ($param, $pend) {
                $query->whereHas('riwayatPendidikan', function ($query) use ($param, $pend) {
                    $query->where('isakhir', 1)
                        ->when(!empty($param['id_unit_kerja']), function ($query) use ($param) {
                            $query->where('id_unit_kerja', $param['id_unit_kerja']);
                        })
                        ->when(!empty($param['status_pegawai']), function ($query) use ($param) {
                            $query->where('status_pegawai', $param['status_pegawai']);
                        })
                        ->when($pend['idtkpendid'], function ($query) use ($pend) {
                            $query->where('id_jenjang', $pend['idtkpendid']);
                        })
                        ->when($pend['tkpendid'] == 'kosong', function ($query) {
                            $query->where('id_jenjang', '')
                                ->orWhereNull('id_jenjang');
                        })
                        ->whereColumn('tb_01.id_unit_kerja', 'a_skpd.id_unit_kerja')
                        ->when('tb_01.id_induk_upt' != '00', function ($query) {
                            $query->whereColumn('tb_01.id_induk_upt', 'a_skpd.id_induk_upt')
                                ->whereColumn('tb_01.id_sub_unit', 'a_skpd.id_sub_unit')
                                ->whereColumn('tb_01.id_sub_sub_unit', 'a_skpd.id_sub_sub_unit')
                                ->whereColumn('tb_01.id_sub_sub_sub_unit', 'a_skpd.id_sub_sub_unit');
                        });
                });
            };
            unset($pend);
        }
        gc_collect_cycles();

        $data = UnitKerjaModel::unitKerjaActive($param)
                ->select(
                    'idskpd',
                    'id_unit_kerja',
                    'id_induk_upt',
                    'id_sub_unit',
                    'id_sub_sub_unit',
                    'id_sub_sub_sub_unit',
                    'path as skpd'
                )
                ->withCount($employeeCount)
                ->orderBy('skpd')
                ->groupBy('id_unit_kerja', 'id_induk_upt', 'id_sub_unit', 'id_sub_sub_unit', 'id_sub_sub_sub_unit')
                // ->groupBy('id_sub_unit')
                ->get();
        
        gc_collect_cycles();

        return $data;
    }

    public static function queryJenisKelaminGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.00.50.00.00

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'kepangkatan.id',
                'kepangkatan.name as pangkat',
                DB::raw("SUM(if(b.jenis_kelamin!='',1,0)) as 'total'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'laki'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'perempuan'"),
            )
            ->groupBy('pangkat')
            ->orderBy('kepangkatan.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return $data->get();
    }

    public static function queryUnitKerjaGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.00.50.00.00

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            // ->leftjoin('tb_01 as b', 'a.idskpd', '=', 'b.id_unit_kerja')
            ->select(
                'a.id_unit_kerja',
                'a.id_induk_upt',
                'a.id_sub_unit',
                'a.id_sub_sub_unit',
                'a.id_sub_sub_sub_unit',
                'a.path as unit_kerja',
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            );

        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data->leftjoin('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->join('r_kepangkatan as c', function ($join) {
            $join
                ->on('b.id', '=', 'c.id_pegawai')
                ->where('c.isakhir', '=', 1);
        })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return $data->get();
    }

    public static function sumGolonganRow($data)
    {
        $data = $data->toArray();
        $sum = [
            'total' => array_sum(array_column($data, 'total')),
            'kosong' => array_sum(array_column($data, 'kosong')),
            'Ia' => array_sum(array_column($data, 'Ia')),
            'Ib' => array_sum(array_column($data, 'Ib')),
            'Ic' => array_sum(array_column($data, 'Ic')),
            'Id' => array_sum(array_column($data, 'Id')),
            'IIa' => array_sum(array_column($data, 'IIa')),
            'IIb' => array_sum(array_column($data, 'IIb')),
            'IIc' => array_sum(array_column($data, 'IIc')),
            'IId' => array_sum(array_column($data, 'IId')),
            'IIIa' => array_sum(array_column($data, 'IIIa')),
            'IIIb' => array_sum(array_column($data, 'IIIb')),
            'IIIc' => array_sum(array_column($data, 'IIIc')),
            'IIId' => array_sum(array_column($data, 'IIId')),
            'IVa' => array_sum(array_column($data, 'IVa')),
            'IVb' => array_sum(array_column($data, 'IVb')),
            'IVc' => array_sum(array_column($data, 'IVc')),
            'IVd' => array_sum(array_column($data, 'IVd')),
            'IVe' => array_sum(array_column($data, 'IVe')),
        ];
        return $sum;
    }

    public static function sumPendidikanRow($data)
    {
        $tkpendid = self::handleTkPendid();
        $sum = [];

        foreach ($tkpendid as $pend) {
            $sum[$pend['tkpendid']] = array_sum(array_column($data->toArray(), $pend['tkpendid']));
        }

        return $sum;
    }

    public static function sumJenisKelaminRow($data)
    {
        $data = $data->toArray();
        $sum = [
            'laki' => array_sum(array_column($data, 'laki')),
            'perempuan' => array_sum(array_column($data, 'perempuan')),
            'total' => array_sum(array_column($data, 'total')),
        ];
        return $sum;
    }
}