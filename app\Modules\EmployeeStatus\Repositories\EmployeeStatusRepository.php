<?php

namespace App\Modules\EmployeeStatus\Repositories;

use App\Modules\EmployeeStatus\Models\EmployeeStatus;

class EmployeeStatusRepository
{
    public static function datatable($per_page = 15)
    {
        $data = EmployeeStatus::paginate($per_page);
        return $data;
    }
    public static function get($employee_status_id)
    {
        $employee_status = EmployeeStatus::where('id', $employee_status_id)->first();
        return $employee_status;
    }
    public static function create($employee_status)
    {
        $employee_status = EmployeeStatus::create($employee_status);
        return $employee_status;
    }

    public static function update($employee_status_id, $employee_status)
    {
        EmployeeStatus::where('id', $employee_status_id)->update($employee_status);
        $employee_status = EmployeeStatus::where('id', $employee_status_id)->first();
        return $employee_status;
    }

    public static function delete($employee_status_id)
    {
        $delete = EmployeeStatus::where('id', $employee_status_id)->delete();
        return $delete;
    }
}
