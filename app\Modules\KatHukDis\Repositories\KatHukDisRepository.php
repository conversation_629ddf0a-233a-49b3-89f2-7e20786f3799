<?php

namespace App\Modules\KatHukDis\Repositories;

use App\Modules\KatHukDis\Models\KatHukDisModel;

class KatHukDisRepository
{
    public static function datatable($per_page = 15)
    {
        $data =  KatHukDisModel::paginate($per_page);
        return $data;
    }
    public static function get($jeniskp_id)
    {
        $jeniskp = KatHukDisModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }
    public static function create($jeniskp)
    {
        $jeniskp = KatHukDisModel::create($jeniskp);
        return $jeniskp;
    }

    public static function update($jeniskp_id, $jeniskp)
    {
        KatHukDisModel::where('id', $jeniskp_id)->update($jeniskp);
        $jeniskp = KatHukDisModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }

    public static function delete($jeniskp_id)
    {
        $delete = KatHukDisModel::where('id', $jeniskp_id)->delete();
        return $delete;
    }

    public static function getKatHukDisFromApi($katHukDisID)
    {
        switch ($katHukDisID) {
            case 'R':
                $id = 1;
                break;
            case 'S':
                $id = 2;
                break;
            case 'B':
                $id = 3;
                break;
            default:
                $id = null;
        }
        
        if (!empty($id)) {
            $katHukDis = KatHukDisModel::find($id);
            return @$katHukDis;
        }
    }
}
