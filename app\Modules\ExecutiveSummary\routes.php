<?php

namespace App\Modules\ExecutiveSummary;

use App\Modules\ExecutiveSummary\Controllers\ExecutiveSummaryController;
use Illuminate\Support\Facades\Route;

use App\Modules\ExecutiveSummary\Controllers\PrediksiPensiunController;
use App\Modules\ExecutiveSummary\Controllers\StatistikASNController;
use App\Modules\ExecutiveSummary\Controllers\NominatifPegawaiController;
use App\Modules\ExecutiveSummary\Controllers\PrediksiKenaikanPangkatController;
use App\Modules\ExecutiveSummary\Controllers\PrediksiKGBController;
use App\Modules\ExecutiveSummary\Controllers\PrediksiUlangTahunController;
use App\Modules\ExecutiveSummary\Controllers\JabatanKosongController;
use App\Modules\ExecutiveSummary\Controllers\ValiditasDataASNController;
use App\Modules\ExecutiveSummary\Controllers\PersentaseASNController;
// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/executive-summary')->group(function () {
    Route::prefix('/prediksi-pensiun')->group(function () {
        Route::get('/datatable', [PrediksiPensiunController::class, 'datatable']);
        Route::get('/cetak-pdf', [PrediksiPensiunController::class, 'cetakPdf']);
        Route::get('/export-excel', [PrediksiPensiunController::class, 'exportExcel']);
    });

    Route::prefix('/statistik-asn')->group(function () {
        Route::get('/datatablePendidikanFormal', [StatistikASNController::class, 'datatablePendidikanFormal']);
        Route::get('/grafikPendidikanFormalGolonganI', [StatistikASNController::class, 'grafikPendidikanFormalGolonganI']);
        Route::get('/grafikPendidikanFormalGolonganII', [StatistikASNController::class, 'grafikPendidikanFormalGolonganII']);
        Route::get('/grafikPendidikanFormalGolonganIII', [StatistikASNController::class, 'grafikPendidikanFormalGolonganIII']);
        Route::get('/grafikPendidikanFormalGolonganIV', [StatistikASNController::class, 'grafikPendidikanFormalGolonganIV']);
        Route::get('/pdf-pegawai-pendidikan-golongan', [StatistikASNController::class, 'pdfPendidikanFormal']);

        Route::get('/datatableUnitKerjaPendidikanFormal', [StatistikASNController::class, 'datatableUnitKerjaPendidikanFormal']);
        Route::get('/grafikUnitKerjaPendidikanFormal', [StatistikASNController::class, 'grafikUnitKerjaPendidikanFormal']);
        Route::get('/pdf-pegawai-unit-kerja-pendidikan-formal', [StatistikAsnController::class, 'pdfUnitKerjaPendidikanFormal']);

        Route::get('/datatableUnitKerjaGolongan', [StatistikASNController::class, 'datatableUnitKerjaGolongan']);
        Route::get('/grafikUnitKerjaGolonganI', [StatistikASNController::class, 'grafikUnitKerjaGolonganI']);
        Route::get('/grafikUnitKerjaGolonganII', [StatistikASNController::class, 'grafikUnitKerjaGolonganII']);
        Route::get('/grafikUnitKerjaGolonganIII', [StatistikASNController::class, 'grafikUnitKerjaGolonganIII']);
        Route::get('/grafikUnitKerjaGolonganIV', [StatistikASNController::class, 'grafikUnitKerjaGolonganIV']);
        Route::get('/pdf-pegawai-unit-kerja-golongan', [StatistikAsnController::class, 'pdfUnitKerjaGolongan']);

        Route::get('/datatableDiklat', [StatistikASNController::class, 'datatableDiklat']);
        Route::get('/grafikDiklatEselonI', [StatistikASNController::class, 'grafikDiklatEselonI']);
        Route::get('/grafikDiklatEselonII', [StatistikASNController::class, 'grafikDiklatEselonII']);
        Route::get('/grafikDiklatEselonIII', [StatistikASNController::class, 'grafikDiklatEselonIII']);
        Route::get('/grafikDiklatEselonIV', [StatistikASNController::class, 'grafikDiklatEselonIV']);
        Route::get('/grafikDiklatEselonV', [StatistikASNController::class, 'grafikDiklatEselonV']);
        Route::get('/grafikDiklatGolonganI', [StatistikASNController::class, 'grafikDiklatGolonganI']);
        Route::get('/grafikDiklatGolonganII', [StatistikASNController::class, 'grafikDiklatGolonganII']);
        Route::get('/grafikDiklatGolonganIII', [StatistikASNController::class, 'grafikDiklatGolonganIII']);
        Route::get('/grafikDiklatGolonganIV', [StatistikASNController::class, 'grafikDiklatGolonganIV']);

        Route::get('/datatableEselonGolongan', [StatistikASNController::class, 'datatableEselonGolongan']);
        Route::get('/grafikEselonGolonganI', [StatistikASNController::class, 'grafikEselonGolonganI']);
        Route::get('/grafikEselonGolonganII', [StatistikASNController::class, 'grafikEselonGolonganII']);
        Route::get('/grafikEselonGolonganIII', [StatistikASNController::class, 'grafikEselonGolonganIII']);
        Route::get('/grafikEselonGolonganIV', [StatistikASNController::class, 'grafikEselonGolonganIV']);
        Route::get('/pdf-pegawai-eselon-golongan', [StatistikAsnController::class, 'pdfEselonGolongan']);

        Route::get('/datatableJenisKelaminEselon', [StatistikASNController::class, 'datatableJenisKelaminEselon']);
        Route::get('/grafikJenisKelaminEselonI', [StatistikASNController::class, 'grafikJenisKelaminEselonI']);
        Route::get('/grafikJenisKelaminEselonII', [StatistikASNController::class, 'grafikJenisKelaminEselonII']);
        Route::get('/grafikJenisKelaminEselonIII', [StatistikASNController::class, 'grafikJenisKelaminEselonIII']);
        Route::get('/grafikJenisKelaminEselonIV', [StatistikASNController::class, 'grafikJenisKelaminEselonIV']);
        Route::get('/grafikJenisKelaminEselonV', [StatistikASNController::class, 'grafikJenisKelaminEselonV']);
        Route::get('/pdf-pegawai-jenis-kelamin-eselon', [StatistikAsnController::class, 'pdfJenisKelaminEselon']);

        Route::get('/datatableJenisKelaminGolongan', [StatistikASNController::class, 'datatableJenisKelaminGolongan']);
        Route::get('/grafikJenisKelaminGolonganI', [StatistikASNController::class, 'grafikJenisKelaminGolonganI']);
        Route::get('/grafikJenisKelaminGolonganII', [StatistikASNController::class, 'grafikJenisKelaminGolonganII']);
        Route::get('/grafikJenisKelaminGolonganIII', [StatistikASNController::class, 'grafikJenisKelaminGolonganIII']);
        Route::get('/grafikJenisKelaminGolonganIV', [StatistikASNController::class, 'grafikJenisKelaminGolonganIV']);
        Route::get('/pdf-pegawai-jenis-kelamin-golongan', [StatistikAsnController::class, 'pdfJenisKelaminGolongan']);

        Route::get('/datatableJenisJabatanGolongan', [StatistikASNController::class, 'datatableJenisJabatanGolongan']);
        Route::get('/grafikJenisJabatanGolonganI', [StatistikASNController::class, 'grafikJenisJabatanGolonganI']);
        Route::get('/grafikJenisJabatanGolonganII', [StatistikASNController::class, 'grafikJenisJabatanGolonganII']);
        Route::get('/grafikJenisJabatanGolonganIII', [StatistikASNController::class, 'grafikJenisJabatanGolonganIII']);
        Route::get('/grafikJenisJabatanGolonganIV', [StatistikASNController::class, 'grafikJenisJabatanGolonganIV']);
        Route::get('/pdf-pegawai-jenis-jabatan-golongan', [StatistikAsnController::class, 'pdfJenisJabatanGolongan']);

        Route::get('/datatableKedudukanPegawai', [StatistikASNController::class, 'datatableKedudukanPegawai']);
        Route::get('/grafikKedudukanPegawaiGolonganI', [StatistikASNController::class, 'grafikKedudukanPegawaiGolonganI']);
        Route::get('/grafikKedudukanPegawaiGolonganII', [StatistikASNController::class, 'grafikKedudukanPegawaiGolonganII']);
        Route::get('/grafikKedudukanPegawaiGolonganIII', [StatistikASNController::class, 'grafikKedudukanPegawaiGolonganIII']);
        Route::get('/grafikKedudukanPegawaiGolonganIV', [StatistikASNController::class, 'grafikKedudukanPegawaiGolonganIV']);
        Route::get('/pdf-pegawai-kedudukan-pegawai', [StatistikAsnController::class, 'pdfKedudukanPegawai']);

        Route::get('/datatableAgamaGolongan', [StatistikASNController::class, 'datatableAgamaGolongan']);
        Route::get('/grafikAgamaGolonganI', [StatistikASNController::class, 'grafikAgamaGolonganI']);
        Route::get('/grafikAgamaGolonganII', [StatistikASNController::class, 'grafikAgamaGolonganII']);
        Route::get('/grafikAgamaGolonganIII', [StatistikASNController::class, 'grafikAgamaGolonganIII']);
        Route::get('/grafikAgamaGolonganIV', [StatistikASNController::class, 'grafikAgamaGolonganIV']);
        Route::get('/pdf-pegawai-agama-golongan', [StatistikASNController::class, 'pdfAgamaGolongan']);


        Route::get('/datatableUsiaGolongan', [StatistikASNController::class, 'datatableUsiaGolongan']);
        Route::get('/grafikUsiaGolongan', [StatistikASNController::class, 'grafikUsiaGolongan']);
        Route::get('/pdf-pegawai-usia-golongan', [StatistikASNController::class, 'pdfUsiaGolongan']);

        Route::get('/datatableStatusPerkawinan', [StatistikASNController::class, 'datatableStatusPerkawinan']);
        Route::get('/grafikStatusPerkawinanGolonganI', [StatistikASNController::class, 'grafikStatusPerkawinanGolonganI']);
        Route::get('/grafikStatusPerkawinanGolonganII', [StatistikASNController::class, 'grafikStatusPerkawinanGolonganII']);
        Route::get('/grafikStatusPerkawinanGolonganIII', [StatistikASNController::class, 'grafikStatusPerkawinanGolonganIII']);
        Route::get('/grafikStatusPerkawinanGolonganIV', [StatistikASNController::class, 'grafikStatusPerkawinanGolonganIV']);
        Route::get('/pdf-pegawai-status-perkawinan', [StatistikASNController::class, 'pdfStatusPerkawinan']);

        Route::get('/datatableGuruGolongan', [StatistikASNController::class, 'datatableGuruGolongan']);
        Route::get('/grafikGuruGolonganI', [StatistikASNController::class, 'grafikGuruGolonganI']);
        Route::get('/grafikGuruGolonganII', [StatistikASNController::class, 'grafikGuruGolonganII']);
        Route::get('/grafikGuruGolonganIII', [StatistikASNController::class, 'grafikGuruGolonganIII']);
        Route::get('/grafikGuruGolonganIV', [StatistikASNController::class, 'grafikGuruGolonganIV']);
        Route::get('/pdf-pegawai-guru-golongan', [StatistikASNController::class, 'pdfGuruGolongan']);

        Route::get('/datatableGuruUnitKerja', [StatistikASNController::class, 'datatableGuruUnitKerja']);
        Route::get('/grafikGuruUnitKerjaI', [StatistikASNController::class, 'grafikGuruUnitKerjaI']);
        Route::get('/grafikGuruUnitKerjaII', [StatistikASNController::class, 'grafikGuruUnitKerjaII']);
        Route::get('/grafikGuruUnitKerjaIIII', [StatistikASNController::class, 'grafikGuruUnitKerjaIII']);
        Route::get('/grafikGuruUnitKerjaIV', [StatistikASNController::class, 'grafikGuruUnitKerjaIV']);
        Route::get('/grafikGuruUnitKerja', [StatistikASNController::class, 'grafikGuruUnitKerja']);
        Route::get('/pdf-pegawai-guru-unit-kerja', [StatistikASNController::class, 'pdfGuruUnitKerja']);

        Route::get('/datatablePelaksanaGolongan', [StatistikASNController::class, 'datatablePelaksanaGolongan']);
        Route::get('/grafikPelaksanaGolonganI', [StatistikASNController::class, 'grafikPelaksanaGolonganI']);
        Route::get('/grafikPelaksanaGolonganII', [StatistikASNController::class, 'grafikPelaksanaGolonganII']);
        Route::get('/grafikPelaksanaGolonganIII', [StatistikASNController::class, 'grafikPelaksanaGolonganIII']);
        Route::get('/grafikPelaksanaGolonganIV', [StatistikASNController::class, 'grafikPelaksanaGolonganIV']);
        Route::get('/pdf-pegawai-pelaksana-golongan', [StatistikASNController::class, 'pdfPelaksanaGolongan'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableKesehatanGolongan', [StatistikASNController::class, 'datatableKesehatanGolongan']);
        Route::get('/grafikKesehatanGolonganI', [StatistikASNController::class, 'grafikKesehatanGolonganI']);
        Route::get('/grafikKesehatanGolonganII', [StatistikASNController::class, 'grafikKesehatanGolonganII']);
        Route::get('/grafikKesehatanGolonganIII', [StatistikASNController::class, 'grafikKesehatanGolonganIII']);
        Route::get('/grafikKesehatanGolonganIV', [StatistikASNController::class, 'grafikKesehatanGolonganIV']);
        Route::get('/pdf-pegawai-kesehatan-golongan', [StatistikASNController::class, 'pdfKesehatanGolongan'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableTeknisGolongan', [StatistikASNController::class, 'datatableTeknisGolongan']);
        Route::get('/grafikTeknisGolonganI', [StatistikASNController::class, 'grafikTeknisGolonganI']);
        Route::get('/grafikTeknisGolonganII', [StatistikASNController::class, 'grafikTeknisGolonganII']);
        Route::get('/grafikTeknisGolonganIII', [StatistikASNController::class, 'grafikTeknisGolonganIII']);
        Route::get('/grafikTeknisGolonganIV', [StatistikASNController::class, 'grafikTeknisGolonganIV']);
        Route::get('/pdf-pegawai-teknis-golongan', [StatistikASNController::class, 'pdfTeknisGolongan'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableStatusPegawaiKelamin', [StatistikASNController::class, 'datatableStatusPegawaiKelamin']);
        Route::get('/grafikStatusPegawaiKelamin', [StatistikASNController::class, 'grafikStatusPegawaiKelamin']);
        Route::get('/pdf-pegawai-jenkepeg-kelamin', [StatistikASNController::class, 'pdfJenisPegawaiKelamin'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableStatusPegawaiPendidikan', [StatistikASNController::class, 'datatableStatusPegawaiPendidikan']);
        Route::get('/grafikStatusPegawaiPendidikan', [StatistikASNController::class, 'grafikStatusPegawaiPendidikan']);
        Route::get('/pdf-pegawai-pendidikan-kelamin', [StatistikASNController::class, 'pdfPendidikanKelamin'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableUnitKerjaKelamin', [StatistikASNController::class, 'datatableUnitKerjaKelamin']);
        Route::get('/grafikUnitKerjaKelamin', [StatistikASNController::class, 'grafikUnitKerjaKelamin']);

        Route::get('/datatableMasaKerjaKelamin', [StatistikASNController::class, 'datatableMasaKerjaKelamin']);
        Route::get('/grafikMasaKerjaKelamin', [StatistikASNController::class, 'grafikMasaKerjaKelamin']);
        Route::get('/pdf-pegawai-masa-kerja-kelamin', [StatistikASNController::class, 'pdfMasaKerjaKelamin'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableJabfungKelamin', [StatistikASNController::class, 'datatableJabfungKelamin']);
        Route::get('/grafikJabfungKelamin', [StatistikASNController::class, 'grafikJabfungKelamin']);
        Route::get('/pdf-pegawai-jabfung-kelamin', [StatistikASNController::class, 'pdfJabfungKelamin'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableUsiaKelamin', [StatistikASNController::class, 'datatableUsiaKelamin']);
        Route::get('/grafikUsiaKelamin', [StatistikASNController::class, 'grafikUsiaKelamin']);
        Route::get('/pdf-pegawai-usia-kelamin', [StatistikASNController::class, 'pdfUsiaKelamin'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatablePensiunJabatanPertahun', [StatistikASNController::class, 'datatablePensiunJabatanPertahun']);
        Route::get('/grafikPensiunJabatanPertahun', [StatistikASNController::class, 'grafikPensiunJabatanPertahun']);
        Route::get('/pdf-pegawai-pensiun-pertahun', [StatistikASNController::class, 'pdfPensiunJabatanPertahun'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatablePensiunJabatanPerbulan', [StatistikASNController::class, 'datatablePensiunJabatanPerbulan']);
        Route::get('/grafikPensiunJabatanPerbulan', [StatistikASNController::class, 'grafikPensiunJabatanPerbulan']);
        Route::get('/pdf-pegawai-pensiun-perbulan', [StatistikASNController::class, 'pdfPensiunJabatanPerbulan'])->withoutMiddleware(['auth', 'jwt.auth']);

        Route::get('/datatableP3kJabatan', [StatistikASNController::class, 'datatableP3kJabatan']);
        Route::get('/grafikP3kJabatan', [StatistikASNController::class, 'grafikP3kJabatan']);

        Route::get('/datatableP3kTmtMasuk', [StatistikASNController::class, 'datatableP3kTmtMasuk']);
        Route::get('/grafikP3kTmtMasuk', [StatistikASNController::class, 'grafikP3kTmtMasuk']);

        Route::get('/datatablePnsGol', [StatistikASNController::class, 'datatablePnsGol']);
        Route::get('/grafikPnsGol', [StatistikASNController::class, 'grafikPnsGol']);

        Route::get('/datatableJabManajerial', [StatistikASNController::class, 'datatableJabManajerial']);
        Route::get('/grafikJabManajerial', [StatistikASNController::class, 'grafikJabManajerial']);

        Route::get('/datatableJenisKelaminPendidikan', [StatistikASNController::class, 'datatableJenisKelaminPendidikan']);
        Route::get('/grafikJenisKelaminPendidikan', [StatistikASNController::class, 'grafikJenisKelaminPendidikan']);

        Route::get('/datatableManajerialKelamin', [StatistikASNController::class, 'datatableManajerialKelamin']);
        Route::get('/grafikManajerialKelamin', [StatistikASNController::class, 'grafikManajerialKelamin']);

        Route::get('/datatableManajerialEselonKelamin', [StatistikASNController::class, 'datatableManajerialEselonKelamin']);
        Route::get('/grafikManajerialEselonKelamin', [StatistikASNController::class, 'grafikManajerialEselonKelamin']);

        Route::get('/datatableManajerialEselonPendidikan', [StatistikASNController::class, 'datatableManajerialEselonPendidikan']);
        Route::get('/grafikManajerialEselonPendidikan', [StatistikASNController::class, 'grafikManajerialEselonPendidikan']);

        Route::get('/datatableFormasiManajerial', [StatistikASNController::class, 'datatableFormasiManajerial']);
        Route::get('/grafikFormasiManajerial', [StatistikASNController::class, 'grafikFormasiManajerial']);

        Route::get('/print-statistik', [StatistikASNController::class, 'handlePrintStatistik']);
    });

    Route::prefix('/nominatif-pegawai')->group(function () {
        Route::get('/datatable', [NominatifPegawaiController::class, 'datatable']);
    });
    Route::prefix('/prediksi-kenaikan-pangkat')->group(function () {
        Route::get('/datatable', [PrediksiKenaikanPangkatController::class, 'datatable']);
    });
    Route::prefix('/prediksi-kgb')->group(function () {
        Route::get('/datatable', [PrediksiKGBController::class, 'datatable']);
    });
    Route::prefix('/prediksi-ulang-tahun')->group(function () {
        Route::get('/datatable', [PrediksiUlangTahunController::class, 'datatable']);
    });
    Route::prefix('/jabatan-kosong')->group(function () {
        Route::get('/datatable', [JabatanKosongController::class, 'datatable']);
    });
    Route::prefix('/validitas-data-asn')->group(function() {
        Route::get('/', [ValiditasDataASNController::class, 'index']);
        Route::get('/skpd', [ValiditasDataASNController::class, 'skpdCompletionAndValidity']);
        Route::get('/{employeeId}', [ValiditasDataASNController::class, 'employeeCompletionAndValidity']);
    });
    Route::prefix('/persentase-asn')->group(function() {
        Route::get('/', [PersentaseASNController::class, 'datatable_hukdis']);
        Route::get('/datatable', [PersentaseASNController::class, 'datatable_hukdis']);
    });
    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [ExecutiveSummaryController::class, 'index'])->middleware('authorize:read-executive_summary');
    Route::get('/datatable', [ExecutiveSummaryController::class, 'datatable'])->middleware('authorize:read-executive_summary');
    Route::get('/create', [ExecutiveSummaryController::class, 'create'])->middleware('authorize:create-executive_summary');
    Route::post('/', [ExecutiveSummaryController::class, 'store'])->middleware('authorize:create-executive_summary');
    Route::get('/{executive_summary_id}', [ExecutiveSummaryController::class, 'show'])->middleware('authorize:read-executive_summary');
    Route::get('/{executive_summary_id}/edit', [ExecutiveSummaryController::class, 'edit'])->middleware('authorize:update-executive_summary');
    Route::put('/{executive_summary_id}', [ExecutiveSummaryController::class, 'update'])->middleware('authorize:update-executive_summary');
    Route::delete('/{executive_summary_id}', [ExecutiveSummaryController::class, 'destroy'])->middleware('authorize:delete-executive_summary');
});
