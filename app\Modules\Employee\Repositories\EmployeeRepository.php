<?php

namespace App\Modules\Employee\Repositories;

use App\Exceptions\AppException;
use Illuminate\Queue\MaxAttemptsExceededException;
use Exception;

use App\Mail\UserRegisterMail;
use App\Modules\Employee\Interfaces\IEmployeeRepository;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Employee\Model\EmployeePeriodicDataModel;
use App\Modules\JabFung\Models\JabFung;
use App\Modules\JabFungUm\Models\JabFungUm;
use App\Modules\JenJurusan\Models\JenJurusan;
use App\Modules\Kepangkatan\Models\Kepangkatan;
use App\Modules\PerubahanData\Models\PerubahanData;
use App\Modules\RiwayatCpns\Models\RiwayatCpnsModel;
use App\Modules\RiwayatJabatan\Models\RiwayatJabatanModel;
use App\Modules\RiwayatKepangkatan\Models\RiwayatKepangkatanModel;
use App\Modules\RiwayatPendidikan\Models\RiwayatPendidikanModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiModel;
use App\Modules\RiwayatPppk\Models\RiwayatPppkModel;
use App\Modules\EmployeeStatus\Models\EmployeeStatus;
use App\Modules\Role\Model\RoleModel;
use App\Modules\TkPendid\Models\TkPendid;
use App\Modules\UnitKerja\Models\UnitKerjaModel;
use App\Modules\User\Model\UserModel;
use App\Modules\User\Model\UserRoleModel;
use App\Type\JsonResponseType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class EmployeeRepository implements IEmployeeRepository
{
    public static function datatable($options)
    {
        $employees = !isset($options['sortBy']) ? EmployeeModel::paginate($options['per_page']) : EmployeeModel::orderBy($options['sortBy']['column'], $options['sortBy']['type'])->paginate($options['per_page']);
        return $employees;
    }
    public static function get($employee_id)
    {
        $employee = EmployeeModel::where('id', $employee_id)->first();
        return $employee;
    }
    public static function create($payload)
    {
        $requiredColumns = EmployeeModel::REQUIRED_IMPORT_COLUMNS;

        foreach ($requiredColumns as $column) {
            if(empty($payload[$column])) {
                throw new AppException("Field $column kosong");
            }
        }

        $exlodedIdSkpd = explode(".", $payload['id_sub_sub_unit']);

        if (count($exlodedIdSkpd) != 5) {
            throw new AppException('Format ID Sub Sub Unit Tidak Valid');
        }

        $id_unit_kerja = $exlodedIdSkpd[0];
        $id_induk_upt = $exlodedIdSkpd[1];
        $id_sub_unit = $exlodedIdSkpd[2];
        $id_sub_sub_unit = $exlodedIdSkpd[3];
        $id_sub_sub_sub_unit = $exlodedIdSkpd[4];

        DB::beginTransaction();
        try {
            $allowedEmployeeStatus = EmployeeStatus::pluck('id')->toArray();
            if (empty($payload['status_pegawai']) || !in_array($payload['status_pegawai'], $allowedEmployeeStatus)) {
                throw new AppException('Status Pegawai tidak ditemukan');
            }

            $existingEmployee = EmployeeModel::where('nip', $payload['nip'])->first();
            if (!empty($existingEmployee)) {
                throw new AppException('Duplicate NIP dengan pegawai a/n ' . strtoupper($existingEmployee->nama));
            }

            do {
                $employeeId = Str::uuid();
            } 
            while (EmployeeModel::where('id', $employeeId)->exists());

            // Create Employee
            $employee = EmployeeModel::create([
                'id' => $employeeId,
                'nip' => $payload['nip'],
                'nip_lama' => $payload['nip'],
                'nama' => $payload['nama'],
                'nama_ijazah' => $payload['nama_ijazah'] ?? $payload['nama'],
                'email' => $payload['email'],
                'email_instansi' => $payload['nip'] . '@asn.jatengprov.go.id',
                'id_unit_kerja' => $id_unit_kerja,
                'id_induk_upt' => $id_induk_upt,
                'id_sub_unit' => $id_sub_unit,
                'id_sub_sub_unit' => $id_sub_sub_unit,
                'id_sub_sub_sub_unit' => $id_sub_sub_sub_unit,
                'gelar_depan' => !empty($payload['gelar_depan']) ? $payload['gelar_depan'] : '',
                'gelar_belakang' => !empty($payload['gelar_belakang']) ? $payload['gelar_belakang'] : '',
                'jenis_kelamin' => $payload['jenis_kelamin'] ?? null,
                'tempat_lahir' => $payload['tempat_lahir'] ?? null,
                'tanggal_lahir' => $payload['tanggal_lahir'] ?? null,
                'status_kedudukan' => $payload['status_hidup'] ?? null,
                'status_pegawai' => $payload['status_pegawai'],
                'jenis_pegawai' => $payload['jenis_pegawai'] ?? null,
                'kedudukan_pegawai' => $payload['kedudukan_pegawai'] ?? null,
                'is_pemberkasan' => true
            ]);

            // Create Pangkat
            if (!empty($payload['golru_p3k'])) {
                $kepangkatan = Kepangkatan::where('golru_p3k', $payload['golru_p3k'])->first();
                if (empty($kepangkatan)) {
                    throw new AppException("Data Golongan PPPK tidak ditemukan");
                }
                RiwayatPppkModel::create([
                    'id_pegawai' => $employeeId,
                    'nip' => $employee->nip,
                    'id_golongan_ruang' => $kepangkatan->id,
                    'masa_kerja_tahun' => str_pad($payload['masa_kerja_tahun'], 2, "0", STR_PAD_LEFT),
                    'masa_kerja_bulan' => str_pad($payload['masa_kerja_bulan'], 2, "0", STR_PAD_LEFT),
                    'nomor_sk_pppk' => $payload['nomor_sk_golongan'],
                    'tmt_sk_pppk' => $payload['tmt_golongan'],
                    'tanggal_sk_pppk' => $payload['tanggal_sk'],
                    'tmt_kontrak_awal' => $payload['tmt_kontrak_awal'],
                    'tmt_kontrak_akhir' => $payload['tmt_kontrak_akhir'],
                    'spmt_tmt' => $payload['spmt_tmt'],
                    'spmt_tanggal' => $payload['spmt_tanggal'],
                    'isakhir' => ($payload['status_pegawai'] != 2) ? 1 : 0,
                ]);
            }

            // Create CPNS
            if (!empty($payload['golru_cpns'])) {
                $kepangkatan = Kepangkatan::where('id', $payload['golru_cpns'])->first();
                if (empty($kepangkatan)) {
                    throw new AppException("Data Golongan CPNS tidak ditemukan");
                }
                RiwayatCpnsModel::create([
                    'id_pegawai' => $employeeId,
                    'nip' => $employee->nip,
                    'id_golongan_ruang' => $kepangkatan->id,
                    'masa_kerja_tahun' => str_pad($payload['masa_kerja_tahun'], 2, "0", STR_PAD_LEFT),
                    'masa_kerja_bulan' => str_pad($payload['masa_kerja_bulan'], 2, "0", STR_PAD_LEFT),
                    'nomor_sk_cpns' => $payload['nomor_sk_golongan'],
                    'tanggal_sk_cpns' => $payload['tanggal_sk'],
                    'tmt_cpns' => $payload['tmt_golongan'],
                    'tmt_spmt' => $payload['spmt_tmt'],
                    'tanggal_spmt' => $payload['spmt_tanggal'],
                    'id_penetap' => in_array($payload['status_pegawai'], [1, 3]) ? "D" : ''
                ]);
            }

            if (!empty($payload['golru_cpns']) || !empty($payload['golru_p3k'])) {
                RiwayatKepangkatanModel::create([
                    'id_pegawai' => $employeeId,
                    'nip' => $employee->nip,
                    'id_kepangkatan' => $kepangkatan->id,
                    'masa_kerja_tahun' => str_pad($payload['masa_kerja_tahun'], 2, "0", STR_PAD_LEFT),
                    'masa_kerja_bulan' => str_pad($payload['masa_kerja_bulan'], 2, "0", STR_PAD_LEFT),
                    'nomor_sk' => $payload['nomor_sk_golongan'],
                    'tanggal_sk' => $payload['tanggal_sk'],
                    'tmt_sk' => $payload['tmt_golongan'],
                    'isakhir' => in_array($payload['status_pegawai'], [1, 3, 4]) ? 1 : 0,
                    'id_penetap' => in_array($payload['status_pegawai'], [1, 3]) ? "D" : null,
                    'status_tunjangan' => ('status_pegawai' == 2) ? 2 : 1
                ]);
            }


            // Create Jabatan
            if (!empty($payload['id_jenis_jabatan']) || !empty($payload['id_jabatan'])) {
                $jabatanPayload = [
                    'id_pegawai' => $employeeId,
                    'nip' => $employee->nip,
                    'id_jenis_jabatan' => $payload['id_jenis_jabatan'],
                    'nomor_sk' => $payload['nomor_sk_jabatan'],
                    'tanggal_sk' => $payload['tanggal_sk'],
                    'tmt_jabatan' => $payload['tmt_jabatan'],
                    'id_unit_kerja' => $id_unit_kerja,
                    'id_induk_upt' => $id_induk_upt,
                    'id_sub_unit' => $id_sub_unit,
                    'id_sub_sub_unit' => $id_sub_sub_unit,
                    'id_sub_sub_sub_unit' => $id_sub_sub_sub_unit,
                    'isakhir' => 1,
                    'is_lokasi_akhir' => 1,
                    'jenis_mutasi' => '1',
                    'id_instansi_induk' => '1116',
                    'id_penetap' => in_array($payload['status_pegawai'], [1, 3]) ? "D" : ''
                ];
                if ($payload['id_jenis_jabatan'] == '1') {
                    $skpd = UnitKerjaModel::where('id_unit_kerja', $id_unit_kerja)
                        ->where('id_induk_upt', $id_induk_upt)
                        ->where('id_sub_unit', $id_sub_unit)
                        ->where('id_sub_sub_unit', $id_sub_sub_unit)
                        ->where('id_sub_sub_sub_unit', $id_sub_sub_sub_unit)
                        ->first();
                    if (empty($skpd)) {
                        throw new AppException("Skpd Jabatan tidak ditemukan");
                    }
                    $jabatanPayload['id_jabatan'] =  $payload['id_sub_sub_unit'];
                    $jabatanPayload['jabatan'] =  $skpd->jab;
                } else if ($payload['id_jenis_jabatan'] == '2') {
                    $jabatan = JabFung::where('idjabfung', $payload['id_jabatan'])->first();
                    if (empty($jabatan)) {
                        throw new AppException("Jabatan Fungsional tidak ditemukan");
                    }
                    $jabatanPayload['id_jabatan'] = $jabatan->idjabfung;
                    $jabatanPayload['jabatan'] =  $jabatan->jabfung;
                } else if ($payload['id_jenis_jabatan'] == '3') {
                    $jabatan = JabFungUm::where('idjabfungum', $payload['id_jabatan'])->first();
                    if (empty($jabatan)) {
                        throw new AppException("Jabatan Pelaksana tidak ditemukan");
                    }
                    $jabatanPayload['id_jabatan'] = $jabatan->idjabfungum;
                    $jabatanPayload['jabatan'] =  $jabatan->jabfungum;
                } else {
                    throw new AppException("Id Jenis Jabatan tidak valid => " .  $payload['id_jenis_jabatan']);
                }
                RiwayatJabatanModel::create($jabatanPayload);
            }


            // Create pendidikan
            if (!empty($payload['id_jenjang_pendidikan']) && !empty($payload['id_jurusan'])) {
                $tingkatPendidikan = TkPendid::where('idtkpendid', $payload['id_jenjang_pendidikan'])->first();
                if (empty($tingkatPendidikan)) {
                    throw new AppException('Tingkat Pendidikan tidak ditemukan => ' . $payload['id_jenjang_pendidikan']);
                }

                $jurusan = JenJurusan::where('idjenjurusan', $payload['id_jurusan'])->first();
                if (empty($jurusan)) {
                    throw new AppException('Jurusan Pendidikan tidak ditemukan => ' . $payload['id_jurusan']);
                }

                RiwayatPendidikanModel::create([
                    'id_pegawai' => $employeeId,
                    'nip' => $employee->nip,
                    'id_jenjang' => $tingkatPendidikan->idtkpendid,
                    'jenjang' => $tingkatPendidikan->tkpendid,
                    'id_jurusan' => $payload['id_jurusan'],
                    'jurusan' => $jurusan->jenjurusan,
                    'tahun_lulus' => $payload['tahun_lulus'],
                    'isakhir' => 1,
                    'isawal' => in_array($payload['status_pegawai'], [1, 3]) ? 1 : 0
                ]);
            }

            // Create User
            $defaultPassword = ucfirst(strtolower(explode(" ", $payload['nama'])[0])) . "*" . implode("", explode("-", $payload['tanggal_lahir'])) . "*" . "!";
            $user = UserModel::create([
                'name' => $payload['nama'],
                'email' => $payload['nip'] . "@asn.jatengprov.go.id",
                'username' => $payload['nip'],
                'id_pegawai' => $employeeId,
                'default_password' => $defaultPassword
            ]);

            // Add Role
            UserRoleModel::create([
                'user_id' => $user->id,
                'role_id' =>  RoleModel::ROLE_ID_PEGAWAI
            ]);

            // Create email instansi
            if (!empty($payload['tanggal_sk'])) {
                self::reqEmailInstansi($employee, $defaultPassword, $payload['tanggal_sk'], $payload['nip']);
            }

            // Create FPID
            $fpid = self::createFpid($payload['nip'], $payload['nama']);
            EmployeeModel::where('id', $employeeId)->update(['fpid' => $fpid['fpid']]);

            // Create nominasis
            self::createNominate($employee);

            // Create usernew
            self::createReguserNew($employee, $defaultPassword);

            // Create token mobile
            self::regTokenMobile($employee);

            Mail::to([$payload['nip'] . "@asn.jatengprov.go.id", $payload['email']])->send(new UserRegisterMail($user));

            DB::commit();
            return $employee;
        } catch (AppException $err) {
            DB::rollBack();
            throw $err;
        } catch (Exception $err) {
            DB::rollBack();
            throw $err;
        } catch (MaxAttemptsExceededException $err) {
            DB::rollBack();
            throw $err;
        }
    }

    public static function createFpid($nip, $nama)
    {
        $url = config('services.fpid_api.baseUrl');
        $fpidResponse = Http::withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($url, [
            'token' => config('services.bkd_service.token'),
            'nip' => $nip,
            'nama' => $nama,
        ]);
        if (!$fpidResponse->successful()) {
            throw new AppException("Gagal membuat FPID", JsonResponseType::INTERNAL_SERVER_ERROR, [], 400);
        }
        $fpid = $fpidResponse->json();
        if (isset($fpidResponse['status']) && $fpidResponse['status'] == false) {
            throw new AppException(!empty($fpidResponse['message']) ? $fpidResponse['message'] : 'Unknown error from api', JsonResponseType::INTERNAL_SERVER_ERROR, $fpidResponse->json(), 400);
        }
        if (empty($fpid['fpid'])) {
            throw new AppException("tidak menerima response FPID", JsonResponseType::INTERNAL_SERVER_ERROR, [], 400);
        }


        return $fpid;
    }

    public static function createNominate(EmployeeModel $employee)
    {
        $url = config('services.siasn_registration.baseUrl') . "/reg_nominasis";
        $nominasisResponse = Http::withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($url, [
            'token' => config('services.bkd_service.token'),
            'nip' => $employee->nip,
            'name' => $employee->nama,
            'A_01' => $employee->id_unit_kerja,
            'A_02' => $employee->id_induk_upt,
            'A_03' => $employee->id_sub_unit,
            'A_04' => $employee->id_sub_sub_unit,
            'A_05' => $employee->id_sub_sub_sub_unit,
        ]);
        if (!$nominasisResponse->successful()) {
            throw new AppException("Gagal membuat Nominasis", JsonResponseType::INTERNAL_SERVER_ERROR, $nominasisResponse->json(), 400);
        }
        $nominasis = $nominasisResponse->json();
        Log::debug("[CREATE NOMINATE]");
        Log::debug($url);
        Log::debug($nominasis);
        if (isset($nominasis['status']) && $nominasis['status'] == false) {
            throw new AppException(!empty($nominasis['message']) ? $nominasis['message'] : 'Unknown error from api', JsonResponseType::INTERNAL_SERVER_ERROR, $nominasis->json(), 400);
        }
        return $nominasis;
    }

    public static function createReguserNew(EmployeeModel $employee, $password)
    {
        $url = config('services.siasn_registration.baseUrl') . "/reg_usernew";
        $userNewResponse = Http::withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($url, [
            'token' => config('services.bkd_service.token'),
            'nip' => $employee->nip,
            'pass' => $password,
        ]);
        if (!$userNewResponse->successful()) {
            throw new AppException("Gagal membuat User new", JsonResponseType::INTERNAL_SERVER_ERROR, $userNewResponse->json(), 400);
        }
        $userNew = $userNewResponse->json();
        if (isset($userNew['status']) && $userNew['status'] == false) {
            throw new AppException(!empty($userNew['message']) ? $userNew['message'] : 'Unknown error from api', JsonResponseType::INTERNAL_SERVER_ERROR, $userNew->json(), 400);
        }
        Log::debug("[CREATE REG User New]");
        Log::debug($url);
        Log::debug($userNew);
        return $userNew;
    }

    public static function regTokenMobile(EmployeeModel $employee)
    {
        $url = config('services.siasn_registration.baseUrl') . "/reg_tokenmobile";
        $payload = [
            'token' => config('services.bkd_service.token'),
            'nip' => $employee->nip,
        ];
        $tokenMobileResponse = Http::withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($url, $payload);

        if (!$tokenMobileResponse->successful()) {
            $responseJson =  $tokenMobileResponse->json();
            Log::debug($responseJson);
            if (empty($responseJson) || empty($responseJson['message'])) {
                throw new AppException("Gagal membuat token mobile", JsonResponseType::INTERNAL_SERVER_ERROR, $tokenMobileResponse->json(), 400);
            }
            throw new AppException("Gagal membuat token mobile (" . $responseJson['message'] . ")", JsonResponseType::INTERNAL_SERVER_ERROR, $tokenMobileResponse->json(), 400);
        }


        $tokenMobile = $tokenMobileResponse->json();
        Log::debug("[CREATE  Token Mobile]");
        Log::debug($payload);
        Log::debug($url);
        Log::debug($tokenMobile);

        if (isset($tokenMobile['status']) && $tokenMobile['status'] == false) {
            throw new AppException("Gagal membuat token mobile (" . $tokenMobile['message'] . ")", JsonResponseType::INTERNAL_SERVER_ERROR, $tokenMobileResponse->json(), 400);
        }
        return $tokenMobile;
    }

    public static function reqEmailInstansi(EmployeeModel $employee, $password, $tglSk, $reqEmailInstansi)
    {
        $url = config('services.email_instansi_registration.baseUrl');
        $emailReqResponse = Http::withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Authorization' => config('services.kominfo_service.token')
        ])
        ->asForm()
        ->post($url, [
            'nip' => $employee->nip,
            'nama' => $employee->nama,
            'email' => $reqEmailInstansi,
            'tgl' => $tglSk,
            'password' => $password,
            'cpassword' => $password,
            'flag' => 'reg',
            'recovery_email' => $employee->email
        ]);

        if (!$emailReqResponse->successful()) {
            $responseJson =  $emailReqResponse->json();
            if (empty($responseJson) || empty($responseJson['message'])) {
                throw new AppException("Gagal membuat email instansi", JsonResponseType::INTERNAL_SERVER_ERROR, $emailReqResponse->json(), 400);
            }
            throw new AppException("Gagal membuat email instansi (" . $responseJson['message'] . ")", JsonResponseType::INTERNAL_SERVER_ERROR, $emailReqResponse->json(), 400);
        }

        $emailInstansi = $emailReqResponse->json();
        if (!isset($emailInstansi) || !isset($emailInstansi['success']) || !$emailInstansi['success']) {
            throw new AppException(!empty($emailInstansi['error_message']) ? $emailInstansi['error_message'] : 'Unknown error from api Email Instansi', JsonResponseType::INTERNAL_SERVER_ERROR, $emailReqResponse->json(), 400);
        }
        if (isset($emailInstansi['error']) && $emailInstansi['error'] == true) {
            throw new AppException(!empty($emailInstansi['error_message']) ? $emailInstansi['error_message'] : 'Unknown error from api Email Instansi', JsonResponseType::INTERNAL_SERVER_ERROR, $emailReqResponse->json(), 400);
        }
        Log::debug($emailInstansi);
        return $emailInstansi;
    }

    public static function update($employee_id, $payload)
    {
        $user = UserModel::where('id', Auth::user()->id)->first();
        $employee = EmployeeModel::where('id', $employee_id)->first();
        $employeeModel = new EmployeeModel;
        $isAdmin = $user->isAdmin();

        if ($isAdmin) {
            $efile_payload = [];
            foreach ($payload as $key => $value) {
                if (str_contains($key, 'efile')) {
                    if (!empty($value)) {
                        $efile_payload[$key] = $value;
                    }
                    unset($payload[$key]);
                }
            }
            // cleaning payload
            foreach ($payload as $key => $value) {
                if (!in_array($key, $employeeModel->allowedPdmFields) && !in_array($key, $employeeModel->allowedNonPdmFields)) {
                    unset($payload[$key]);
                }
                if (in_array($value, ['null', 'undefined'])) {
                    unset($payload[$key]);
                }
            }
            $employee = EmployeeModel::where('id', $employee_id)->update($payload);
            if (!empty($payload['email_instansi'])) {
                UserModel::where('id_pegawai', $employee_id)->update(['email' => $payload['email_instansi']]);
            }
            if (!empty($efile_payload)) {
                EmployeeModel::where('id', $employee_id)->first()->handleEfileUpdate($efile_payload, 2);
            }
        } 
        elseif (!$isAdmin) {
            foreach ($payload as $key => $value) {
                if (in_array($key, $employeeModel->allowedNonPdmFields)) {
                    unset($payload[$key]);
                }
            }
            // get existing pdm
            $employeeExistingPdm = PerubahanData::where('status', 0)
                ->where('id_pegawai', $employee_id)
                ->first();
            if (!empty($employeeExistingPdm)) {
                foreach ($employeeExistingPdm->toArray() as $field => $value) {
                    if (!array_key_exists($field, $payload)) {
                        $payload[$field] = $value;
                    }
                }
            }
            $employee->doPdm($payload, 2);
        }
    }

    public static function delete($employee_id)
    {
        $delete = EmployeeModel::where('id', $employee_id)->delete();
        return $delete;
    }

    public static function generateEmployeePeriodicData($employee_id)
    {
        $employee = DB::table('tb_01')->select(
            'tb_01.nip',
            'tb_01.id',
            'tb_01.id_unit_kerja',
            'tb_01.id_induk_upt',
            'tb_01.id_sub_unit',
            'tb_01.id_sub_sub_unit',
            'tb_01.id_sub_sub_sub_unit',
            'r_pendidikan_formal.id as id_riwayat_pendidikan',
            'r_pendidikan_formal.id_jenjang',
            'r_pendidikan_formal.id_jurusan',
            'r_kepangkatan.id as id_riwayat_kepangkatan',
            'r_kepangkatan.id_kepangkatan',

        )
            ->leftJoin('r_pendidikan_formal', function ($join) {
                $join->on('r_pendidikan_formal.id_pegawai', '=', 'tb_01.id')
                    ->where('r_pendidikan_formal.isakhir', 1);
            })
            ->leftJoin('r_kepangkatan', function ($join) {
                $join->on('r_kepangkatan.id_pegawai', '=', 'tb_01.id')
                    ->where('r_kepangkatan.isakhir', 1);
            })
            ->where('tb_01.id', $employee_id)
            ->first();

        $payload = [
            'employee_id' => $employee->id,
            'nip' => $employee->nip,
            'id_unit_kerja' => $employee->id_unit_kerja,
            'id_induk_upt' => $employee->id_induk_upt,
            'id_sub_unit' => $employee->id_sub_unit,
            'id_sub_sub_unit' => $employee->id_sub_sub_unit,
            'id_sub_sub_sub_unit' => $employee->id_sub_sub_sub_unit,
            'id_riwayat_pendidikan' => $employee->id_riwayat_pendidikan,
            'id_jenjang' => $employee->id_jenjang,
            'id_jurusan' => $employee->id_jurusan,
            'id_riwayat_kepangkatan' => $employee->id_riwayat_kepangkatan,
            'id_kepangkatan' => $employee->id_kepangkatan,
            'month_periode' => date('n'),
            'year_periode' => date('Y')
        ];

        EmployeePeriodicDataModel::updateOrCreate(
            [
                'employee_id' => $payload['employee_id'],
                'month_periode' => $payload['month_periode'],
                'year_periode' => $payload['year_periode'],
            ],
            $payload
        );
    }

    public static function generateSecurePassword($length = 12)
    {
        $upper = Str::random(1, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'); // At least one uppercase
        $lower = Str::random(1, 'abcdefghijklmnopqrstuvwxyz'); // At least one lowercase
        $number = Str::random(1, '0123456789'); // At least one digit
        $special = Str::random(1, '@$!%*?&_'); // At least one special character

        // Fill the remaining length with a mix of characters
        $remainingLength = $length - 4;
        $randomChars = Str::random($remainingLength);

        // Combine all characters
        $password = $upper . $lower . $number . $special . $randomChars;

        // Shuffle the password to randomize character positions
        return str_shuffle($password);
    }

    public static function updateStatusPernikahan($employeeId)
    {
        $pasanganAktif = KeluargaIstriSuamiModel::where('id_pegawai', $employeeId)
                            ->where('status_perkawinan', 1)
                            ->count();

        $pasanganCerai = KeluargaIstriSuamiModel::where('id_pegawai', $employeeId)
                            ->whereIn('status_perkawinan', [2, 3])
                            ->count();

        if ($pasanganAktif > 0) {
            $statusKawin = 1;
        }
        else {
            if ($pasanganCerai > 0) {
                $statusKawin = 2;
            }
            else {
                $statusKawin = 3;
            }
        }
        
        EmployeeModel::find($employeeId)->update(['status_pernikahan' => $statusKawin]);
    }
}
