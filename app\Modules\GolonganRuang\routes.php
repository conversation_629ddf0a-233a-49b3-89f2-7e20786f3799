<?php
namespace App\Modules\GolonganRuang;

use App\Modules\GolonganRuang\Controllers\GolonganRuangController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/golongan-ruang')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [GolonganRuangController::class, 'index'])->middleware('authorize:read-golongan_ruang');
    Route::get('/datatable', [GolonganRuangController::class, 'datatable'])->middleware('authorize:read-golongan_ruang');
    Route::get('/create', [GolonganRuangController::class, 'create'])->middleware('authorize:create-golongan_ruang');
    Route::post('/', [GolonganRuangController::class, 'store'])->middleware('authorize:create-golongan_ruang');
    Route::get('/{golongan_ruang_id}', [Golongan<PERSON>uangController::class, 'show'])->middleware('authorize:read-golongan_ruang');
    Route::get('/{golongan_ruang_id}/detail', [GolonganRuangController::class, 'detail'])->middleware('authorize:read-golongan_ruang');
    Route::get('/{golongan_ruang_id}/edit', [GolonganRuangController::class, 'edit'])->middleware('authorize:update-golongan_ruang');
    Route::put('/{golongan_ruang_id}', [GolonganRuangController::class, 'update'])->middleware('authorize:update-golongan_ruang');
    Route::patch('/{golongan_ruang_id}', [GolonganRuangController::class, 'update'])->middleware('authorize:update-golongan_ruang');
    Route::delete('/{golongan_ruang_id}', [GolonganRuangController::class, 'destroy'])->middleware('authorize:delete-golongan_ruang');
});