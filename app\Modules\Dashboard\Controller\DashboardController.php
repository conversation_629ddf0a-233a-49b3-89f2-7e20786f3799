<?php

namespace App\Modules\Dashboard\Controller;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Pengumuman\Models\Pengumuman;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        return view('Dashboard::index');
    }

    public function statistic(Request $request)
    {
        $totalPegawai = EmployeeModel::active()->whereUserHaveAccess('tb_01')->count();
        $cpns = EmployeeModel::cpns()->whereUserHaveAccess('tb_01')->count();
        $pns = EmployeeModel::pns()->whereUserHaveAccess('tb_01')->count();
        $pppk = EmployeeModel::pppk()->whereUserHaveAccess('tb_01')->count();
        $pengumuman = Pengumuman::where('status', 1)->count();

        return JsonResponseHandler::setResult([
            'total' => $totalPegawai,
            'cpns' => $cpns,
            'pns' => $pns,
            'pppk' => $pppk,
            'pengumuman' => $pengumuman
        ])->send();
    }

    public function pengumuman(Request $request)
    {
        $pengumuman = Pengumuman::where('status', 1)->get();
        return JsonResponseHandler::setResult($pengumuman)->send();
    }

    public function pnsCountByGolongan(Request $request)
    {
        $groupedKepangkatan = DB::table('r_kepangkatan')
            ->select('r_kepangkatan.id', 'r_kepangkatan.id_kepangkatan', 'kepangkatan.name')
            ->join('tb_01', 'tb_01.id', '=', 'r_kepangkatan.id_pegawai')
            ->join('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
            ->whereRaw('r_kepangkatan.isakhir = 1')
            ->whereRaw('tb_01.kedudukan_pegawai NOT IN (99,21)')
            ->whereRaw('tb_01.status_pegawai = 2')
            // ->where(function ($query) {
            //     return (new EmployeeModel())->scopeWhereUserHaveAccess($query, 'tb_01.id_unit_kerja');
            // })
            ->groupBy('tb_01.nip', 'r_kepangkatan.id', 'r_kepangkatan.id_kepangkatan', 'kepangkatan.name');

        $groupedCount = DB::table(DB::raw("({$groupedKepangkatan->toSql()}) as grouped_kepangkatan"))
            // ->mergeBindings($groupedKepangkatan)
            ->select('grouped_kepangkatan.name', DB::raw('COUNT(*) as count'))
            ->groupBy('grouped_kepangkatan.id_kepangkatan')
            ->orderBy('grouped_kepangkatan.id_kepangkatan','asc')
            ->get();

        return JsonResponseHandler::setResult($groupedCount)->send();
    }

    public function cpnsCountByGolongan(Request $request)
    {
        $groupedKepangkatan = DB::table('r_kepangkatan')
            ->select('r_kepangkatan.id', 'r_kepangkatan.id_kepangkatan', 'kepangkatan.name')
            ->join('tb_01', 'tb_01.id', '=', 'r_kepangkatan.id_pegawai')
            ->join('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
            ->whereRaw('r_kepangkatan.isakhir = 1')
            ->whereRaw('tb_01.kedudukan_pegawai NOT IN (99,21)')
            ->whereRaw('tb_01.status_pegawai = 1')
            // ->where(function ($query) {
            //     return (new EmployeeModel())->scopeWhereUserHaveAccess($query, 'tb_01.id_unit_kerja');
            // })
            ->groupBy('tb_01.nip', 'r_kepangkatan.id', 'r_kepangkatan.id_kepangkatan', 'kepangkatan.name');

        $groupedCount = DB::table(DB::raw("({$groupedKepangkatan->toSql()}) as grouped_kepangkatan"))
            // ->mergeBindings($groupedKepangkatan)
            ->select('grouped_kepangkatan.name', DB::raw('COUNT(*) as count'))
            ->groupBy('grouped_kepangkatan.id_kepangkatan')
            ->orderBy('grouped_kepangkatan.id_kepangkatan','asc')
            ->get();

        return JsonResponseHandler::setResult($groupedCount)->send();
    }

    public function pppkCountByGolongan(Request $request)
    {
        $groupedKepangkatan = DB::table('r_pppk')
            ->select('r_pppk.id', 'r_pppk.id_golongan_ruang', 'kepangkatan.golru_p3k as name')
            ->join('tb_01', 'tb_01.id', '=', 'r_pppk.id_pegawai')
            ->join('kepangkatan', 'r_pppk.id_golongan_ruang', '=', 'kepangkatan.id')
            ->whereRaw('r_pppk.isakhir = 1')
            ->whereRaw('tb_01.kedudukan_pegawai NOT IN (99,21)')
            ->whereRaw('tb_01.status_pegawai = 3')
            // ->where(function ($query) {
            //     return (new EmployeeModel())->scopeWhereUserHaveAccess($query, 'tb_01.id_unit_kerja');
            // })
            ->groupBy('tb_01.nip', 'r_pppk.id', 'r_pppk.id_golongan_ruang', 'kepangkatan.name');

        $groupedCount = DB::table(DB::raw("({$groupedKepangkatan->toSql()}) as grouped_kepangkatan"))
            // ->mergeBindings($groupedKepangkatan)
            ->select('grouped_kepangkatan.name', DB::raw('COUNT(*) as count'))
            ->groupBy('grouped_kepangkatan.id_golongan_ruang')
            ->orderBy('grouped_kepangkatan.id_golongan_ruang','asc')
            ->get();

        return JsonResponseHandler::setResult($groupedCount)->send();
    }
}
