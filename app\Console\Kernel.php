<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('generate:gaji_pns')->monthlyOn(30, '7:30')->timezone('Asia/Jakarta');
        $schedule->command('generate:gaji_pppk')->monthlyOn(30, '7:30')->timezone('Asia/Jakarta');
        $schedule->command('generate:tpp_pns')->monthlyOn(30, '7:30')->timezone('Asia/Jakarta');
        $schedule->command('generate:tpp_pppk')->monthlyOn(30, '7:30')->timezone('Asia/Jakarta');
        $schedule->command('sync:simgaji_kgb')->monthlyOn(30, '7:30')->timezone('Asia/Jakarta');
        $schedule->command('sync:transpkt_r_kepangkatan')->monthlyOn(30, '7:30')->timezone('Asia/Jakarta');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
