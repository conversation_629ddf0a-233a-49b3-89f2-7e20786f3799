@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jenkedudupeg">
    <default-datatable title="JenKeduduPeg" url="{!! url('jenkedudupeg') !!}" :headers="headers" :can-add="{{ $permissions['create-jenkedudupeg'] }}" :can-edit="{{ $permissions['update-jenkedudupeg'] }}" :can-delete="{{ $permissions['delete-jenkedudupeg'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'name',
        						text: 'name'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jenkedudupeg');
</script>
@endsection