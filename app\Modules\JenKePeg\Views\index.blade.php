@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jenkepeg">
    <default-datatable title="JenKePeg" url="{!! url('jenkepeg') !!}" :headers="headers" :can-add="{{ $permissions['create-jenkepeg'] }}" :can-edit="{{ $permissions['update-jenkepeg'] }}" :can-delete="{{ $permissions['delete-jenkepeg'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'jenkepeg',
        						text: 'jenkepeg'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jenkepeg');
</script>
@endsection