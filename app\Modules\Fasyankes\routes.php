<?php
namespace App\Modules\Fasyankes;

use App\Modules\Fasyankes\Controllers\FasyankesController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/fasyankes')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [FasyankesController::class, 'index']);
    Route::get('/datatable', [FasyankesController::class, 'datatable'])->middleware('authorize:read-fasyankes');
    Route::get('/create', [FasyankesController::class, 'create'])->middleware('authorize:create-fasyankes');
    Route::post('/', [FasyankesController::class, 'store'])->middleware('authorize:create-fasyankes');
    Route::get('/{fasyankes_id}', [FasyankesController::class, 'show'])->middleware('authorize:read-fasyankes');
    Route::get('/{fasyankes_id}/edit', [FasyankesController::class, 'edit'])->middleware('authorize:update-fasyankes');
    Route::put('/{fasyankes_id}', [FasyankesController::class, 'update'])->middleware('authorize:update-fasyankes');
    Route::delete('/{fasyankes_id}', [FasyankesController::class, 'destroy'])->middleware('authorize:delete-fasyankes');
});