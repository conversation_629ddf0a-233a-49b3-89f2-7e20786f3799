<?php

namespace App\Modules\JenHukDis\Repositories;

use App\Modules\JenHukDis\Models\JenHukDisModel;

class JenHukDisRepository
{
    public static function datatable($per_page = 15)
    {
        $data =  JenHukDisModel::paginate($per_page);
        return $data;
    }
    public static function get($jeniskp_id)
    {
        $jeniskp = JenHukDisModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }
    public static function create($jeniskp)
    {
        $jeniskp = JenHukDisModel::create($jeniskp);
        return $jeniskp;
    }

    public static function update($jeniskp_id, $jeniskp)
    {
        JenHukDisModel::where('id', $jeniskp_id)->update($jeniskp);
        $jeniskp = JenHukDisModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }

    public static function delete($jeniskp_id)
    {
        $delete = JenHukDisModel::where('id', $jeniskp_id)->delete();
        return $delete;
    }
}
