<?php
namespace App\Modules\Dashboard;

use App\Modules\Dashboard\Controller\DashboardController;
use Illuminate\Support\Facades\Route;

Route::prefix('/dashboard')->group(function() {
    Route::get('/', [DashboardController::class, 'index']);
    Route::get('/statistic', [DashboardController::class, 'statistic']);
    Route::get('/pengumuman', [DashboardController::class, 'pengumuman']);
    Route::get('/pns-count-by-golongan', [DashboardController::class, 'pnsCountByGolongan']);
    Route::get('/cpns-count-by-golongan', [DashboardController::class, 'cpnsCountByGolongan']);
    Route::get('/pppk-count-by-golongan', [DashboardController::class, 'pppkCountByGolongan']);
});
