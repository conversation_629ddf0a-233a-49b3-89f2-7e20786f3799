@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="executive-summary">
    <default-datatable title="ExecutiveSummary" url="{!! url('executive-summary') !!}" :headers="headers" :can-add="{{ $permissions['create-executive_summary'] }}" :can-edit="{{ $permissions['update-executive_summary'] }}" :can-delete="{{ $permissions['delete-executive_summary'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#executive-summary');
</script>
@endsection