@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jabfungum">
    <default-datatable title="JabFungUm" url="{!! url('jabfungum') !!}" :headers="headers" :can-add="{{ $permissions['create-jabfungum'] }}" :can-edit="{{ $permissions['update-jabfungum'] }}" :can-delete="{{ $permissions['delete-jabfungum'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jabfungum');
</script>
@endsection