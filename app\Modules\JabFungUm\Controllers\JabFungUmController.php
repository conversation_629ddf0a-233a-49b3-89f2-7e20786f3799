<?php

namespace App\Modules\JabFungUm\Controllers;

use Illuminate\Http\Request;
use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JabFungUm\Models\JabFungUm;
use App\Modules\JabFungUm\Requests\JabFungUmCreateRequest;
use App\Modules\JabFungUm\Repositories\JabFungUmRepository;
use App\Modules\Permission\Repositories\PermissionRepository;

class JabFungUmController extends Controller
{
    public function index(Request $request)
    {
        $data = JabFungUm::where('flag', 1)
            ->selectRaw("
                idjabfungum,
                concat(if(kelas_jabatan, concat(kelas_jabatan, ' - '), ''), jabfungum) as jabfungum,
                kelas_jabatan
            ")
            ->get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $keyword = $request->input('keyword') ?? null;
        $data = JabFungUmRepository::datatable($per_page, $keyword);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $jabfungum_id)
    {
        $jabfungum = JabFungUm::where('idjabfungum', $jabfungum_id)->first();
        return JsonResponseHandler::setResult($jabfungum)->send();
    }

    public function create()
    {
        return view('JabFungUm::create');
    }

    public function store(JabFungUmCreateRequest $request)
    {
        $payload = $request->all();
        $jabfungum = JabFungUmRepository::create($payload);
        return JsonResponseHandler::setResult($jabfungum)->send();
    }

    public function show(Request $request, $id)
    {
        $jabfungum = JabFungUmRepository::get($id);
        return JsonResponseHandler::setResult($jabfungum)->send();
    }

    public function edit($id)
    {
        return view('JabFungUm::edit', ['jabfungum_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jabfungum = JabFungUmRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jabfungum)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JabFungUmRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
