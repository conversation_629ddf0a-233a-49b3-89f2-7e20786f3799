<?php

namespace App\Modules\Bidang\Repositories;

use App\Modules\Bidang\Models\Bidang;

class BidangRepository
{
    public static function datatable($per_page = 15)
    {
        $data = Bidang::paginate($per_page);
        return $data;
    }
    public static function get($bidang_id)
    {
        $bidang = Bidang::where('id', $bidang_id)->first();
        return $bidang;
    }
    public static function create($bidang)
    {
        $bidang = Bidang::create($bidang);
        return $bidang;
    }

    public static function update($bidang_id, $bidang)
    {
        Bidang::where('id', $bidang_id)->update($bidang);
        $bidang = Bidang::where('id', $bidang_id)->first();
        return $bidang;
    }

    public static function delete($bidang_id)
    {
        $delete = Bidang::where('id', $bidang_id)->delete();
        return $delete;
    }
}
