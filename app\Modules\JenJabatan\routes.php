<?php
namespace App\Modules\JenJabatan;

use App\Modules\JenJabatan\Controllers\JenJabatanController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenjabatan')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenJabatanController::class, 'index']);
    Route::get('/datatable', [JenJabatanController::class, 'datatable']);
    Route::get('/subjabatan', [JenJabatanController::class, 'subJabatanAll']);
    Route::get('/{jabatan_id}/subjabatan', [JenJabatanController::class, 'subjabatan']);
    Route::post('/', [JenJabatanController::class, 'store']);
    Route::get('/{jabatan_id}/detail', [JenJabatanController::class, 'detail']);
    Route::patch('{jabatan_id}', [JenJabatanController::class, 'update']);
    Route::delete('{jabatan_id}', [JenJ<PERSON>tanController::class, 'delete']);
});