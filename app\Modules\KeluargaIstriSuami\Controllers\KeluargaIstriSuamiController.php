<?php

namespace App\Modules\KeluargaIstriSuami\Controllers;

use App\Exceptions\AppException;
use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\User\Model\UserModel;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiTempModel;
use App\Modules\KeluargaIstriSuami\Repositories\KeluargaIstriSuamiRepository;
use App\Modules\KeluargaIstriSuami\Requests\KeluargaIstriSuamiCreateRequest;
use App\Modules\Efile\Repositories\EfileRepository;
use Illuminate\Http\Request;

use Exception;
use App\Handler\FileHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

use function Psy\debug;

class KeluargaIstriSuamiController extends Controller
{
    public function employeeDatatable(Request $request, $employeeId)
    {
        $per_page = $request->input('per_page') ?? 15;
        $datatable = KeluargaIstriSuamiRepository::employeeDatatable($employeeId, $per_page);
        $items = $datatable->items();
        $datas = [];

        foreach ($items as $item) {
            $datas[] = $item->toArray();
        }

        if (!empty($datas)) {            
            $employee = EmployeeModel::find($employeeId);
            $efilesCode = KeluargaIstriSuamiModel::EFILE_CODE_MAPPING;
            $requiredEfileCodes = ["10", "11", "19_2", "29_2", "48", "35_2", "23_2"];

            $efilesFromApi = (new EfileRepository())->getFileByNipAndCodes($employee->nip, implode(",", $requiredEfileCodes));
            // dd($efilesFromApi);
            $efiles = collect($efilesFromApi)
                        ->keyBy('id_jenis')
                        ->all();

            if (!empty($efiles)) {
                foreach ($datas as $dataKey => $data) {
                    foreach ($efiles as $efileKey => $efile) {
                        if ($efile['response'] == 'success' && !empty($efile)) {
                            if ($efile['riwayat'] == 0) {
                                $datas[$dataKey][$efilesCode[$efileKey]] = $efile['efile'][0];
                            }
                            else if ($efile['riwayat'] == 1) {
                                $efileCollection = collect($efile['efile']);
                                $datas[$dataKey][$efilesCode[$efileKey]] = $efileCollection
                                    ->filter(function ($value) use ($data) {
                                        return $value['id_riwayat'] == $data['old_id'] || $value['id_riwayat'] == $data['id'];
                                    })
                                    ->sortByDesc('modified_at')
                                    ->first();
                            }
                        }
                        else {
                            $datas[$dataKey][$efilesCode[$efileKey]] = null;
                        }
                    }
                }
            }
        }

        $modifiedData = new \Illuminate\Pagination\LengthAwarePaginator(
            $datas,
            $datatable->total(),
            $datatable->perPage(),
            $datatable->currentPage(),
            ['path' => $datatable->path()]
        );

        return JsonResponseHandler::setResult($modifiedData)->send();
    }

    public function createSubmission(KeluargaIstriSuamiCreateRequest $request, $employeeId)
    {
        $payload = $request->only([
            'id',
            'nip',
            'status_asn',
            'status_pasangan',
            'pernikahan_ke',
            'nama_pasangan',
            'tempat_lahir',
            'tanggal_lahir',
            'alamat',
            'alamat_rt',
            'alamat_rw',
            'alamat_kode_pos',
            'alamat_provinsi',
            'alamat_kabupaten',
            'alamat_kecamatan',
            'alamat_kelurahan',
            'alamat_long',
            'alamat_lat',
            'ktp_alamat',
            'ktp_alamat_rt',
            'ktp_alamat_rw',
            'ktp_alamat_kode_pos',
            'ktp_alamat_provinsi',
            'ktp_alamat_kabupaten',
            'ktp_alamat_kecamatan',
            'ktp_alamat_kelurahan',
            'ktp_alamat_long',
            'ktp_alamat_lat',
            'status_tunjangan',
            'status_tunjangan_final',
            'status_perkawinan',
            'nikah_tanggal',
            'nikah_nomor',
            'kematian_tanggal',
            'kematian_nomor',
            'cerai_tanggal',
            'cerai_nomor',
            'nik',
            'id_pendidikan',
            'pekerjaan',
            'pekerjaan_detail',
            'pekerjaan_status',
            'pekerjaan_nip',
            'pekerjaan_kantor',
            'pekerjaan_long',
            'pekerjaan_lat',
            // 'satus_tunjangan',
            'karis_karsu',
            'nomor_hp',
            'nomor_bpjs',
            'nomor_akta_lahir',
            'tanggal_akta_lahir',
            'karis_karsu_efile',
            'nomor_nikah_efile',
            'nomor_akta_kematian_efile',
            'nomor_akta_cerai_efile',
            'dokumen_kepegawaian_efile',
            'bpjs_pasangan_efile',
            'nomor_akta_lahir_efile'
        ]);

        try {
            $err = 0; $message = "";
            if(((strlen($payload['nik']) < 16) && $payload['nik'] != '-') && ($payload['status_perkawinan'] != 2)){
                $err = $err+1; $message = "NIK tidak valid, minimal terdiri dari 16 karakter. <br>";
            }
            if(($payload['status_asn'] == 2) && ($payload['status_perkawinan'] != 2)){
                $cekortu = EmployeeModel::where('nip',@$payload['pekerjaan_nip'])->where('nik','!=',$payload['nik'])->count();
                if($cekortu > 0){
                    $err = $err+1; $message .="NIP Pasangan tercatat sebagai ASN jawa Tengah, tetapi NIK tidak valid.";
                }
            }
            if($payload['status_perkawinan'] == 1){
                $cekpasangan = KeluargaIstriSuamiModel::where('id_pegawai', '!=', $employeeId)
                                ->where('nik',$payload['nik'])
                                ->where('status_perkawinan','=',1)
                                ->first();

                $cekpasangan2 = KeluargaIstriSuamiModel::where('id_pegawai', $employeeId)
                                ->where('nik', '!=', $payload['nik'])
                                ->where('status_perkawinan','=',1)
                                ->first();

                if($cekpasangan){
                    $err = $err+1; $message .="NIK Pasangan <b>" . strtoupper($cekpasangan->nama_pasangan) . "</b> tercatat masih berstatus pasangan aktif dari pegawai lain.";
                }
                if($cekpasangan2){
                    $err = $err+1; $message .="Pegawai berstatus masih memiliki pasangan aktif. Silahkan ubah status pernikahan atau hapus data dari pasangan <b>" . strtoupper($cekpasangan2->nama_pasangan) . "</b> sebelum menambahkan data pasangan baru.";
                }
            }
    
            if ($err > 0) {
                return response()->json([
                    'status' => 'error', 
                    'message' => $message, 
                    'data' => ''
                ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
            }
    
            $employee = EmployeeModel::where('id', $employeeId)->first();
            $nip = $employee->nip;
            $timestamp = date("Ymd-H_i_s");
            $fileName = [];
            // Storing efile into submission/pasangan foler
            foreach ($payload as $key => $value) {
                if (str_contains($key, 'efile') && $request->hasFile($key)) {
                    $prefix = str_replace(['nomor_', '_efile', 'dokumen_'], ['', '', ''], $key);
                    $docName = implode('_', array_map('ucfirst', explode('_', strtolower($prefix))));
                    $fileName[$key] = "Dokumen_" . $docName . "_" . $nip . "_" . $timestamp;
                    $payload[$key] = FileHandler::store(
                        file: $request->file($key), 
                        targetDir: KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, 
                        fileName: $fileName[$key], 
                        isNeedException: false
                    );

                    // Error handling
                    if (is_array($payload[$key]) && $payload[$key]['success'] === false) {
                        $payload[$key]['message'] = 'Terjadi kesalahan saat menyimpan Dokumen ' . str_replace('_', ' ', $docName) . ': ' . $payload[$key]['message'];
                        throw new AppException(
                            $payload[$key]['message'],
                            $payload[$key]['code'],
                            $payload[$key]['status']
                        );
                    }
                }
            }

            // dd($fileName);
            
            $payload['id_pegawai'] = $employeeId;
            $payload['nip'] = $employee->nip;
            $riwayat = KeluargaIstriSuamiRepository::createSubmission($payload);
            // dd($riwayat);
            return JsonResponseHandler::setResult($riwayat)->send();
        }
        catch (AppException $e) {
            foreach ($payload as $key => $value) {
                if (str_contains($key, 'efile') && is_string($payload[$key])) {
                    $ext = $request->file($key)->extension();
                    $payload[$key] = FileHandler::delete(KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR . '/' . $fileName[$key] . '.' . $ext);
                }
            }
            
            return JsonResponseHandler::setResult($e)
                ->setMessage($e->getMessage())
                ->setCode($e->getCode())
                ->setStatus(422)
                ->send();
        }
        
    }
    public function employeeRiwayat(Request $request, $employeeId, $riwayatId)
    {
        $data = KeluargaIstriSuamiModel::where('id', $riwayatId)
                ->where('id_pegawai', $employeeId)
                ->first();
        return JsonResponseHandler::setResult($data)->send();
    }
    public function createDeleteSubmission(Request $request, $employeeId, $riwayatPangkatId)
    {
        $riwayat = KeluargaIstriSuamiRepository::deleteSubmission($riwayatPangkatId);
        return JsonResponseHandler::setResult($riwayat)->send();
    }
    public function updateSubmission(Request $request, $employee_id, $riwayatId)
    {
        $employee = EmployeeModel::where('id', $employee_id)->first();
        $payload = $request->only([
            'id',
            'id_pegawai',
            'nip',
            'status_asn',
            'status_pasangan',
            'pernikahan_ke',
            'nama_pasangan',
            'tempat_lahir',
            'tanggal_lahir',
            'alamat',
            'alamat_rt',
            'alamat_rw',
            'alamat_kode_pos',
            'alamat_provinsi',
            'alamat_kabupaten',
            'alamat_kecamatan',
            'alamat_kelurahan',
            'alamat_long',
            'alamat_lat',
            'ktp_alamat',
            'ktp_alamat_rt',
            'ktp_alamat_rw',
            'ktp_alamat_kode_pos',
            'ktp_alamat_provinsi',
            'ktp_alamat_kabupaten',
            'ktp_alamat_kecamatan',
            'ktp_alamat_kelurahan',
            'ktp_alamat_long',
            'ktp_alamat_lat',
            'status_tunjangan',
            'status_tunjangan_final',
            'status_perkawinan',
            'nikah_tanggal',
            'nikah_nomor',
            'kematian_tanggal',
            'kematian_nomor',
            'cerai_tanggal',
            'cerai_nomor',
            'nik',
            'id_pendidikan',
            'pekerjaan',
            'pekerjaan_detail',
            'pekerjaan_status',
            'pekerjaan_nip',
            'pekerjaan_kantor',
            'pekerjaan_long',
            'pekerjaan_lat',
            // 'satus_tunjangan',
            'karis_karsu',
            'nomor_hp',
            'nomor_bpjs',
            'nomor_akta_lahir',
            'tanggal_akta_lahir',
            'nomor_nikah_efile',
            'nomor_akta_kematian_efile',
            'nomor_akta_cerai_efile',
            'dokumen_kepegawaian_efile',
            'bpjs_pasangan_efile',
            'nomor_akta_lahir_efile',
            'karis_karsu_efile'
        ]);
        // dd($);
        foreach ($payload as $key => $data) {
            if (str_contains($key, 'efile') && $request->hasFile($key)) {
                FileHandler::validateEfile($data, $key);
            }
        };

        $err = 0; $message = "";
        if(((strlen($payload['nik']) < 16) && $payload['nik'] != '-') && ($payload['status_perkawinan'] != 2)){
            $err = $err+1; $message = "Status Pasangan masih hidup, tetapi NIK tidak valid. <br>";
        }

        if(($payload['status_asn'] == 2) && ($payload['status_perkawinan'] != 2) && isset($payload['pekerjaan_nip']) && isset($payload['nik'])){
            $cekpasangantb01 = EmployeeModel::where('nip', $payload['pekerjaan_nip'])
                                ->where('nik', '!=', $payload['nik'])
                                ->where('id', '!=', '')
                                ->first();

            if($cekpasangantb01){
                $err = $err+1; 
                $message .="NIP Pasangan atas nama <b>" . strtoupper($cekpasangantb01->nama) . "</b> tercatat sebagai ASN jawa Tengah, tetapi NIK tidak valid.<br><br>Silahkan ubah data NIK pasangan anda melalui akun SIASN pasangan anda saat periode PDM, atau hubungi admin SIASN untuk mengubah data SIASN pasangan anda.";
            }
        }
        if($payload['status_perkawinan'] == 1){
            $cekpasangan = KeluargaIstriSuamiModel::where('id_pegawai', '!=', $employee_id)
                            ->where('nik',$payload['nik'])
                            ->where('status_perkawinan','=',1)
                            ->first();

            $cekpasangan2 = KeluargaIstriSuamiModel::where('id_pegawai', $employee_id)
                            ->where('id', '!=', $riwayatId)
                            ->where('status_perkawinan','=',1)
                            ->first();

            if($cekpasangan){
                $err = $err+1; $message .="NIK Pasangan <b>" . strtoupper($cekpasangan->nama_pasangan) . "</b> tercatat masih berstatus pasangan aktif dari pegawai lain.";
            }
            if($cekpasangan2){
                $err = $err+1; $message .="Pegawai berstatus masih memiliki pasangan aktif. Silahkan ubah status pernikahan atau hapus data dari pasangan <b>" . strtoupper($cekpasangan2->nama_pasangan) . "</b> sebelum menambahkan data pasangan baru.";
            }
        }

        if ($err > 0) {
            return response()->json([ 'status' => 'error', 'message' => $message, 'data' => ''], JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
        }

        if ($request->hasFile('nomor_nikah_efile')) {
            $payload['nomor_nikah_efile'] = FileHandler::store($request->file('nomor_nikah_efile'), KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, "Dokumen_Nikah_Submission_" . date("Ymd_His") . $employee->nip);
        } else {
            $payload['nomor_nikah_efile'] = null;
        }
        if ($request->hasFile('nomor_akta_kematian_efile')) {
            $payload['nomor_akta_kematian_efile'] = FileHandler::store($request->file('nomor_akta_kematian_efile'), KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, "Dokumen_Kematian_Submission_" . date("Ymd_His") . $employee->nip);
        } else {
            $payload['nomor_akta_kematian_efile'] = null;
        }
        if ($request->hasFile('nomor_akta_cerai_efile')) {
            $payload['nomor_akta_cerai_efile'] = FileHandler::store($request->file('nomor_akta_cerai_efile'), KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, "Dokumen_Cerai_Submission_" . date("Ymd_His"));
        } else {
            $payload['nomor_akta_cerai_efile'] = null;
        }
        if ($request->hasFile('dokumen_kepegawaian_efile')) {
            $payload['dokumen_kepegawaian_efile'] = FileHandler::store($request->file('dokumen_kepegawaian_efile'), KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, "Dokumen_Kepegawaian_Submission_" . date("Ymd_His") . $employee->nip);
        } else {
            $payload['dokumen_kepegawaian_efile'] = null;
        }
        if ($request->hasFile('bpjs_pasangan_efile')) {
            $payload['bpjs_pasangan_efile'] = FileHandler::store($request->file('bpjs_pasangan_efile'), KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, "Dokumen_Bpjs_Submission_" . date("Ymd_His") . $employee->nip);
        } else {
            $payload['bpjs_pasangan_efile'] = null;
        }
        if ($request->hasFile('nomor_akta_lahir_efile')) {
            $payload['nomor_akta_lahir_efile'] = FileHandler::store($request->file('nomor_akta_lahir_efile'), KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, "Dokumen_Lahir_Submission_" . date("Ymd_His") . $employee->nip);
        } else {
            $payload['nomor_akta_lahir_efile'] = null;
        }
        if ($request->hasFile('karis_karsu_efile')) {
            $payload['karis_karsu_efile'] = FileHandler::store($request->file('karis_karsu_efile'), KeluargaIstriSuamiModel::PASANGAN_EFILE_DIR, "Dokumen_Karis_Karsu_Submission_" . date("Ymd_His") . $employee->nip);
        } else {
            $payload['karis_karsu_efile'] = null;
        }

        unset($payload['created_at']);
        unset($payload['updated_at']);
        $riwayat = KeluargaIstriSuamiRepository::updateSubmission($riwayatId, $payload);
        return JsonResponseHandler::setResult($riwayat)->send();
    }

    public function submissionDatatable(Request $request)
    {
        $employee_id = $request->input('employee_id');
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = KeluargaIstriSuamiTempModel::select("r_pasangan_temp.*", "a_pekerjaan.pekerjaan as pekerjaan_name", DB::raw("if(r_pasangan_temp.status_perkawinan=1,'Kawin',if(r_pasangan_temp.status_perkawinan=2,'Cerai Mati',if(r_pasangan_temp.status_perkawinan=3,'Cerai Hidup','-'))) as status"))
            ->where('status', 0)
            ->leftJoin('a_pekerjaan', 'r_pasangan_temp.pekerjaan', '=', 'a_pekerjaan.id')
            ->with(['employee' => function ($query) {
                $query->select('id', 'nip', 'nama', 'gelar_depan', 'gelar_belakang');
            }])
            ->where('id_pegawai', $employee_id)
            ->paginate($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    // public function submissionDetail(Request $request, $submissionId)
    // {

    //     $submission = KeluargaIstriSuamiTempModel::where('id', $submissionId)->with(['initial'])->first();
    //     return JsonResponseHandler::setResult($suupdateSubmission)->send();
    // }

    // public function submissionApprove(Request $request, $submissionId)
    // {

    //     $submission = KeluargaIstriSuamiTempModel::where('id', $submissionId)->first();
    //     $payload = $request->only(['id','nip', 'status_asn', 'status_pasangan', 'pernikahan_ke', 'nama_pasangan', 'tempat_lahir', 'tanggal_lahir', 'alamat', 'alamat_rt', 'alamat_rw', 'alamat_kode_pos', 'alamat_provinsi', 'alamat_kabupaten', 'alamat_kecamatan', 'alamat_kelurahan', 'alamat_long', 'alamat_lat', 'ktp_alamat', 'ktp_alamat_rt', 'ktp_alamat_rw', 'ktp_alamat_kode_pos', 'ktp_alamat_provinsi', 'ktp_alamat_kabupaten', 'ktp_alamat_kecamatan', 'ktp_alamat_kelurahan', 'ktp_alamat_long', 'ktp_alamat_lat', 'status_tunjangan', 'status_perkawinan', 'nikah_tanggal', 'nikah_nomor', 'kematian_tanggal', 'kematian_nomor', 'cerai_tanggal', 'cerai_nomor', 'nik', 'id_pendidikan', 'pekerjaan', 'pekerjaan_detail', 'pekerjaan_status', 'pekerjaan_nip', 'pekerjaan_kantor', 'pekerjaan_long', 'pekerjaan_lat', 'satus_tunjangan', 'karis_karsu', 'nomor_hp', 'nomor_bpjs', 'nomor_akta_lahir', 'tanggal_akta_lahir']);

    //     if ($submission->idjnsaksi == 1) {
    //         $payload['id_pegawai'] = $submission->id_pegawai;
    //         $riwayat = KeluargaIstriSuamiModel::create($payload);
    //     }
    //     if ($submission->idjnsaksi == 2) {
    //         $riwayat = KeluargaIstriSuamiModel::where('id', $submission->id_riwayat)->update($payload);
    //     }
    //     if ($submission->idjnsaksi == 3) {
    //         $riwayat = KeluargaIstriSuamiModel::where('id', $submission->id_riwayat)->delete();
    //     }
    //     KeluargaIstriSuamiTempModel::where('id', $submissionId)->update(['status' => 1]);
    //     return JsonResponseHandler::setResult(['status' => 'success'])->send();
    // }

    public function employeeSubmissionStatus(Request $request, $employeeId)
    {
        $statusCounts = KeluargaIstriSuamiTempModel::where('id_pegawai', $employeeId)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->mapWithKeys(function ($count, $status) {
                return ["status_{$status}" => $count];
            });

        return JsonResponseHandler::setResult($statusCounts)->send();
    }

    public function processSubmission(Request $request, $id)
    {
        $status = $request->input('status');
        $remark = $request->input('remark');

        $updated = (new KeluargaIstriSuamiModel())->processSubmission($id, $status, $remark);
        return JsonResponseHandler::setResult($updated)->send();
    }

    public function fieldDetail($id)
    {
        $data = (new KeluargaIstriSuamiModel())->getPdmFieldDetail($id);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function updateField(Request $request, $perubahan_data_id)
    {

        $field = $request->input('field');
        $value = $request->input('value');
        $status = $request->input('status');
        $remark = $request->input('remark');

        $updated = (new KeluargaIstriSuamiModel())->updatePdmField($perubahan_data_id, $field, $value, $status, $remark);
        return JsonResponseHandler::setResult($updated)->send();
    }

    public function timeline($id)
    {
        $data = (new KeluargaIstriSuamiModel())->getPdmTimeline($id);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function employeeDatatableusulan(Request $request, $employeeId)
    {
        $per_page = $request->input('per_page') ?? 15;
        $data = KeluargaIstriSuamiRepository::employeeUsulanDatatable($employeeId, $per_page);
        return JsonResponseHandler::setResult($data)->send();
    }
}
