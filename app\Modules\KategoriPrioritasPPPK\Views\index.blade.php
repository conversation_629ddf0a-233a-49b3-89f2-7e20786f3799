@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="kategori-prioritas-pppk">
    <default-datatable title="KategoriPrioritasPPPK" url="{!! url('kategori-prioritas-pppk') !!}" :headers="headers" :can-add="{{ $permissions['create-kategori_prioritas_pppk'] }}" :can-edit="{{ $permissions['update-kategori_prioritas_pppk'] }}" :can-delete="{{ $permissions['delete-kategori_prioritas_pppk'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#kategori-prioritas-pppk');
</script>
@endsection