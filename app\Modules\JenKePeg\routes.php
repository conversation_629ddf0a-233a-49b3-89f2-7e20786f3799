<?php
namespace App\Modules\JenKePeg;

use App\Modules\JenKePeg\Controllers\JenKePegController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenkepeg')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenKePegController::class, 'index'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/datatable', [JenKePegController::class, 'datatable']); 
    Route::get('/create', [JenKePegController::class, 'create'])->middleware('authorize:create-jenkepeg');
    Route::post('/', [JenKePegController::class, 'store'])->middleware('authorize:create-jenkepeg');
    Route::get('/{jenkepeg_id}', [JenKePegController::class, 'show'])->middleware('authorize:read-jenkepeg');
    Route::get('/{jenkepeg_id}/detail', [JenKePegController::class, 'detail'])->middleware('authorize:read-jenkepeg');
    Route::get('/{jenkepeg_id}/edit', [JenKePegController::class, 'edit'])->middleware('authorize:update-jenkepeg');
    Route::put('/{jenkepeg_id}', [JenKePegController::class, 'update'])->middleware('authorize:update-jenkepeg');
    Route::patch('/{jenkepeg_id}', [JenKePegController::class, 'update'])->middleware('authorize:update-jenkepeg');
    Route::delete('/{jenkepeg_id}', [JenKePegController::class, 'destroy'])->middleware('authorize:delete-jenkepeg');
});