<?php

namespace App\Modules\Fungsional\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FungsionalController extends Controller
{
    public function nama_jabatan(Request $request)
    {
        $data = DB::table('a_jabfung')->select('jabfung2','tingkat')->groupBy('jabfung2','tingkat')->orderBy('jabfung2', 'asc');
        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function tingkat_jabatan(Request $request)
    {
        $idtingkat = $request->input('idtingkat');
        $data = DB::table('a_jabfung')->where('tingkat','=',$idtingkat)
            ->get();
        return JsonResponseHandler::setResult($data)->send();
    }
}
