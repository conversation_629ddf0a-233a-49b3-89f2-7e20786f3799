<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\UnitKerja\Models\UnitKerjaModel;
use App\Modules\DiklatStruktural\Models\DiklatStruktural;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\ExecutiveSummary\Repositories\StatistikASNRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;

class StatistikASNController extends Controller
{
    public function handlePrintStatistik(Request $request)
    {
        $kategori = (int) $request->input('id_kategori');
        
        switch ($kategori) {
            case 1:
                return $this->printTablePendidikanFormal($request);
            case 2:
                return $this->printTableUnitKerjaPendidikanFormal($request);
            case 3:
                return $this->printTableUnitKerjaGolongan($request);
            case 4:
                return $this->printTableJenisKelaminGolongan($request);
        }
    }

    public function datatablePendidikanFormal(Request $request)
    {
        $data = StatistikASNRepository::queryTablePendidikanFormal($request);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function printTablePendidikanFormal(Request $request)
    {
        $data = StatistikASNRepository::queryTablePendidikanFormal($request);
        $sum = StatistikASNRepository::sumGolonganRow($data);

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.pdf-statistik-pendidikan-formal', compact('data', 'sum'))
                    ->setOption('orientation', 'Landscape');

        return $pdf->download('Statistik ASN - Pendidikan Formal.pdf');
    }

    public function grafikPendidikanFormalGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftjoin('r_pendidikan_formal as a', function ($join) {
                $join
                    ->on('b.id', '=', 'a.id_pegawai')
                    ->where('a.isakhir', '=', 1);
            })
            ->leftjoin('a_tkpendid as d', 'a.id_jenjang', '=', 'd.idtkpendid')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id_jenjang',
                DB::raw("IF(d.tkpendid IS NULL, 'DATA TIDAK LENGKAP', d.tkpendid) as 'pendidikan_formal'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->groupBy('a.id_jenjang', 'd.tkpendid')
            ->orderBy('a.id_jenjang', 'asc');


        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja]);
        }

        if ($id_sub_unit != null && $id_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit]);
        }

        if ($id_sub_sub_unit != null && $id_sub_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit])
                ->where('b.id_sub_sub_unit', [$pecah_sub_sub_unit])
                ->where('b.id_sub_sub_sub_unit', [$pecah_sub_sub_sub_unit]);
        }


        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }
        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPendidikanFormalGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftjoin('r_pendidikan_formal as a', function ($join) {
                $join
                    ->on('b.id', '=', 'a.id_pegawai')
                    ->where('a.isakhir', '=', 1);
            })
            ->leftjoin('a_tkpendid as d', 'a.id_jenjang', '=', 'd.idtkpendid')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id_jenjang',
                // 'd.tkpendid as pendidikan_formal',
                DB::raw("IF(d.tkpendid IS NULL, 'DATA TIDAK LENGKAP', d.tkpendid) as 'pendidikan_formal'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->groupBy('a.id_jenjang')
            ->orderBy('a.id_jenjang', 'asc');


        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja]);
        }

        if ($id_sub_unit != null && $id_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit]);
        }

        if ($id_sub_sub_unit != null && $id_sub_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit])
                ->where('b.id_sub_sub_unit', [$pecah_sub_sub_unit])
                ->where('b.id_sub_sub_sub_unit', [$pecah_sub_sub_sub_unit]);
        }


        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }
        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPendidikanFormalGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftjoin('r_pendidikan_formal as a', function ($join) {
                $join
                    ->on('b.id', '=', 'a.id_pegawai')
                    ->where('a.isakhir', '=', 1);
            })
            ->leftjoin('a_tkpendid as d', 'a.id_jenjang', '=', 'd.idtkpendid')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id_jenjang',
                // 'd.tkpendid as pendidikan_formal',
                DB::raw("IF(d.tkpendid IS NULL, 'DATA TIDAK LENGKAP', d.tkpendid) as 'pendidikan_formal'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->groupBy('a.id_jenjang')
            ->orderBy('a.id_jenjang', 'asc');


        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja]);
        }

        if ($id_sub_unit != null && $id_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit]);
        }

        if ($id_sub_sub_unit != null && $id_sub_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit])
                ->where('b.id_sub_sub_unit', [$pecah_sub_sub_unit])
                ->where('b.id_sub_sub_sub_unit', [$pecah_sub_sub_sub_unit]);
        }


        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }
        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPendidikanFormalGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftjoin('r_pendidikan_formal as a', function ($join) {
                $join
                    ->on('b.id', '=', 'a.id_pegawai')
                    ->where('a.isakhir', '=', 1);
            })
            ->leftjoin('a_tkpendid as d', 'a.id_jenjang', '=', 'd.idtkpendid')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id_jenjang',
                // 'd.tkpendid as pendidikan_formal',
                DB::raw("IF(d.tkpendid IS NULL, 'DATA TIDAK LENGKAP', d.tkpendid) as 'pendidikan_formal'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.id_jenjang')
            ->orderBy('a.id_jenjang', 'asc');


        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja]);
        }

        if ($id_sub_unit != null && $id_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit]);
        }

        if ($id_sub_sub_unit != null && $id_sub_sub_unit != "") {
            $data = $data->where('b.id_unit_kerja', [$id_unit_kerja])
                ->where('b.id_induk_upt', [$pecah_induk_upt])
                ->where('b.id_sub_unit', [$pecah_sub_unit])
                ->where('b.id_sub_sub_unit', [$pecah_sub_sub_unit])
                ->where('b.id_sub_sub_sub_unit', [$pecah_sub_sub_sub_unit]);
        }


        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }
        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfPendidikanFormal(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);    

        $data = StatistikASNRepository::getPDFQuery($request)
            ->whereRelation('riwayatJabatan', 'isakhir', 1)
            ->when($param['pendidikan_id'], function ($query) use ($param) {
                $query->whereHas('riwayatPendidikan', function ($query) use ($param) {
                    $query->where('isakhir', 1)
                        ->where('id_jenjang', $param['pendidikan_id']);
                });
            })
            ->when(!$param['pendidikan_id'] && $param['is_doesnt_have_education'] == 1, function ($query) { // Data tidak lengkap
                $query->whereDoesntHave('riwayatPendidikan', function ($query) {
                    $query->where('isakhir', 1);
                });
            })
            ->golongan($param['golongan_id'])
            ->get();

        gc_collect_cycles();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                    ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableUnitKerjaPendidikanFormal(Request $request)
    {
        $data = StatistikASNRepository::queryUnitKerjaPendidikanFormal($request);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function printTableUnitKerjaPendidikanFormal(Request $request)
    {
        $data = StatistikASNRepository::queryUnitKerjaPendidikanFormal($request);
        $sum = StatistikASNRepository::sumPendidikanRow($data);

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.pdf-statistik-unit-kerja-pendidikan-formal', compact('data', 'sum'))
                ->setOption('orientation', 'Landscape');

        return $pdf->download('Statistik ASN - Unit Kerja dan Pendidikan Formal.pdf');
    }   

    public function grafikUnitKerjaPendidikanFormal(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            // ->leftjoin('tb_01 as b', 'a.idskpd', '=', 'b.id_unit_kerja')
            ->select(
                'a.idskpd',
                'a.skpd as unit_kerja',
                DB::raw("SUM(IF(c.id_jenjang='10',1,0)) AS 'SD'"),
                DB::raw("SUM(IF(c.id_jenjang='20',1,0)) AS 'SLTP'"),
                DB::raw("SUM(IF(c.id_jenjang='30',1,0)) AS 'SLTA'"),
                DB::raw("SUM(IF(c.id_jenjang='41',1,0)) AS 'DI'"),
                DB::raw("SUM(IF(c.id_jenjang='42',1,0)) AS 'DII'"),
                DB::raw("SUM(IF(c.id_jenjang='43',1,0)) AS 'DIII'"),
                DB::raw("SUM(IF(c.id_jenjang='44',1,0)) AS 'DIV'"),
                DB::raw("SUM(IF(c.id_jenjang='50',1,0)) AS 'SARMUD_NON_AK'"),
                DB::raw("SUM(IF(c.id_jenjang='60',1,0)) AS 'SARMUD'"),
                DB::raw("SUM(IF(c.id_jenjang='70',1,0)) AS 'S1'"),
                DB::raw("SUM(IF(c.id_jenjang='80',1,0)) AS 'S2'"),
                DB::raw("SUM(IF(c.id_jenjang='90',1,0)) AS 'S3'")
            );

        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data->leftjoin('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->leftjoin('r_pendidikan_formal as c', 'b.id', '=', 'c.id_pegawai')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereRaw('c.isakhir=1')
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }
        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfUnitKerjaPendidikanFormal(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        // dd($param);
        $data = StatistikASNRepository::getPDFQuery($request)
                ->whereHas('riwayatPendidikan', function ($query) use ($param) {
                    $query->where('isakhir', 1)
                        ->when($param['pendidikan_id'], function ($query) use ($param) {
                            $query->where('id_jenjang', $param['pendidikan_id']);
                        });
                })
                ->whereHas('riwayatJabatan', function ($query) use ($param) {
                    $query->where('isakhir', 1)
                        ->when($param['id_unit_kerja'], function ($query) use ($param) {
                            $query->where('id_unit_kerja', $param['id_unit_kerja']);
                        })
                        ->when($param['id_sub_unit'], function ($query) use ($param) {
                            $query->where('id_sub_unit', $param['id_sub_unit']);
                        });
                })
                ->get();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                    ->setOption('footer-right', '[page]');
        
        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableUnitKerjaGolongan(Request $request)
    {
        $data = StatistikASNRepository::queryUnitKerjaGolongan($request);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function printTableUnitKerjaGolongan(Request $request)
    {
        $data = StatistikASNRepository::queryUnitKerjaGolongan($request);
        $sum = StatistikASNRepository::sumGolonganRow($data);

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.pdf-statistik-unit-kerja-golongan', compact('data', 'sum'))
                ->setOption('orientation', 'Landscape');

        return $pdf->download('Statistik ASN - Unit Kerja dan Golongan.pdf');
    }

    public function grafikUnitKerjaGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            // ->leftjoin('tb_01 as b', 'a.idskpd', '=', 'b.id_unit_kerja')
            ->select(
                'b.id_unit_kerja',
                'a.skpd as unit_kerja',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
            );

        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data->leftjoin('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->join('r_kepangkatan as c', function ($join) {
            $join
                ->on('b.id', '=', 'c.id_pegawai')
                ->where('c.isakhir', '=', 1);
        })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikUnitKerjaGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            // ->leftjoin('tb_01 as b', 'a.idskpd', '=', 'b.id_unit_kerja')
            ->select(
                'b.id_unit_kerja',
                'a.skpd as unit_kerja',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
            );

        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data->leftjoin('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->join('r_kepangkatan as c', function ($join) {
            $join
                ->on('b.id', '=', 'c.id_pegawai')
                ->where('c.isakhir', '=', 1);
        })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikUnitKerjaGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            // ->leftjoin('tb_01 as b', 'a.idskpd', '=', 'b.id_unit_kerja')
            ->select(
                'b.id_unit_kerja',
                'a.skpd as unit_kerja',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
            );

        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data->leftjoin('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->join('r_kepangkatan as c', function ($join) {
            $join
                ->on('b.id', '=', 'c.id_pegawai')
                ->where('c.isakhir', '=', 1);
        })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikUnitKerjaGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            // ->leftjoin('tb_01 as b', 'a.idskpd', '=', 'b.id_unit_kerja')
            ->select(
                'b.id_unit_kerja',
                'a.skpd as unit_kerja',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            );

        if ($id_unit_kerja != null && $id_unit_kerja != "") {
            $data->leftjoin('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->join('r_kepangkatan as c', function ($join) {
            $join
                ->on('b.id', '=', 'c.id_pegawai')
                ->where('c.isakhir', '=', 1);
        })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfUnitKerjaGolongan(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        // dd($param);
        
        $data = StatistikASNRepository::getPDFQuery($request)
                ->golongan($param['golongan_id'])
                ->get();        
                
        gc_collect_cycles();
        // dd($data->toArray());
        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                    ->setOption('footer-right', '[page]');
                
        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableDiklat(Request $request)
    { 
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('eselon as a', 'r_jabatan.id_eselon', '=', 'a.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as eselon',
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        // $param = StatistikASNRepository::handleQueryParam($request);

        // $data = DiklatStruktural::when(!empty($param['id_unit_kerja']), function ($query) use ($param) {
        //         $query->where('id_unit_kerja', $param['id_unit_kerja']);
        //     })
        //     ->when('id_induk_upt' != '00', function ($query) use ($param) {
        //         $query->when(!empty($param['pecah_induk_upt']), function ($query) use ($param) {
        //             $query->where('id_induk_upt', $param['pecah_induk_upt']);
        //         });
        //     })
        //     ->when(!empty($param['pecah_sub_unit']), function ($query) use ($param) {
        //         $query->where('id_sub_unit', $param['pecah_sub_unit']);
        //     })
        //     ->when(!empty($param['id_sub_sub_unit']), function ($query) use ($param) {
        //         $query->where('id_sub_sub_unit', $param['id_sub_sub_unit']);
        //     })
        //     ->when(!empty($param['id_sub_sub_sub_unit']), function ($query) use ($param) {
        //         $query->where('id_sub_sub_sub_unit', $param['id_sub_sub_sub_unit']);
        //     })
        //     ->get();

        // $data = DiklatStruktural::with
        

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableEselonGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('eselon as a', 'r_jabatan.id_eselon', '=', 'a.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as eselon',
                DB::raw('IF(a.name is null, "Data Tidak Lengkap", a.name) as eselon'),
                DB::raw('IF(a.name is null, 1, 0) as is_doesnt_have_eselon'),
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikEselonGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('eselon as a', 'r_jabatan.id_eselon', '=', 'a.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as eselon',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikEselonGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('eselon as a', 'r_jabatan.id_eselon', '=', 'a.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as eselon',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikEselonGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('eselon as a', 'r_jabatan.id_eselon', '=', 'a.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as eselon',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikEselonGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('eselon as a', 'r_jabatan.id_eselon', '=', 'a.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as eselon',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfEselonGolongan(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);

        $data = StatistikASNRepository::getPDFQuery($request)
            ->whereHas('riwayatJabatan', function ($query) use ($param) {
                $query->when($param['eselon_id'], function ($query) use ($param) {
                    $query->where('isakhir', 1)
                        ->where('id_eselon', $param['eselon_id']);
                    });
            })
            ->when(!$param['eselon_id'] && $param['is_doesnt_have_eselon'] == 1, function ($query) {
                $query->whereDoesntHave('riwayatJabatan', function ($query) {
                    $query->where('isakhir', 1);
                });
            })
            ->golongan($param['golongan_id'])
            ->get();
        // return JsonResponseHandler::setResult($data)->send();
        gc_collect_cycles();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                    ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableJenisKelaminEselon(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->leftJoin('r_jabatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftjoin('eselon as d', 'c.id_eselon', '=', 'd.id')
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(if(c.id_eselon!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_eselon='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_eselon='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_eselon='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_eselon='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_eselon='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_eselon='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_eselon='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_eselon='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_eselon='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_eselon='51',1,0)) AS 'Va'"),
                DB::raw("SUM(IF(c.id_eselon='52',1,0)) AS 'Vb'"),
                DB::raw("SUM(IF(c.id_eselon='60',1,0)) AS 'non'"),
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('c.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminEselonI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->leftJoin('r_jabatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftjoin('eselon as d', 'c.id_eselon', '=', 'd.id')
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_eselon='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_eselon='12',1,0)) AS 'Ib'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('c.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminEselonII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->leftJoin('r_jabatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftjoin('eselon as d', 'c.id_eselon', '=', 'd.id')
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_eselon='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_eselon='22',1,0)) AS 'IIb'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('c.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminEselonIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->leftJoin('r_jabatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftjoin('eselon as d', 'c.id_eselon', '=', 'd.id')
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_eselon='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_eselon='32',1,0)) AS 'IIIb'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('c.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminEselonIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->leftJoin('r_jabatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftjoin('eselon as d', 'c.id_eselon', '=', 'd.id')
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_eselon='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_eselon='42',1,0)) AS 'IVb'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('c.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminEselonV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->leftJoin('r_jabatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftjoin('eselon as d', 'c.id_eselon', '=', 'd.id')
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_eselon='51',1,0)) AS 'Va'"),
                DB::raw("SUM(IF(c.id_eselon='52',1,0)) AS 'Vb'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('c.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfJenisKelaminEselon(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        // dd($param['is_empty']);
        
        $data = StatistikASNRepository::getPDFQuery($request)
                ->when($param['gender_id'], function ($query) use ($param) {
                    $query->where('tb_01.jenis_kelamin', $param['gender_id']);
                })
                ->when($param['eselon_id'], function ($query) use ($param) {
                    $query->whereHas('riwayatJabatan', function ($query) use ($param) {
                        $query->where('isakhir', 1)
                            ->where('id_eselon', $param['eselon_id']);
                    });
                })
                ->when(!$param['eselon_id'], function ($query) {
                    // dd($query);
                    $query->whereDoesntHave('riwayatJabatan', function ($query) {
                        $query->whereNull('id_eselon')
                            ->orWhere('id_eselon', '');
                    });
                })
                ->get();

        gc_collect_cycles();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                    ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableJenisKelaminGolongan(Request $request)
    {
        $data = StatistikASNRepository::queryJenisKelaminGolongan($request);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function printTableJenisKelaminGolongan(Request $request)
    {
        $data = StatistikASNRepository::queryJenisKelaminGolongan($request);
        $sum = StatistikASNRepository::sumJenisKelaminRow($data);

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.pdf-statistik-jenis-kelamin-golongan', compact('data', 'sum'))
                ->setOption('orientation', 'Landscape');

        return $pdf->download('Statistik ASN - Jenis Kelamin dan Golongan.pdf');
    }

    public function grafikJenisKelaminGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisKelaminGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('gender as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.jenis_kelamin')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as jenis_kelamin',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfJenisKelaminGolongan(Request $request) 
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        // dd($param);
        $data = StatistikASNRepository::getPDFQuery($request)
                ->whereHas('riwayatPangkat', function ($query) use ($param) {
                    $query->where('isakhir', 1)
                        ->when($param['golongan_id'], function ($query) use ($param) {
                            $query->where('id_kepangkatan', $param['golongan_id']);
                    });
                })
                ->when(!empty($param['gender_id']), function ($query) use ($param) {
                    $query->where('tb_01.jenis_kelamin', $param['gender_id']);
                })
                ->get();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableJenisJabatanGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('a_jenjab as a', 'a.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjenjab',
                'a.jenjab as JenisJabatan',
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->groupBy('a.idjenjab')
            ->orderBy('a.idjenjab', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisJabatanGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('a_jenjab as a', 'a.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjenjab',
                'a.jenjab as JenisJabatan',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->groupBy('a.idjenjab')
            ->orderBy('a.idjenjab', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisJabatanGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('a_jenjab as a', 'a.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjenjab',
                'a.jenjab as JenisJabatan',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->groupBy('a.idjenjab')
            ->orderBy('a.idjenjab', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisJabatanGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('a_jenjab as a', 'a.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjenjab',
                'a.jenjab as JenisJabatan',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->groupBy('a.idjenjab')
            ->orderBy('a.idjenjab', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJenisJabatanGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('a_jenjab as a', 'a.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjenjab',
                'a.jenjab as JenisJabatan',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idjenjab')
            ->orderBy('a.idjenjab', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfJenisJabatanGolongan(Request $request) 
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        // dd($param);
        $data = StatistikASNRepository::getPDFQuery($request)
                ->whereHas('riwayatPangkat', function ($query) use ($param) {
                    $query->where('isakhir', 1)
                        ->when($param['golongan_id'], function ($query) use ($param) {
                            $query->where('id_kepangkatan', $param['golongan_id']);
                    });
                })
                ->get();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data]);
        return $pdf->download('executiveSummary.pdf');
    }


    public function datatableKedudukanPegawai(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('jenkedudupeg as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.kedudukan_pegawai')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as kedudukan_pegawai',
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKedudukanPegawaiGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('jenkedudupeg as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.kedudukan_pegawai')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as kedudukan_pegawai',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKedudukanPegawaiGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('jenkedudupeg as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.kedudukan_pegawai')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as kedudukan_pegawai',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKedudukanPegawaiGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('jenkedudupeg as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.kedudukan_pegawai')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as kedudukan_pegawai',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKedudukanPegawaiGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('jenkedudupeg as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.kedudukan_pegawai')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.name as kedudukan_pegawai',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.id')
            ->orderBy('a.id', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfKedudukanPegawai(Request $request) 
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        // dd($param);
        $data = StatistikASNRepository::getPDFQuery($request)
                ->whereHas('riwayatPangkat', function ($query) use ($param) {
                    $query->where('isakhir', 1)
                        ->when($param['golongan_id'], function ($query) use ($param) {
                            $query->where('id_kepangkatan', $param['golongan_id']);
                    });
                })
                ->when(!empty($param['id_jenkedudupeg']), function ($query) use ($param) {
                    $query->whereRelation('pendudupeg', 'id', $param['id_jenkedudupeg']);
                })
                ->get();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableAgamaGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('agama as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.agama')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.agama',
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();

        return JsonResponseHandler::setResult($data)->send();
    }

    public function pdfAgamaGolongan(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
    
        $data = StatistikASNRepository::getPDFQuery($request)
                ->where('tb_01.agama', $param['agama_id'])
                ->golongan($param['golongan_id'])
                ->get();

        gc_collect_cycles();
        
        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
    }

    public function grafikAgamaGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('agama as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.agama')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.agama',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();

        return JsonResponseHandler::setResult($data)->send();
    }

    public function grafikAgamaGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('agama as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.agama')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.agama',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();

        return JsonResponseHandler::setResult($data)->send();
    }

    public function grafikAgamaGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('agama as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.agama')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.agama',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();

        return JsonResponseHandler::setResult($data)->send();
    }

    public function grafikAgamaGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('agama as a')
            ->leftjoin('tb_01 as b', 'a.id', '=', 'b.agama')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.id',
                'a.agama',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->orderBy('a.id', 'asc')
            ->groupBy('a.id');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();

        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatableUsiaGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $total_records = DB::table('tb_01')
                        ->whereIn('status_pegawai', [2, 3])
                        ->whereNotIn('kedudukan_pegawai', [99, 21])
                        ->count();


        $data = DB::table('tb_01 as b')
            ->leftJoin('employee_status', 'b.status_pegawai', '=', 'employee_status.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->selectRaw("
                employee_status.id as id,
                `employee_status`.`name` AS `status_pegawai`,
                SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 10 YEAR), 
                    1, 0)
                ) AS 'usia_10_30',
                SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 45 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 31 YEAR), 
                    1, 0)
                ) AS 'usia_31_45',
                SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 46 YEAR), 
                    1, 0)
                ) AS 'usia_46_55',
                SUM(IF(
                    b.tanggal_lahir < DATE_SUB(CURDATE(), INTERVAL 55 YEAR),
                    1, 0)
                ) AS 'usia_56',
                COUNT(*) AS 'total',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 10 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_10_30',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 45 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 31 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_31_45',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 46 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_46_55',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 46 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_46_55',
                ROUND((SUM(IF(
                    b.tanggal_lahir < DATE_SUB(CURDATE(), INTERVAL 55 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2) AS 'persen_56',
                ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
            ")
            ->whereIn('b.status_pegawai', [2, 3])
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('status_pegawai');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikUsiaGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $total_records = DB::table('tb_01')
                        // ->whereIn('status_pegawai', [2, 3])
                        ->whereNotIn('kedudukan_pegawai', [99, 21]) // 99 = Pensiun, 21 = Keluar
                        ->count();

        $data = DB::table('tb_01 as b')
            ->leftJoin('employee_status', 'b.status_pegawai', '=', 'employee_status.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->selectRaw("
                `employee_status`.`name` AS `status_pegawai`,
                SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 10 YEAR), 
                    1, 0)
                ) AS 'usia_10_30',
                SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 45 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 31 YEAR), 
                    1, 0)
                ) AS 'usia_31_45',
                SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 46 YEAR), 
                    1, 0)
                ) AS 'usia_46_55',
                SUM(IF(
                    b.tanggal_lahir < DATE_SUB(CURDATE(), INTERVAL 55 YEAR),
                    1, 0)
                ) AS 'usia_56',
                COUNT(*) AS 'total',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 10 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_10_30',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 45 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 31 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_31_45',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 46 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_46_55',
                ROUND((SUM(IF(
                    b.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 46 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2
                ) AS 'persen_46_55',
                ROUND((SUM(IF(
                    b.tanggal_lahir < DATE_SUB(CURDATE(), INTERVAL 55 YEAR),
                    1, 0))
                    / {$total_records}) * 100, 2) AS 'persen_56',
                ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
            ")
            ->whereIn('b.status_pegawai', [2, 3])
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('status_pegawai');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfUsiaGolongan(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        
        $data = StatistikASNRepository::getPDFQuery($request)
            ->when($param['age_range'] == 1, function ($query) {
                $query->whereRaw('
                    tb_01.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 10 YEAR)
                ');
            })
            ->when($param['age_range'] == 2, function ($query) {
                $query->whereRaw('
                    tb_01.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 45 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 31 YEAR)
                ');
            })
            ->when($param['age_range'] == 3, function ($query) {
                $query->whereRaw('
                    tb_01.tanggal_lahir BETWEEN DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                    AND DATE_SUB(CURDATE(), INTERVAL 46 YEAR)
                ');
            })
            ->when($param['age_range'] == 4, function ($query) {
                $query->whereRaw('
                    tb_01.tanggal_lahir < DATE_SUB(CURDATE(), INTERVAL 55 YEAR)
                ');
            })
            ->when(!empty($param['status_pegawai']), function ($query) use ($param) {
                $query->where('tb_01.status_pegawai', $param['status_pegawai']);
            })
            ->get();
        
            gc_collect_cycles();
        
        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
        // dd(now()->subYears(20));
    }

    public function datatableStatusPerkawinan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_stskawin as a')
            ->leftjoin('tb_01 as b', 'a.idstskawin', '=', 'b.status_pernikahan')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.idstskawin',
                'a.stskawin as status_pernikahan',
                DB::raw("SUM(IF(c.id_kepangkatan!='',1,0)) AS 'total'"),
                DB::raw("SUM(IF(c.id_kepangkatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idstskawin')
            ->orderBy('status_pernikahan', 'asc');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikStatusPerkawinanGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_stskawin as a')
            ->leftjoin('tb_01 as b', 'a.idstskawin', '=', 'b.status_pernikahan')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.idstskawin',
                'a.stskawin as status_pernikahan',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->groupBy('a.idstskawin')
            ->orderBy('status_pernikahan', 'asc');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikStatusPerkawinanGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_stskawin as a')
            ->leftjoin('tb_01 as b', 'a.idstskawin', '=', 'b.status_pernikahan')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.idstskawin',
                'a.stskawin as status_pernikahan',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->groupBy('a.idstskawin')
            ->orderBy('status_pernikahan', 'asc');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikStatusPerkawinanGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_stskawin as a')
            ->leftjoin('tb_01 as b', 'a.idstskawin', '=', 'b.status_pernikahan')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.idstskawin',
                'a.stskawin as status_pernikahan',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->groupBy('a.idstskawin')
            ->orderBy('status_pernikahan', 'asc');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikStatusPerkawinanGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_stskawin as a')
            ->leftjoin('tb_01 as b', 'a.idstskawin', '=', 'b.status_pernikahan')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.idstskawin',
                'a.stskawin as status_pernikahan',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idstskawin')
            ->orderBy('status_pernikahan', 'asc');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfStatusPerkawinan(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);

        $data = StatistikASNRepository::getPDFQuery($request)
            ->golongan($param['golongan_id'])
            ->when(!empty($param['status_kawin']), function ($query) use ($param) {
                // dd($query);
                $query->where('tb_01.status_pernikahan', $param['status_kawin']);
            })
            ->get();
            // dd($data->toArray());
                
        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableGuruGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftjoin('a_skpd as a', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a.id_sub_sub_sub_unit');
            })
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 2)
                    ->where('a_jabfung.profesi', '=', 1);
            })
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.issek',
                DB::raw("IF(a.issek = 4, 'SMA', IF(a.issek = 5, 'SMK', '-')) AS guru"),
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->where('a.flag', '=', 1)
            ->groupBy('a.issek')
            ->orderBy('a.issek', 'asc');

        // if ($id_unit_kerja != null && $id_unit_kerja != "") {
        //     $data = $data->where('a.idskpd', [$id_unit_kerja]);
        // }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }   

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikGuruGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftjoin('a_skpd as a', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a.id_sub_sub_sub_unit');
            })
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 2)
                    ->where('a_jabfung.profesi', '=', 1);
            })
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.issek',
                DB::raw("IF(a.issek = 4, 'SMA', IF(a.issek = 5, 'SMK', '-')) AS guru"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->where('a.flag', '=', 1)
            ->groupBy('a.issek')
            ->orderBy('a.issek', 'asc');

        // if ($id_unit_kerja != null && $id_unit_kerja != "") {
        //     $data = $data->where('a.idskpd', [$id_unit_kerja]);
        // }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikGuruGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftjoin('a_skpd as a', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a.id_sub_sub_sub_unit');
            })
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 2)
                    ->where('a_jabfung.profesi', '=', 1);
            })
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.issek',
                DB::raw("IF(a.issek = 4, 'SMA', IF(a.issek = 5, 'SMK', '-')) AS guru"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->where('a.flag', '=', 1)
            ->groupBy('a.issek')
            ->orderBy('a.issek', 'asc');

        // if ($id_unit_kerja != null && $id_unit_kerja != "") {
        //     $data = $data->where('a.idskpd', [$id_unit_kerja]);
        // }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikGuruGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftjoin('a_skpd as a', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a.id_sub_sub_sub_unit');
            })
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 2)
                    ->where('a_jabfung.profesi', '=', 1);
            })
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.issek',
                DB::raw("IF(a.issek = 4, 'SMA', IF(a.issek = 5, 'SMK', '-')) AS guru"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->where('a.flag', '=', 1)
            ->groupBy('a.issek')
            ->orderBy('a.issek', 'asc');

        // if ($id_unit_kerja != null && $id_unit_kerja != "") {
        //     $data = $data->where('a.idskpd', [$id_unit_kerja]);
        // }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikGuruGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->leftjoin('a_skpd as a', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a.id_sub_sub_sub_unit');
            })
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 2)
                    ->where('a_jabfung.profesi', '=', 1);
            })
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.issek',
                DB::raw("IF(a.issek = 4, 'SMA', IF(a.issek = 5, 'SMK', '-')) AS guru"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->where('a.flag', '=', 1)
            ->groupBy('a.issek')
            ->orderBy('a.issek', 'asc');

        // if ($id_unit_kerja != null && $id_unit_kerja != "") {
        //     $data = $data->where('a.idskpd', [$id_unit_kerja]);
        // }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfGuruGolongan(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        $is_skpd = ($param['issek'] != '0') ? true : false;

        $data = StatistikASNRepository::getPDFQuery($request, $is_skpd)
            ->whereHas('riwayatJabatan', function ($query) use ($param) {
                $query->where('isakhir', 1)
                    ->whereHas('jabatanTertentu', function ($query) use ($param) {
                        $query->when(!empty($param['issek']), function ($query) {
                                $query->where('idrumpun', 2)
                                    ->where('profesi', 1);
                        });
                    });
            })
            ->when($param['issek'] == '0' || !empty($param['issek']), function ($query) use ($param) {
                $query->where('a_skpd.issek', $param['issek']);
            })
            ->golongan($param['golongan_id'])
            ->get();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
                ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableGuruUnitKerja(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            ->leftJoin('tb_01 as b', function ($join) {
                $join->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit');
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 2);
            })
            ->select(
                'a.skpd as unit_kerja',
                'a.id_unit_kerja',
                'a.id_induk_upt',
                'a.id_sub_unit',
                'a.id_sub_sub_unit',
                'a.id_sub_sub_sub_unit',
                DB::raw("SUM(if(a.issek!='',1,0)) as 'total'"),
                DB::raw("SUM(IF(a.issek='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(a.issek='4',1,0)) AS 'sma'"),
                DB::raw("SUM(IF(a.issek='5',1,0)) AS 'smk'"),
            )
            ->where('a_jabfung.profesi', '=', 1)
            ->orderBy('a.idskpd', 'asc')
            ->groupBy('a.idskpd');

        // if ($id_unit_kerja != null && $id_unit_kerja != "") {
        //     $data = $data->where('a.idskpd', [$id_unit_kerja]);
        // }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikGuruUnitKerja(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('a_skpd as a')
            ->leftJoin('tb_01 as b', function ($join) {
                $join->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit');
            })
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 2);
            })
            ->select(
                'a.skpd as unit_kerja',
                DB::raw("SUM(IF(a.issek='4',1,0)) AS 'sma'"),
                DB::raw("SUM(IF(a.issek='5',1,0)) AS 'smk'"),
            )
            ->where('a_jabfung.profesi', '=', 1)
            ->orderBy('a.idskpd', 'asc')
            ->groupBy('a.idskpd');

        // if ($id_unit_kerja != null && $id_unit_kerja != "") {
        //     $data = $data->where('a.idskpd', [$id_unit_kerja]);
        // }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfGuruUnitKerja(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
        // dd($param);
        $data = StatistikASNRepository::getPDFQuery($request, true)
            ->whereHas('riwayatJabatan', function ($query) use ($param) {
                $query->where('isakhir', 1)
                    ->whereHas('jabatanTertentu', function ($query) use ($param) {
                        // $query->when(!empty($param['issek']), function ($query) {
                                $query->where('idrumpun', 2)
                                    ->where('profesi', 1);
                        // });
                    });
            })
            ->when(!empty($param['issek']), function ($query) use ($param) {
                $query->where('a_skpd.issek', $param['issek']);
            })
            ->get();

        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data])
            ->setOption('footer-right', '[page]');

        return $pdf->download('executiveSummary.pdf');
    }

    public function datatablePelaksanaGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 3)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('a_jabfungum as a', 'r_jabatan.id_jabatan', '=', 'a.idjabfungum')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjabfungum',
                'a.jabfungum as pelaksana',
                DB::raw("SUM(IF(c.id_kepangkatan!='',1,0)) AS 'total'"),
                DB::raw("SUM(IF(c.id_kepangkatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idjabfungum', 'pelaksana')
            ->orderBy('pelaksana', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPelaksanaGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 3)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('a_jabfungum as a', 'r_jabatan.id_jabatan', '=', 'a.idjabfungum')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjabfungum',
                'a.jabfungum as pelaksana',
                DB::raw("SUM(IF(c.id_kepangkatan!='',1,0)) AS 'total'"),
                DB::raw("SUM(IF(c.id_kepangkatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idjabfungum', 'pelaksana')
            ->orderBy('pelaksana', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPelaksanaGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 3)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('a_jabfungum as a', 'r_jabatan.id_jabatan', '=', 'a.idjabfungum')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjabfungum',
                'a.jabfungum as pelaksana',
                DB::raw("SUM(IF(c.id_kepangkatan!='',1,0)) AS 'total'"),
                DB::raw("SUM(IF(c.id_kepangkatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idjabfungum', 'pelaksana')
            ->orderBy('pelaksana', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPelaksanaGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 3)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('a_jabfungum as a', 'r_jabatan.id_jabatan', '=', 'a.idjabfungum')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjabfungum',
                'a.jabfungum as pelaksana',
                DB::raw("SUM(IF(c.id_kepangkatan!='',1,0)) AS 'total'"),
                DB::raw("SUM(IF(c.id_kepangkatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idjabfungum', 'pelaksana')
            ->orderBy('pelaksana', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPelaksanaGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.id_jenis_jabatan', '=', 3)
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('a_jabfungum as a', 'r_jabatan.id_jabatan', '=', 'a.idjabfungum')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a.idjabfungum',
                'a.jabfungum as pelaksana',
                DB::raw("SUM(IF(c.id_kepangkatan!='',1,0)) AS 'total'"),
                DB::raw("SUM(IF(c.id_kepangkatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->groupBy('a.idjabfungum', 'pelaksana')
            ->orderBy('pelaksana', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableKesehatanGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as kesehatan',
                'a_jabfung.tingkat',
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->where('a_jabfung.idrumpun', '=', 1)
            ->orderBy('kesehatan', 'asc')
            ->groupBy('a_jabfung.tingkat', 'kesehatan');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKesehatanGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as kesehatan',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->where('a_jabfung.idrumpun', '=', 1)
            ->orderBy('kesehatan', 'asc')
            ->groupBy('a_jabfung.tingkat', 'kesehatan');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKesehatanGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as kesehatan',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->where('a_jabfung.idrumpun', '=', 1)
            ->orderBy('kesehatan', 'asc')
            ->groupBy('a_jabfung.tingkat', 'kesehatan');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKesehatanGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as kesehatan',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->where('a_jabfung.idrumpun', '=', 1)
            ->orderBy('kesehatan', 'asc')
            ->groupBy('a_jabfung.tingkat', 'kesehatan');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikKesehatanGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as kesehatan',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'")
            )
            ->where('a_jabfung.idrumpun', '=', 1)
            ->orderBy('kesehatan', 'asc')
            ->groupBy('a_jabfung.tingkat', 'kesehatan');


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableTeknisGolongan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as teknis',
                'a_jabfung.tingkat',
                DB::raw("SUM(if(c.id_kepangkatan!='',1,0)) as 'total'"),
                DB::raw("SUM(if(c.id_kepangkatan='',1,0)) as 'kosong'"),
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'"),
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'"),
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->where('a_jabfung.idrumpun', '=', 3)
            ->orderBy('teknis', 'asc')
            ->groupBy('a_jabfung.tingkat', 'teknis');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikTeknisGolonganI(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as teknis',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='11',1,0)) AS 'Ia'"),
                DB::raw("SUM(IF(c.id_kepangkatan='12',1,0)) AS 'Ib'"),
                DB::raw("SUM(IF(c.id_kepangkatan='13',1,0)) AS 'Ic'"),
                DB::raw("SUM(IF(c.id_kepangkatan='14',1,0)) AS 'Id'")
            )
            ->where('a_jabfung.idrumpun', '=', 3)
            ->orderBy('teknis', 'asc')
            ->groupBy('a_jabfung.tingkat', 'teknis');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikTeknisGolonganII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as teknis',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='21',1,0)) AS 'IIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='22',1,0)) AS 'IIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='23',1,0)) AS 'IIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='24',1,0)) AS 'IId'")
            )
            ->where('a_jabfung.idrumpun', '=', 3)
            ->orderBy('teknis', 'asc')
            ->groupBy('a_jabfung.tingkat', 'teknis');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikTeknisGolonganIII(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as teknis',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='31',1,0)) AS 'IIIa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='32',1,0)) AS 'IIIb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='33',1,0)) AS 'IIIc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='34',1,0)) AS 'IIId'")
            )
            ->where('a_jabfung.idrumpun', '=', 3)
            ->orderBy('teknis', 'asc')
            ->groupBy('a_jabfung.tingkat', 'teknis');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikTeknisGolonganIV(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $data = DB::table('tb_01 as b')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('a_jabfung', 'r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
            ->leftjoin('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->select(
                'a_jabfung.jabfung2 as teknis',
                'a_jabfung.tingkat',
                DB::raw("SUM(IF(c.id_kepangkatan='41',1,0)) AS 'IVa'"),
                DB::raw("SUM(IF(c.id_kepangkatan='42',1,0)) AS 'IVb'"),
                DB::raw("SUM(IF(c.id_kepangkatan='43',1,0)) AS 'IVc'"),
                DB::raw("SUM(IF(c.id_kepangkatan='44',1,0)) AS 'IVd'"),
                DB::raw("SUM(IF(c.id_kepangkatan='45',1,0)) AS 'IVe'"),
            )
            ->where('a_jabfung.idrumpun', '=', 3)
            ->orderBy('teknis', 'asc')
            ->groupBy('a_jabfung.tingkat', 'teknis');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableStatusPegawaiKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->selectRaw("
                    `employee_status`.`name` AS `status_pegawai`,
                    SUM(IF(b.jenis_kelamin = '1', 1, 0)) AS 'jumlah_pegawai_cowo',
                    SUM(IF(b.jenis_kelamin = '2', 1, 0)) AS 'jumlah_pegawai_cewe',
                    COUNT(*) AS 'total',
                    ROUND((SUM(IF(b.jenis_kelamin = '1', 1, 0)) /
                        (SELECT COUNT(*) FROM tb_01 WHERE status_pegawai IN (2, 3) AND kedudukan_pegawai NOT IN (99, 21))) * 100, 2) AS 'persen_cowo',
                    ROUND((SUM(IF(b.jenis_kelamin = '2', 1, 0)) /
                        (SELECT COUNT(*) FROM tb_01 WHERE status_pegawai IN (2, 3) AND kedudukan_pegawai NOT IN (99, 21))) * 100, 2) AS 'persen_cewe',
                    ROUND((COUNT(*) /
                        (SELECT COUNT(*) FROM tb_01 WHERE status_pegawai IN (2, 3) AND kedudukan_pegawai NOT IN (99, 21))) * 100, 2) AS 'persen_total'
                ")
                ->leftJoin('employee_status', 'b.status_pegawai', '=', 'employee_status.id')
                ->whereIn('b.status_pegawai', [2, 3])
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('status_pegawai');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikStatusPegawaiKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->selectRaw("
                    `employee_status`.`name` AS `status_pegawai`,
                    SUM(IF(b.jenis_kelamin = '1', 1, 0)) AS 'jumlah_pegawai_cowo',
                    SUM(IF(b.jenis_kelamin = '2', 1, 0)) AS 'jumlah_pegawai_cewe',
                    COUNT(*) AS 'total',
                    ROUND((SUM(IF(b.jenis_kelamin = '1', 1, 0)) /
                        (SELECT COUNT(*) FROM tb_01 WHERE status_pegawai IN (2, 3) AND kedudukan_pegawai NOT IN (99, 21))) * 100, 2) AS 'persen_cowo',
                    ROUND((SUM(IF(b.jenis_kelamin = '2', 1, 0)) /
                        (SELECT COUNT(*) FROM tb_01 WHERE status_pegawai IN (2, 3) AND kedudukan_pegawai NOT IN (99, 21))) * 100, 2) AS 'persen_cewe',
                    ROUND((COUNT(*) /
                        (SELECT COUNT(*) FROM tb_01 WHERE status_pegawai IN (2, 3) AND kedudukan_pegawai NOT IN (99, 21))) * 100, 2) AS 'persen_total'
                ")
                ->leftJoin('employee_status', 'b.status_pegawai', '=', 'employee_status.id')
                ->whereIn('b.status_pegawai', [2, 3])
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('status_pegawai');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableStatusPegawaiPendidikan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        // Calculate total_records
        $total_records = DB::table('tb_01')
                        ->join('r_pendidikan_formal', function ($join) {
                            $join->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
                                ->where('r_pendidikan_formal.isakhir', '=', 1);
                        })
                        ->leftJoin('r_jabatan', function ($join) {
                            $join->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1);
                        })
                        ->whereIn('status_pegawai', [2, 3])
                        ->whereNotIn('kedudukan_pegawai', [99, 21])
                        ->count();


        // Main query
        $data = DB::table('tb_01 as b')
        ->join('r_pendidikan_formal', function ($join) {
            $join->on('b.id', '=', 'r_pendidikan_formal.id_pegawai')
                ->where('r_pendidikan_formal.isakhir', '=', 1);
        })
        ->leftJoin('a_tkpendid', 'r_pendidikan_formal.id_jenjang', '=', 'a_tkpendid.idtkpendid')
        ->leftJoin('employee_status', 'b.status_pegawai', '=', 'employee_status.id')
        ->leftJoin('r_jabatan', function ($join) {
            $join->on('b.id', '=', 'r_jabatan.id_pegawai')
                ->where('r_jabatan.isakhir', '=', 1);
        })
        ->selectRaw("
            `employee_status`.`name` AS `status_pegawai`,
            SUM(IF(a_tkpendid.idtkpendid IN (10,20,30), 1, 0)) AS 'sd_sma',
            SUM(IF(a_tkpendid.idtkpendid IN (41,42,43), 1, 0)) AS 'd1_d3',
            SUM(IF(a_tkpendid.idtkpendid IN (44,50,60,70), 1, 0)) AS 'd4_s1',
            SUM(IF(a_tkpendid.idtkpendid=80, 1, 0)) AS 's2',
            SUM(IF(a_tkpendid.idtkpendid=90, 1, 0)) AS 's3',
            COUNT(*) AS 'total',
            ROUND((SUM(IF(a_tkpendid.idtkpendid IN (10,20,30), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_sd_sma',
            ROUND((SUM(IF(a_tkpendid.idtkpendid IN (41,42,43), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_d1_d3',
            ROUND((SUM(IF(a_tkpendid.idtkpendid IN (44,50,60,70), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_d4_s1',
            ROUND((SUM(IF(a_tkpendid.idtkpendid=80, 1, 0)) / {$total_records}) * 100, 2) AS 'persen_s2',
            ROUND((SUM(IF(a_tkpendid.idtkpendid=90, 1, 0)) / {$total_records}) * 100, 2) AS 'persen_s3',
            ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
        ")
        ->whereIn('b.status_pegawai', [2, 3])
        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
        ->groupBy('status_pegawai');

        // additional filters
        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
        $data->where('b.status_pegawai', $status_pegawai);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
        $data->where('r_jabatan.id_jenis_jabatan', $id_jenjab);
        }

        return JsonResponseHandler::setResult($data->get())->send();

    }

    public function grafikStatusPegawaiPendidikan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        // Calculate total_records
        $total_records = DB::table('tb_01')
                        ->join('r_pendidikan_formal', function ($join) {
                            $join->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
                                ->where('r_pendidikan_formal.isakhir', '=', 1);
                        })
                        ->leftJoin('r_jabatan', function ($join) {
                            $join->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1);
                        })
                        ->whereIn('status_pegawai', [2, 3])
                        ->whereNotIn('kedudukan_pegawai', [99, 21])
                        ->count();


        // Main query
        $data = DB::table('tb_01 as b')
        ->join('r_pendidikan_formal', function ($join) {
            $join->on('b.id', '=', 'r_pendidikan_formal.id_pegawai')
                ->where('r_pendidikan_formal.isakhir', '=', 1);
        })
        ->leftJoin('a_tkpendid', 'r_pendidikan_formal.id_jenjang', '=', 'a_tkpendid.idtkpendid')
        ->leftJoin('employee_status', 'b.status_pegawai', '=', 'employee_status.id')
        ->leftJoin('r_jabatan', function ($join) {
            $join->on('b.id', '=', 'r_jabatan.id_pegawai')
                ->where('r_jabatan.isakhir', '=', 1);
        })
        ->selectRaw("
            `employee_status`.`name` AS `status_pegawai`,
            SUM(IF(a_tkpendid.idtkpendid IN (10,20,30), 1, 0)) AS 'sd_sma',
            SUM(IF(a_tkpendid.idtkpendid IN (41,42,43), 1, 0)) AS 'd1_d3',
            SUM(IF(a_tkpendid.idtkpendid IN (44,50,60,70), 1, 0)) AS 'd4_s1',
            SUM(IF(a_tkpendid.idtkpendid=80, 1, 0)) AS 's2',
            SUM(IF(a_tkpendid.idtkpendid=90, 1, 0)) AS 's3',
            COUNT(*) AS 'total',
            ROUND((SUM(IF(a_tkpendid.idtkpendid IN (10,20,30), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_sd_sma',
            ROUND((SUM(IF(a_tkpendid.idtkpendid IN (41,42,43), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_d1_d3',
            ROUND((SUM(IF(a_tkpendid.idtkpendid IN (44,50,60,70), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_d4_s1',
            ROUND((SUM(IF(a_tkpendid.idtkpendid=80, 1, 0)) / {$total_records}) * 100, 2) AS 'persen_s2',
            ROUND((SUM(IF(a_tkpendid.idtkpendid=90, 1, 0)) / {$total_records}) * 100, 2) AS 'persen_s3',
            ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
        ")
        ->whereIn('b.status_pegawai', [2, 3])
        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
        ->groupBy('status_pegawai');

        // additional filters
        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
        $data->where('b.status_pegawai', $status_pegawai);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
        $data->where('r_jabatan.id_jenis_jabatan', $id_jenjab);
        }

        return JsonResponseHandler::setResult($data->get())->send();

    }

    public function datatableJenisKelaminPendidikan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        // Calculate total_records
        $total_records = DB::table('tb_01')
                        ->join('r_pendidikan_formal', function ($join) {
                            $join->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
                                ->where('r_pendidikan_formal.isakhir', '=', 1);
                        })
                        ->leftJoin('r_jabatan', function ($join) {
                            $join->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1);
                        })
                        ->leftJoin('gender', 'tb_01.jenis_kelamin', '=', 'gender.id')
                        ->whereNotIn('kedudukan_pegawai', [99, 21])
                        ->count();


        // Main query
        $data = DB::table('tb_01 as b')
        ->join('r_pendidikan_formal', function ($join) {
            $join->on('b.id', '=', 'r_pendidikan_formal.id_pegawai')
                ->where('r_pendidikan_formal.isakhir', '=', 1);
        })
        ->leftJoin('a_tkpendid', 'r_pendidikan_formal.id_jenjang', '=', 'a_tkpendid.idtkpendid')
        ->leftJoin('gender', 'b.jenis_kelamin', '=', 'gender.id')
        ->leftJoin('r_jabatan', function ($join) {
            $join->on('b.id', '=', 'r_jabatan.id_pegawai')
                ->where('r_jabatan.isakhir', '=', 1);
        })
        ->selectRaw("
            `a_tkpendid`.`tkpendid` AS `jenjang_pendidikan`,
            SUM(IF(b.jenis_kelamin!='',1,0)) as 'total',
            SUM(IF(b.jenis_kelamin='1',1,0)) AS 'laki',
            SUM(IF(b.jenis_kelamin='2',1,0)) AS 'perempuan',
            ROUND((SUM(IF(b.jenis_kelamin='1',1,0)) / {$total_records}) * 100, 2) AS 'persen_laki',
            ROUND((SUM(IF(b.jenis_kelamin='2',1,0)) / {$total_records}) * 100, 2) AS 'persen_perempuan',
            ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
        ")
        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
        ->groupBy('jenjang_pendidikan')
        ->orderBy('a_tkpendid.idtkpendid', 'asc');

        // additional filters
        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
        $data->where('b.status_pegawai', $status_pegawai);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
        $data->where('r_jabatan.id_jenis_jabatan', $id_jenjab);
        }

        return JsonResponseHandler::setResult($data->get())->send();

    }

    public function grafikJenisKelaminPendidikan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        // Calculate total_records
        $total_records = DB::table('tb_01')
                        ->join('r_pendidikan_formal', function ($join) {
                            $join->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
                                ->where('r_pendidikan_formal.isakhir', '=', 1);
                        })
                        ->leftJoin('r_jabatan', function ($join) {
                            $join->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1);
                        })
                        ->leftJoin('gender', 'tb_01.jenis_kelamin', '=', 'gender.id')
                        ->whereNotIn('kedudukan_pegawai', [99, 21])
                        ->count();


        // Main query
        $data = DB::table('tb_01 as b')
        ->join('r_pendidikan_formal', function ($join) {
            $join->on('b.id', '=', 'r_pendidikan_formal.id_pegawai')
                ->where('r_pendidikan_formal.isakhir', '=', 1);
        })
        ->leftJoin('a_tkpendid', 'r_pendidikan_formal.id_jenjang', '=', 'a_tkpendid.idtkpendid')
        ->leftJoin('gender', 'b.jenis_kelamin', '=', 'gender.id')
        ->leftJoin('r_jabatan', function ($join) {
            $join->on('b.id', '=', 'r_jabatan.id_pegawai')
                ->where('r_jabatan.isakhir', '=', 1);
        })
        ->selectRaw("
            `a_tkpendid`.`tkpendid` AS `jenjang_pendidikan`,
            SUM(IF(b.jenis_kelamin!='',1,0)) as 'total',
            SUM(IF(b.jenis_kelamin='1',1,0)) AS 'laki',
            SUM(IF(b.jenis_kelamin='2',1,0)) AS 'perempuan',
            ROUND((SUM(IF(b.jenis_kelamin='1',1,0)) / {$total_records}) * 100, 2) AS 'persen_laki',
            ROUND((SUM(IF(b.jenis_kelamin='2',1,0)) / {$total_records}) * 100, 2) AS 'persen_perempuan',
            ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
        ")
        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
        ->groupBy('jenjang_pendidikan')
        ->orderBy('a_tkpendid.idtkpendid', 'asc');

        // additional filters
        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
        $data->where('b.status_pegawai', $status_pegawai);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
        $data->where('r_jabatan.id_jenis_jabatan', $id_jenjab);
        }

        return JsonResponseHandler::setResult($data->get())->send();

    }

    public function datatableManajerialEselonPendidikan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        // Calculate total_records
        $total_records = DB::table('tb_01')
                        ->join('r_pendidikan_formal', function ($join) {
                            $join->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
                                ->where('r_pendidikan_formal.isakhir', '=', 1);
                        })
                        ->join('r_jabatan', function ($join) {
                            $join
                                ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1)
                                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                        })
                        ->whereNotIn('kedudukan_pegawai', [99, 21])
                        ->count();


        // Main query
        $data = DB::table('tb_01 as b')
        ->join('r_pendidikan_formal', function ($join) {
            $join->on('b.id', '=', 'r_pendidikan_formal.id_pegawai')
                ->where('r_pendidikan_formal.isakhir', '=', 1);
        })
        ->leftJoin('a_tkpendid', 'r_pendidikan_formal.id_jenjang', '=', 'a_tkpendid.idtkpendid')
        ->join('r_jabatan', function ($join) {
            $join
                ->on('b.id', '=', 'r_jabatan.id_pegawai')
                ->where('r_jabatan.isakhir', '=', 1)
                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
        })
        ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
        ->selectRaw("
            `a_tkpendid`.`tkpendid` AS `jenjang_pendidikan`,
            SUM(IF(r_jabatan.id_eselon!='',1,0)) as 'total',
            SUM(IF(r_jabatan.id_eselon IN (11,12), 1, 0)) AS 'eselon_1',
            SUM(IF(r_jabatan.id_eselon IN (21,22), 1, 0)) AS 'eselon_2',
            SUM(IF(r_jabatan.id_eselon IN (31,32), 1, 0)) AS 'eselon_3',
            SUM(IF(r_jabatan.id_eselon IN (41,42), 1, 0)) AS 'eselon_4',
            SUM(IF(r_jabatan.id_eselon IN (51,52), 1, 0)) AS 'eselon_5',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (11,12), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_1',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (21,22), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_2',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (31,32), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_3',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (41,42), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_4',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (51,52), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_5',
            ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
        ")
        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
        ->groupBy('jenjang_pendidikan')
        ->orderBy('a_tkpendid.idtkpendid', 'asc');

        // additional filters
        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
        $data->where('b.status_pegawai', $status_pegawai);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
        $data->where('r_jabatan.id_jenis_jabatan', $id_jenjab);
        }

        return JsonResponseHandler::setResult($data->get())->send();

    }

    public function grafikManajerialEselonPendidikan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        // Calculate total_records
        $total_records = DB::table('tb_01')
                        ->join('r_pendidikan_formal', function ($join) {
                            $join->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
                                ->where('r_pendidikan_formal.isakhir', '=', 1);
                        })
                        ->join('r_jabatan', function ($join) {
                            $join
                                ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1)
                                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                        })
                        ->whereNotIn('kedudukan_pegawai', [99, 21])
                        ->count();


        // Main query
        $data = DB::table('tb_01 as b')
        ->join('r_pendidikan_formal', function ($join) {
            $join->on('b.id', '=', 'r_pendidikan_formal.id_pegawai')
                ->where('r_pendidikan_formal.isakhir', '=', 1);
        })
        ->leftJoin('a_tkpendid', 'r_pendidikan_formal.id_jenjang', '=', 'a_tkpendid.idtkpendid')
        ->join('r_jabatan', function ($join) {
            $join
                ->on('b.id', '=', 'r_jabatan.id_pegawai')
                ->where('r_jabatan.isakhir', '=', 1)
                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
        })
        ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
        ->selectRaw("
            `a_tkpendid`.`tkpendid` AS `jenjang_pendidikan`,
            SUM(IF(r_jabatan.id_eselon!='',1,0)) as 'total',
            SUM(IF(r_jabatan.id_eselon IN (11,12), 1, 0)) AS 'eselon_1',
            SUM(IF(r_jabatan.id_eselon IN (21,22), 1, 0)) AS 'eselon_2',
            SUM(IF(r_jabatan.id_eselon IN (31,32), 1, 0)) AS 'eselon_3',
            SUM(IF(r_jabatan.id_eselon IN (41,42), 1, 0)) AS 'eselon_4',
            SUM(IF(r_jabatan.id_eselon IN (51,52), 1, 0)) AS 'eselon_5',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (11,12), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_1',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (21,22), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_2',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (31,32), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_3',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (41,42), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_4',
            ROUND((SUM(IF(r_jabatan.id_eselon IN (51,52), 1, 0)) / {$total_records}) * 100, 2) AS 'persen_eselon_5',
            ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'
        ")
        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
        ->groupBy('jenjang_pendidikan')
        ->orderBy('a_tkpendid.idtkpendid', 'asc');

        // additional filters
        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
        $data->where('b.status_pegawai', $status_pegawai);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
        $data->where('r_jabatan.id_jenis_jabatan', $id_jenjab);
        }

        return JsonResponseHandler::setResult($data->get())->send();

    }

    public function datatableUnitKerjaKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('a_skpd as a')
            ->select(
                'a.idskpd',
                'a.id_unit_kerja',
                'a.id_induk_upt',
                'a.id_sub_unit',
                'a.id_sub_sub_unit',
                'a.id_sub_sub_sub_unit',
                'a.path as skpd',
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(if(b.jenis_kelamin='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'")
            );

        if (!empty($id_unit_kerja)) {
            $data = $data->leftjoin('tb_01 as b', function ($join) {
                $join->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikUnitKerjaKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('a_skpd as a')
            ->select(
                'a.idskpd',
                'a.id_unit_kerja',
                'a.id_induk_upt',
                'a.id_sub_unit',
                'a.id_sub_sub_unit',
                'a.id_sub_sub_sub_unit',
                'a.path as skpd',
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'")
            );

        if (!empty($id_unit_kerja)) {
            $data = $data->leftjoin('tb_01 as b', function ($join) {
                $join->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->on('a.id_induk_upt', '=', 'b.id_induk_upt')
                    ->on('a.id_sub_unit', '=', 'b.id_sub_unit')
                    ->on('a.id_sub_sub_unit', '=', 'b.id_sub_sub_unit')
                    ->on('a.id_sub_sub_sub_unit', '=', 'b.id_sub_sub_sub_unit')
                    ->where('a.flag', '=', 1);
            });
        } else {
            $data->join('tb_01 as b', function ($join) {
                $join->on('a.id_unit_kerja', '=', 'b.id_unit_kerja')
                    ->where('a.id_induk_upt', '=', '00')
                    ->where('a.id_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_unit', '=', '00')
                    ->where('a.id_sub_sub_sub_unit', '=', '00')
                    ->where('a.flag', '=', 1);
            });
        }

        $data->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->whereNotIn('b.kedudukan_pegawai', [99, 21]);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.idskpd', 'asc');
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->groupBy('a.id_unit_kerja', 'a.id_induk_upt', 'a.id_sub_unit', 'a.id_sub_sub_unit', 'a.id_sub_sub_sub_unit')
                    ->orderBy('a.path', 'asc');
            }
        } else {
            $data = $data->groupBy('a.id_unit_kerja')->orderBy('a.path', 'asc');
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableMasaKerjaKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('r_kepangkatan as a')
            ->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_pegawai', '=', 'b.id')
                    ->where('a.isakhir', '=', 1);
            })
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.masa_kerja_tahun as masa_kerja',
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(if(b.jenis_kelamin='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('masa_kerja')
            ->orderBy('masa_kerja', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikMasaKerjaKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('r_kepangkatan as a')
            ->join('tb_01 as b', function ($join) {
                $join
                    ->on('a.id_pegawai', '=', 'b.id')
                    ->where('a.isakhir', '=', 1);
            })
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                'a.masa_kerja_tahun as masa_kerja',
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('masa_kerja')
            ->orderBy('masa_kerja', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableJabfungKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('a_jabfung as a')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('a.idjabfung', '=', 'r_jabatan.id_jabatan')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('tb_01 as b', 'r_jabatan.id_pegawai', '=', 'b.id')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->select(
                'a.idjabfung',
                'a.jabfung as jabfung',
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(if(b.jenis_kelamin='',1,0)) AS 'kosong'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('a.idjabfung', 'jabfung')
            ->orderBy('jabfung', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikJabfungKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('a_jabfung as a')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('a.idjabfung', '=', 'r_jabatan.id_jabatan')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('tb_01 as b', 'r_jabatan.id_pegawai', '=', 'b.id')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->select(
                'a.jabfung as jabfung',
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('jabfung')
            ->orderBy('jabfung', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableUsiaKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $total_records = DB::table('tb_01 as b')
                        ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
                        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                        ->count();

        $data = DB::table('tb_01 as b')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                DB::raw("
                        CASE
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 20 AND 30 THEN '20-30'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 31 AND 45 THEN '31-45'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 46 AND 55 THEN '46-55'
                            ELSE '>55'
                        END AS usia_group
                    "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='1',1,0)) / {$total_records}) * 100, 2) AS 'persen_pria'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='2',1,0)) / {$total_records}) * 100, 2) AS 'persen_wanita'"),
                DB::raw("ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('usia_group')
            ->orderBy(DB::raw("FIELD(usia_group, '20-30', '31-45', '46-55', '>55')"), 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikUsiaKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->select(
                DB::raw("
                        CASE
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 20 AND 30 THEN '20-30'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 31 AND 45 THEN '31-45'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 46 AND 55 THEN '46-55'
                            ELSE '>55'
                        END AS usia_group
                    "),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('usia_group')
            ->orderBy(DB::raw("FIELD(usia_group, '20-30', '31-45', '46-55', '>55')"), 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfUsiaKelamin(Request $request)
    {
        $usia_group = $request->input("usia_group");
        $gender_id = $request->input("gender_id");

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00
        // dd($request->all());

        // Mulai query
        $employee = DB::table('tb_01')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->where('tb_01.jenis_kelamin', $gender_id);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        //filter usia
        if ($usia_group == "20-30") {
            $employee = $employee->whereRaw('TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) BETWEEN 20 AND 30');
        } elseif ($usia_group == "31-45") {
            $employee = $employee->whereRaw('TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) BETWEEN 31 AND 45');
        } elseif ($usia_group == "46-55") {
            $employee = $employee->whereRaw('TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) BETWEEN 46 AND 55');
        } elseif ($usia_group == ">55") {
            $employee = $employee->whereRaw('TIMESTAMPDIFF(YEAR, tanggal_lahir, CURDATE()) > 55');
        }

        $employee = $employee->get();
        // dd($employee);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.usia_kelamin', ['employee' => $employee]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function datatableManajerialKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $total_records = DB::table('tb_01 as b')
                        ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
                        ->join('r_jabatan', function ($join) {
                            $join
                                ->on('b.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1)
                                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                        })
                        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                        ->count();

        $data = DB::table('tb_01 as b')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 1);
            })
            ->select(
                DB::raw("
                        CASE
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) <35 THEN '<35'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 36 AND 45 THEN '36-45'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 46 AND 55 THEN '46-55'
                            ELSE '>55'
                        END AS usia_group
                    "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='1',1,0)) / {$total_records}) * 100, 2) AS 'persen_pria'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='2',1,0)) / {$total_records}) * 100, 2) AS 'persen_wanita'"),
                DB::raw("ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('usia_group')
            ->orderBy(DB::raw("FIELD(usia_group, '<35', '36-45', '46-55', '>55')"), 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikManajerialKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $total_records = DB::table('tb_01 as b')
                        ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
                        ->join('r_jabatan', function ($join) {
                            $join
                                ->on('b.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1)
                                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                        })
                        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                        ->count();

        $data = DB::table('tb_01 as b')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 1);
            })
            ->select(
                DB::raw("
                        CASE
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) <35 THEN '<35'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 36 AND 45 THEN '36-45'
                            WHEN TIMESTAMPDIFF(YEAR, b.tanggal_lahir, CURDATE()) BETWEEN 46 AND 55 THEN '46-55'
                            ELSE '>55'
                        END AS usia_group
                    "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='1',1,0)) / {$total_records}) * 100, 2) AS 'persen_pria'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='2',1,0)) / {$total_records}) * 100, 2) AS 'persen_wanita'"),
                DB::raw("ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('usia_group')
            ->orderBy(DB::raw("FIELD(usia_group, '<35', '36-45', '46-55', '>55')"), 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableManajerialEselonKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $total_records = DB::table('tb_01 as b')
                        ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
                        ->join('r_jabatan', function ($join) {
                            $join
                                ->on('b.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1)
                                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                        })
                        ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
                        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                        ->count();

        $data = DB::table('tb_01 as b')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 1);
            })
            ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
            ->select(
                DB::raw("
                    CASE
                        WHEN eselon.name IN ('I/a', 'I/b') THEN 'I'
                        WHEN eselon.name IN ('II/a', 'II/b') THEN 'II'
                        WHEN eselon.name IN ('III/a', 'III/b') THEN 'III'
                        WHEN eselon.name IN ('IV/a', 'IV/b') THEN 'IV'
                        WHEN eselon.name IN ('V/a', 'V/b') THEN 'V'
                        ELSE 'NON ESELON'
                    END AS eselon
                "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='1',1,0)) / {$total_records}) * 100, 2) AS 'persen_pria'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='2',1,0)) / {$total_records}) * 100, 2) AS 'persen_wanita'"),
                DB::raw("ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('eselon')
            ->orderBy('eselon', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikManajerialEselonKelamin(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $total_records = DB::table('tb_01 as b')
                        ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
                        ->join('r_jabatan', function ($join) {
                            $join
                                ->on('b.id', '=', 'r_jabatan.id_pegawai')
                                ->where('r_jabatan.isakhir', '=', 1)
                                ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                        })
                        ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
                        ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                        ->count();

        $data = DB::table('tb_01 as b')
            ->leftjoin('gender as c', 'b.jenis_kelamin', '=', 'c.id')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 1);
            })
            ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
            ->select(
                DB::raw("
                    CASE
                        WHEN eselon.name IN ('I/a', 'I/b') THEN 'I'
                        WHEN eselon.name IN ('II/a', 'II/b') THEN 'II'
                        WHEN eselon.name IN ('III/a', 'III/b') THEN 'III'
                        WHEN eselon.name IN ('IV/a', 'IV/b') THEN 'IV'
                        WHEN eselon.name IN ('V/a', 'V/b') THEN 'V'
                        ELSE 'NON ESELON'
                    END AS eselon
                "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(IF(b.jenis_kelamin='1',1,0)) AS 'pria'"),
                DB::raw("SUM(IF(b.jenis_kelamin='2',1,0)) AS 'wanita'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='1',1,0)) / {$total_records}) * 100, 2) AS 'persen_pria'"),
                DB::raw("ROUND((SUM(IF(b.jenis_kelamin='2',1,0)) / {$total_records}) * 100, 2) AS 'persen_wanita'"),
                DB::raw("ROUND((COUNT(*) / {$total_records}) * 100, 2) AS 'persen_total'")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('eselon')
            ->orderBy('eselon', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function datatableP3kJabatan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_kepangkatan', 'b.id', '=', 'r_kepangkatan.id_pegawai')
            ->leftjoin('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
            ->select(
                'kepangkatan.golru_p3k as golru',
                DB::raw("COUNT(*) AS 'total'"),
            )
            ->where('b.status_pegawai', 3)
            ->where('r_kepangkatan.isakhir', 1)
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('golru', 'r_kepangkatan.id_kepangkatan')
            ->orderBy('r_kepangkatan.id_kepangkatan', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();
        $total = $data->sum('total');

        $data = $data->map(function ($item) use ($total) {
            $item->persen = $total > 0 ? round(($item->total / $total) * 100, 2) : 0;
            return $item;
        });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function grafikP3kJabatan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_kepangkatan', 'b.id', '=', 'r_kepangkatan.id_pegawai')
            ->leftjoin('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
            ->select(
                'kepangkatan.golru_p3k as golru',
                DB::raw("COUNT(*) AS 'total'"),
            )
            ->where('b.status_pegawai', 3)
            ->where('r_kepangkatan.isakhir', 1)
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->groupBy('golru', 'r_kepangkatan.id_kepangkatan')
            ->orderBy('r_kepangkatan.id_kepangkatan', 'asc');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();
        $total = $data->sum('total');

        $data = $data->map(function ($item) use ($total) {
            $item->persen = $total > 0 ? round(($item->total / $total) * 100, 2) : 0;
            return $item;
        });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatableP3kTmtMasuk(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->leftJoin('r_pppk', 'b.id', '=', 'r_pppk.id_pegawai')
                ->select(
                    DB::raw("YEAR(r_pppk.tmt_kontrak_awal) as tmt_masuk"),
                    DB::raw("COUNT(*) AS 'total'"),
                )
                ->where('b.status_pegawai', 3)
                ->where('r_pppk.isakhir', 1)
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('tmt_masuk')
                ->orderBy('tmt_masuk', 'asc');

                if (!empty($id_unit_kerja)) {
                    if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit)
                            ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                            ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                    } elseif (!empty($id_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit);
                    } else {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
                    }
                }

                if ($status_pegawai != null && $status_pegawai != "") {
                    $data = $data->where('b.status_pegawai', [$status_pegawai]);
                }

                if ($id_jenjab != null && $id_jenjab != "") {
                    $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
                }

            $data = $data->get();
            $total = $data->sum('total');

            $data = $data->map(function($item) use ($total) {
                $item->persen = $total > 0 ? round(($item->total / $total) * 100,2) : 0;
                return $item;
            });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function grafikP3kTmtMasuk(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->leftJoin('r_pppk', 'b.id', '=', 'r_pppk.id_pegawai')
                ->select(
                    DB::raw("YEAR(r_pppk.tmt_kontrak_awal) as tmt_masuk"),
                    DB::raw("COUNT(*) AS 'total'"),
                )
                ->where('b.status_pegawai', 3)
                ->where('r_pppk.isakhir', 1)
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('tmt_masuk')
                ->orderBy('tmt_masuk', 'asc');

                if (!empty($id_unit_kerja)) {
                    if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit)
                            ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                            ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                    } elseif (!empty($id_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit);
                    } else {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
                    }
                }

                if ($status_pegawai != null && $status_pegawai != "") {
                    $data = $data->where('b.status_pegawai', [$status_pegawai]);
                }

                if ($id_jenjab != null && $id_jenjab != "") {
                    $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
                }

            $data = $data->get();
            $total = $data->sum('total');

            $data = $data->map(function($item) use ($total) {
                $item->persen = $total > 0 ? round(($item->total / $total) * 100,2) : 0;
                return $item;
            });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatablePnsGol(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->leftJoin('r_kepangkatan', 'b.id', '=', 'r_kepangkatan.id_pegawai')
                ->leftjoin('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
                ->select(
                    DB::raw("
                        CASE
                            WHEN kepangkatan.name IN ('I/a', 'I/b', 'I/c', 'I/d') THEN 'I'
                            WHEN kepangkatan.name IN ('II/a', 'II/b', 'II/c', 'II/d') THEN 'II'
                            WHEN kepangkatan.name IN ('III/a', 'III/b', 'III/c', 'III/d') THEN 'III'
                            WHEN kepangkatan.name IN ('IV/a', 'IV/b', 'IV/c', 'IV/d') THEN 'IV'
                        END AS golru
                    "),
                    DB::raw("COUNT(*) AS 'total'"),
                )
                ->where('b.status_pegawai', 2)
                ->where('r_kepangkatan.isakhir', 1)
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('golru')
                ->orderBy('golru', 'asc');

                if (!empty($id_unit_kerja)) {
                    if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit)
                            ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                            ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                    } elseif (!empty($id_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit);
                    } else {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
                    }
                }

                if ($status_pegawai != null && $status_pegawai != "") {
                    $data = $data->where('b.status_pegawai', [$status_pegawai]);
                }

                if ($id_jenjab != null && $id_jenjab != "") {
                    $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
                }

            $data = $data->get();
            $total = $data->sum('total');

            $data = $data->map(function($item) use ($total) {
                $item->persen = $total > 0 ? round(($item->total / $total) * 100,2) : 0;
                return $item;
            });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function grafikPnsGol(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->leftJoin('r_kepangkatan', 'b.id', '=', 'r_kepangkatan.id_pegawai')
                ->leftjoin('kepangkatan', 'r_kepangkatan.id_kepangkatan', '=', 'kepangkatan.id')
                ->select(
                    DB::raw("
                        CASE
                            WHEN kepangkatan.name IN ('I/a', 'I/b', 'I/c', 'I/d') THEN 'I'
                            WHEN kepangkatan.name IN ('II/a', 'II/b', 'II/c', 'II/d') THEN 'II'
                            WHEN kepangkatan.name IN ('III/a', 'III/b', 'III/c', 'III/d') THEN 'III'
                            WHEN kepangkatan.name IN ('IV/a', 'IV/b', 'IV/c', 'IV/d') THEN 'IV'
                        END AS golru
                    "),
                    DB::raw("COUNT(*) AS 'total'"),
                )
                ->where('b.status_pegawai', 2)
                ->where('r_kepangkatan.isakhir', 1)
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('golru')
                ->orderBy('golru', 'asc');

                if (!empty($id_unit_kerja)) {
                    if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit)
                            ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                            ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                    } elseif (!empty($id_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit);
                    } else {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
                    }
                }

                if ($status_pegawai != null && $status_pegawai != "") {
                    $data = $data->where('b.status_pegawai', [$status_pegawai]);
                }

                if ($id_jenjab != null && $id_jenjab != "") {
                    $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
                }

            $data = $data->get();
            $total = $data->sum('total');

            $data = $data->map(function($item) use ($total) {
                $item->persen = $total > 0 ? round(($item->total / $total) * 100,2) : 0;
                return $item;
            });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatableJabManajerial(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->join('r_jabatan', function ($join) {
                    $join
                        ->on('b.id', '=', 'r_jabatan.id_pegawai')
                        ->where('r_jabatan.isakhir', '=', 1)
                        ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                })
                ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
                ->select(
                    DB::raw("
                        CASE
                            WHEN eselon.name IN ('I/a', 'I/b') THEN 'I'
                            WHEN eselon.name IN ('II/a', 'II/b') THEN 'II'
                            WHEN eselon.name IN ('III/a', 'III/b') THEN 'III'
                            WHEN eselon.name IN ('IV/a', 'IV/b') THEN 'IV'
                            WHEN eselon.name IN ('V/a', 'V/b') THEN 'V'
                            ELSE 'NON ESELON'
                        END AS eselon
                    "),
                    DB::raw("COUNT(*) AS 'total'"),
                )
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('eselon')
                ->orderBy('eselon', 'asc');

                if (!empty($id_unit_kerja)) {
                    if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit)
                            ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                            ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                    } elseif (!empty($id_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit);
                    } else {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
                    }
                }

                if ($status_pegawai != null && $status_pegawai != "") {
                    $data = $data->where('b.status_pegawai', [$status_pegawai]);
                }

                if ($id_jenjab != null && $id_jenjab != "") {
                    $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
                }

            $data = $data->get();
            $total = $data->sum('total');

            $data = $data->map(function($item) use ($total) {
                $item->persen = $total > 0 ? round(($item->total / $total) * 100,2) : 0;
                return $item;
            });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function grafikJabManajerial(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
                ->join('r_jabatan', function ($join) {
                    $join
                        ->on('b.id', '=', 'r_jabatan.id_pegawai')
                        ->where('r_jabatan.isakhir', '=', 1)
                        ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                })
                ->leftjoin('eselon', 'r_jabatan.id_eselon', '=', 'eselon.id')
                ->select(
                    DB::raw("
                        CASE
                            WHEN eselon.name IN ('I/a', 'I/b') THEN 'I'
                            WHEN eselon.name IN ('II/a', 'II/b') THEN 'II'
                            WHEN eselon.name IN ('III/a', 'III/b') THEN 'III'
                            WHEN eselon.name IN ('IV/a', 'IV/b') THEN 'IV'
                            WHEN eselon.name IN ('V/a', 'V/b') THEN 'V'
                            ELSE 'NON ESELON'
                        END AS eselon
                    "),
                    DB::raw("COUNT(*) AS 'total'"),
                )
                ->whereNotIn('b.kedudukan_pegawai', [99, 21])
                ->groupBy('eselon')
                ->orderBy('eselon', 'asc');

                if (!empty($id_unit_kerja)) {
                    if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit)
                            ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                            ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                    } elseif (!empty($id_sub_unit)) {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                            ->where('b.id_induk_upt', $pecah_induk_upt)
                            ->where('b.id_sub_unit', $pecah_sub_unit);
                    } else {
                        $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
                    }
                }

                if ($status_pegawai != null && $status_pegawai != "") {
                    $data = $data->where('b.status_pegawai', [$status_pegawai]);
                }

                if ($id_jenjab != null && $id_jenjab != "") {
                    $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
                }

            $data = $data->get();
            $total = $data->sum('total');

            $data = $data->map(function($item) use ($total) {
                $item->persen = $total > 0 ? round(($item->total / $total) * 100,2) : 0;
                return $item;
            });

        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatableFormasiManajerial(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01')
                ->leftjoin('a_skpd', function ($join) {
                    $join
                        ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                        ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                        ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                        ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                        ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                        ->where('a_skpd.flag', '=', 1);
                })
                ->leftJoin('r_jabatan', function ($join) {
                    $join
                        ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                        ->where('r_jabatan.isakhir', '=', 1)
                        ->where('r_jabatan.id_jenis_jabatan', '=', 1);
                })
                ->select(
                    DB::raw("CASE
                        WHEN r_jabatan.id_eselon IN (11, 12) THEN 'I'
                        WHEN r_jabatan.id_eselon IN (21, 22) THEN 'II'
                        WHEN r_jabatan.id_eselon IN (31, 32) THEN 'III'
                        WHEN r_jabatan.id_eselon IN (41, 42) THEN 'IV'
                        WHEN r_jabatan.id_eselon IN (51, 52) THEN 'V'
                        ELSE 'NON ESELON'
                        END AS eselon"),
                    DB::raw('SUM(IF(a_skpd.id_unit_kerja!= "00"
                        and a_skpd.id_induk_upt = "00"
                        and a_skpd.id_sub_unit != "00"
                        and a_skpd.id_sub_sub_unit = "00"
                        and a_skpd.id_sub_sub_sub_unit = "00",1,0)) AS formasi_induk'),
                    DB::raw('SUM(IF(a_skpd.id_unit_kerja!= "00"
                        and a_skpd.id_induk_upt != "00"
                        and a_skpd.id_sub_unit = "00"
                        and a_skpd.id_sub_sub_unit = "00"
                        and a_skpd.id_sub_sub_sub_unit = "00",1,0)) AS formasi_upt'),
                    DB::raw('SUM(IF(tb_01.id_unit_kerja!= "00"
                        and tb_01.id_induk_upt = "00"
                        and tb_01.id_sub_unit != "00"
                        and tb_01.id_sub_sub_unit = "00"
                        and tb_01.id_sub_sub_sub_unit = "00",1,0)) AS terisi_induk'),
                    DB::raw('SUM(IF(tb_01.id_unit_kerja!= "00"
                        and tb_01.id_induk_upt != "00"
                        and tb_01.id_sub_unit = "00"
                        and tb_01.id_sub_sub_unit = "00"
                        and tb_01.id_sub_sub_sub_unit = "00",1,0)) AS terisi_upt'),
                )
                ->whereNotIn('tb_01.kedudukan_pegawai', [99, 21])
                ->groupBy('eselon')
                ->orderBy('eselon', 'asc');


            if (!empty($id_unit_kerja)) {
                if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                    $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                        ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                        ->where('tb_01.id_sub_unit', $pecah_sub_unit)
                        ->where('tb_01.id_sub_sub_unit', $pecah_sub_sub_unit)
                        ->where('tb_01.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
                } elseif (!empty($id_sub_unit)) {
                    $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                        ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                        ->where('tb_01.id_sub_unit', $pecah_sub_unit);
                } else {
                    $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja);
                }
            }

            if ($status_pegawai != null && $status_pegawai != "") {
                $data = $data->where('tb_01.status_pegawai', [$status_pegawai]);
            }

            if ($id_jenjab != null && $id_jenjab != "") {
                $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
            }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfJabfungKelamin(Request $request)
    {
        $idjabfung = $request->input("idjabfung");
        $gender_id = $request->input("gender_id");

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00
        // dd($request->all());

        // Mulai query
        $employee = DB::table('a_jabfung as a')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('a.idjabfung', '=', 'r_jabatan.id_jabatan')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->leftjoin('tb_01', 'r_jabatan.id_pegawai', '=', 'tb_01.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->where('tb_01.jenis_kelamin', $gender_id)
            ->where('r_jabatan.id_jabatan', $idjabfung);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        $employee = $employee->get();
        // dd($employee);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.jabfung_kelamin', ['employee' => $employee]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function pdfMasaKerjaKelamin(Request $request)
    {
        $masa_kerja = $request->input("masa_kerja");
        $gender_id = $request->input("gender_id");

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00
        // dd($request->all());

        // Mulai query
        $employee = DB::table('tb_01')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->where('tb_01.jenis_kelamin', $gender_id)
            ->where('c.masa_kerja_tahun', $masa_kerja);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        $employee = $employee->get();
        // dd($employee);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.masa_kerja_kelamin', ['employee' => $employee]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function pdfPendidikanKelamin(Request $request)
    {
        $idtkpendid = $request->input("idtkpendid");
        $gender_id = $request->input("gender_id");

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $id_jenjab = $request->input("id_jenjab");
        $status_pegawai = $request->input("status_pegawai");
        // dd($request->all());

        // Mulai query
        $employee = DB::table('a_tkpendid as a')
            ->join('r_pendidikan_formal as d', function ($join) {
                $join
                    ->on('a.idtkpendid', '=', 'd.id_jenjang')
                    ->where('d.isakhir', '=', 1);
            })
            ->leftjoin('tb_01', 'd.id_pegawai', '=', 'tb_01.id')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->where('tb_01.jenis_kelamin', $gender_id)
            ->where('d.id_jenjang', $idtkpendid);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        // filter jenjab dan status pegawai
        if ($status_pegawai != null && $status_pegawai != "") {
            $employee = $employee->where('tb_01.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $employee = $employee->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $employee = $employee->get();
        // dd($employee);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.pendidikan_kelamin', ['employee' => $employee]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function pdfJenisPegawaiKelamin(Request $request)
    {
        $jenkepeg_id = $request->input("jenkepeg_id");
        $gender_id = $request->input("gender_id");

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $id_jenjab = $request->input("id_jenjab");
        $status_pegawai = $request->input("status_pegawai");
        // dd($request->all());

        // Mulai query
        $employee = DB::table('jenkepeg as a')
            ->leftjoin('tb_01', 'a.id', '=', 'tb_01.jenis_pegawai')
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->where('tb_01.jenis_kelamin', $gender_id)
            ->where('tb_01.jenis_pegawai', $jenkepeg_id);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        // filter jenjab dan status pegawai
        if ($status_pegawai != null && $status_pegawai != "") {
            $employee = $employee->where('tb_01.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $employee = $employee->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $employee = $employee->get();
        // dd($employee);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.jenis_pegawai_kelamin', ['employee' => $employee]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function pdfTeknisGolongan(Request $request)
    {
        $tingkat_id = $request->input("tingkat_id");
        $golongan_id = $request->input("golongan_id");

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $id_jenjab = $request->input("id_jenjab");
        $status_pegawai = $request->input("status_pegawai");
        // dd($request->all());

        // Mulai query
        $employee = DB::table('tb_01')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->join('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 3);
            })
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->where('c.id_kepangkatan', $golongan_id)
            ->where('a_jabfung.tingkat', $tingkat_id);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        // filter jenjab dan status pegawai
        if ($status_pegawai != null && $status_pegawai != "") {
            $employee = $employee->where('tb_01.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $employee = $employee->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $employee = $employee->get();
        // dd($employee);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.teknis_golongan', ['employee' => $employee]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function pdfKesehatanGolongan(Request $request)
    {
        $tingkat_id = $request->input("tingkat_id");
        $golongan_id = $request->input("golongan_id");

        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $id_jenjab = $request->input("id_jenjab");
        $status_pegawai = $request->input("status_pegawai");
        // dd($request->all());

        // Mulai query
        $employee = DB::table('tb_01')
            ->join('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1)
                    ->where('r_jabatan.id_jenis_jabatan', '=', 2);
            })
            ->join('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.idrumpun', '=', 1);
            })
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->where('c.id_kepangkatan', $golongan_id)
            ->where('a_jabfung.tingkat', $tingkat_id);


        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        // filter jenjab dan status pegawai
        if ($status_pegawai != null && $status_pegawai != "") {
            $employee = $employee->where('tb_01.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $employee = $employee->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $employee = $employee->get();
        // dd($employee);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.kesehatan_golongan', ['employee' => $employee]);
        return $pdf->download('executiveSummary.pdf');
    }

    // public function pdfPelaksanaGolongan(Request $request)
    // {
    //     $idjabfungum = $request->input("idjabfungum");
    //     $golongan_id = $request->input("golongan_id");

    //     $id_unit_kerja = $request->input('id_unit_kerja'); //B2
    //     $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
    //     $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

    //     $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
    //     $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
    //     $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
    //     $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

    //     $id_jenjab = $request->input("id_jenjab");
    //     $status_pegawai = $request->input("status_pegawai");
    //     // dd($request->all());

    //     // Mulai query
    //     $employee = DB::table('tb_01')
    //         ->join('r_jabatan', function ($join) {
    //             $join
    //                 ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
    //                 ->where('r_jabatan.id_jenis_jabatan', '=', 3)
    //                 ->where('r_jabatan.isakhir', '=', 1);
    //         })
    //         ->join('a_jabfungum as a', 'r_jabatan.id_jabatan', '=', 'a.idjabfungum')
    //         ->join('r_kepangkatan as c', function ($join) {
    //             $join
    //                 ->on('tb_01.id', '=', 'c.id_pegawai')
    //                 ->where('c.isakhir', '=', 1);
    //         })
    //         ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
    //         ->leftJoin('r_pendidikan_formal', function ($join) {
    //             $join->on('tb_01.id', '=', 'r_pendidikan_formal.id_pegawai')
    //                 ->where('r_pendidikan_formal.isakhir', 1);
    //         })
    //         ->join('a_tkpendid', 'r_pendidikan_formal.id_jenjang', '=', 'a_tkpendid.idtkpendid')
    //         ->leftjoin('a_skpd', function ($join) {
    //             $join
    //                 ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
    //                 ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
    //                 ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
    //                 ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
    //                 ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
    //                 ->where('a_skpd.flag', '=', 1);
    //         })
    //         ->when(!empty($golongan_id), function ($query) use ($golongan_id) {
    //             $query->where('c.id_kepangkatan', $golongan_id);
    //         })
    //         ->when(!empty($idjabfung), function ($query) use ($idjabfungum) {
    //             $query->where('r_jabatan.id_jabatan', $idjabfungum);
    //         });


    //     if (!empty($id_unit_kerja)) {
    //         if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
    //             $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
    //                 ->where('b.id_induk_upt', $pecah_induk_upt)
    //                 ->where('b.id_sub_unit', $pecah_sub_unit)
    //                 ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
    //                 ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
    //         } elseif (!empty($id_sub_unit)) {
    //             $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja)
    //                 ->where('b.id_induk_upt', $pecah_induk_upt)
    //                 ->where('b.id_sub_unit', $pecah_sub_unit);
    //         } else {
    //             $employee = $employee->where('b.id_unit_kerja', $id_unit_kerja);
    //         }
    //     }

    //     // filter jenjab dan status pegawai
    //     if ($status_pegawai != null && $status_pegawai != "") {
    //         $employee = $employee->where('tb_01.status_pegawai', [$status_pegawai]);
    //     }

    //     if ($id_jenjab != null && $id_jenjab != "") {
    //         $employee = $employee->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
    //     }

    //     $employee = $employee->get();

    //     // Generate PDF
    //     $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.pelaksana_golongan', ['employee' => $employee]);
    //     return $pdf->download('executiveSummary.pdf');
    // }
    public function pdfPelaksanaGolongan(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);

        $data = StatistikASNRepository::getPDFQuery($request)
            ->whereHas('riwayatJabatan', function ($query) use ($param) {
                $query->where('isakhir', 1)
                    ->when(!empty($idjabfung), function ($query) use ($param) {
                        $query->where('id_jabatan', $param['idjabfungum']);
                    });
            })
            ->golongan($param['golongan_id'])
            ->get();
        
        $pdf = PDF::loadView('ExecutiveSummary::statistik-asn.print', ['employee' => $data]);
        return $pdf->download('executiveSummary.pdf');
    }

    // statistik asn pensiun
    public function datatablePensiunJabatanPertahun(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jenjab', 'a_jenjab.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.flag', '=', 1);
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfungum.idjabfungum')
                    ->where('a_jabfungum.flag', '=', 1);
            })
            ->select(
                'a_jenjab.jenjab',
                'a_jenjab.idjenjab',
                DB::raw("
                        CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ) AS tanggal_pensiun
                    "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(if(r_jabatan.id_jenis_jabatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(if(year(CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ))=year(now()),1,0)) AS 'h1'"),
                DB::raw("SUM(IF(year(CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ))=year(now())+1,1,0)) AS 'h2'"),
                DB::raw("SUM(IF(year(CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ))=year(now())+2,1,0)) AS 'h3'")
            )
            ->havingRaw('YEAR(tanggal_pensiun) >= YEAR(now())')
            ->havingRaw('YEAR(tanggal_pensiun) <= YEAR(now()) + 3')
            ->groupBy('r_jabatan.id_jenis_jabatan')
            ->orderBy('r_jabatan.id_jenis_jabatan');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPensiunJabatanPertahun(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jenjab', 'a_jenjab.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.flag', '=', 1);
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfungum.idjabfungum')
                    ->where('a_jabfungum.flag', '=', 1);
            })
            ->select(
                'a_jenjab.jenjab',
                DB::raw("
                        CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ) AS tanggal_pensiun
                    "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(if(r_jabatan.id_jenis_jabatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(if(year(CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ))=year(now()),1,0)) AS 'h1'"),
                DB::raw("SUM(IF(year(CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ))=year(now())+1,1,0)) AS 'h2'"),
                DB::raw("SUM(IF(year(CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ))=year(now())+2,1,0)) AS 'h3'")
            )
            ->havingRaw('YEAR(tanggal_pensiun) >= YEAR(now())')
            ->havingRaw('YEAR(tanggal_pensiun) <= YEAR(now()) + 3')
            ->groupBy('r_jabatan.id_jenis_jabatan')
            ->orderBy('r_jabatan.id_jenis_jabatan');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfPensiunJabatanPertahun(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $jenjab_pensiun = $request->input('jenjab_pensiun');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jenjab', 'a_jenjab.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.flag', '=', 1);
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfungum.idjabfungum')
                    ->where('a_jabfungum.flag', '=', 1);
            })
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->select(
                'b.id',
                'b.nip',
                'b.tempat_lahir',
                'b.tanggal_lahir',
                'b.nomor_kartu_asn',
                'a_skpd.path',
                'r_jabatan.jabatan',
                'c.masa_kerja_bulan',
                'c.masa_kerja_tahun',
                'kepangkatan.name',
                'kepangkatan.pangkat',
                'c.tmt_sk',
                'a_skpd.path',
                'r_jabatan.tmt_jabatan',
                DB::raw('CONCAT(b.gelar_depan,IF(LENGTH(b.gelar_depan)>0," ",""),b.nama,IF(LENGTH(b.gelar_belakang)>0,", ",""),b.gelar_belakang) as namalengkap'),
                DB::raw("
                        CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ) AS tanggal_pensiun
                    "),
                DB::raw("
                        DATE_FORMAT(
                            FROM_DAYS(
                                TO_DAYS(NOW()) - TO_DAYS(b.tanggal_lahir)
                            ),
                            '%Y%m'
                        ) + 0 AS usia
                    ")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->where('r_jabatan.id_jenis_jabatan', $jenjab_pensiun)
            ->having(DB::raw('YEAR(tanggal_pensiun)'), $year);

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();
        // dd($data);

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.pensiun_pertahun', ['employee' => $data, 'year' => $year]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function datatablePensiunJabatanPerbulan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jenjab', 'a_jenjab.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.flag', '=', 1);
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfungum.idjabfungum')
                    ->where('a_jabfungum.flag', '=', 1);
            })
            ->select(
                'b.nip',
                'b.nama',
                'a_jenjab.idjenjab',
                'a_jenjab.jenjab',
                DB::raw("
                        CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ) AS tanggal_pensiun
                    "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(if(r_jabatan.id_jenis_jabatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-01-01'),1,0)) AS 'jan'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-02-01'),1,0)) AS 'feb'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-03-01'),1,0)) AS 'mar'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-04-01'),1,0)) AS 'apr'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-05-01'),1,0)) AS 'mei'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-06-01'),1,0)) AS 'jun'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-07-01'),1,0)) AS 'jul'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-08-01'),1,0)) AS 'ags'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-09-01'),1,0)) AS 'sep'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-10-01'),1,0)) AS 'okt'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-11-01'),1,0)) AS 'nov'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-12-01'),1,0)) AS 'des'")
            )
            ->havingRaw('YEAR(tanggal_pensiun) = YEAR(now())')
            ->groupBy('r_jabatan.id_jenis_jabatan')
            ->orderBy('r_jabatan.id_jenis_jabatan');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function grafikPensiunJabatanPerbulan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');
        $month = $request->input('month');
        $year = $request->input('year');

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jenjab', 'a_jenjab.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.flag', '=', 1);
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfungum.idjabfungum')
                    ->where('a_jabfungum.flag', '=', 1);
            })
            ->select(
                'a_jenjab.jenjab',
                DB::raw("
                        CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ) AS tanggal_pensiun
                    "),
                DB::raw("COUNT(*) AS 'total'"),
                DB::raw("SUM(if(r_jabatan.id_jenis_jabatan='',1,0)) AS 'kosong'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-01-01'),1,0)) AS 'jan'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-02-01'),1,0)) AS 'feb'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-03-01'),1,0)) AS 'mar'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-04-01'),1,0)) AS 'apr'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-05-01'),1,0)) AS 'mei'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-06-01'),1,0)) AS 'jun'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-07-01'),1,0)) AS 'jul'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-08-01'),1,0)) AS 'ags'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-09-01'),1,0)) AS 'sep'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-10-01'),1,0)) AS 'okt'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-11-01'),1,0)) AS 'nov'"),
                DB::raw("SUM(if(CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    b.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    )=concat(year(now()),'-12-01'),1,0)) AS 'des'")
            )
            ->havingRaw('YEAR(tanggal_pensiun) = YEAR(now())')
            ->groupBy('r_jabatan.id_jenis_jabatan')
            ->orderBy('r_jabatan.id_jenis_jabatan');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        return JsonResponseHandler::setResult($data->get())->send();
    }

    public function pdfPensiunJabatanPerbulan(Request $request)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $status_pegawai = $request->input('status_pegawai');
        $id_jenjab = $request->input('id_jenjab');

        $jenjab_pensiun = $request->input('jenjab_pensiun');
        $bulan_id = $request->input('bulan_id');

        // dd($request->all());

        $data = DB::table('tb_01 as b')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('b.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('a_jenjab', 'a_jenjab.idjenjab', '=', 'r_jabatan.id_jenis_jabatan')
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.flag', '=', 1);
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfungum.idjabfungum')
                    ->where('a_jabfungum.flag', '=', 1);
            })
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('b.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('b.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('b.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('b.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('b.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->join('r_kepangkatan as c', function ($join) {
                $join
                    ->on('b.id', '=', 'c.id_pegawai')
                    ->where('c.isakhir', '=', 1);
            })
            ->join('kepangkatan', 'c.id_kepangkatan', '=', 'kepangkatan.id')
            ->select(
                'b.id',
                'b.nip',
                'b.tempat_lahir',
                'b.tanggal_lahir',
                'b.nomor_kartu_asn',
                'a_skpd.path',
                'r_jabatan.jabatan',
                'c.masa_kerja_bulan',
                'c.masa_kerja_tahun',
                'kepangkatan.name',
                'kepangkatan.pangkat',
                'c.tmt_sk',
                'a_skpd.path',
                'r_jabatan.tmt_jabatan',
                DB::raw('CONCAT(b.gelar_depan,IF(LENGTH(b.gelar_depan)>0," ",""),b.nama,IF(LENGTH(b.gelar_belakang)>0,", ",""),b.gelar_belakang) as namalengkap'),
                DB::raw("
                        CONCAT(
                            LEFT(
                                DATE_ADD(
                                    DATE_ADD(
                                        b.tanggal_lahir,
                                        INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                    ),
                                    INTERVAL 1 MONTH
                                ),
                                8
                            ),
                            '01'
                        ) AS tanggal_pensiun
                    "),
                DB::raw("
                        DATE_FORMAT(
                            FROM_DAYS(
                                TO_DAYS(NOW()) - TO_DAYS(b.tanggal_lahir)
                            ),
                            '%Y%m'
                        ) + 0 AS usia
                    ")
            )
            ->whereNotIn('b.kedudukan_pegawai', [99, 21])
            ->where('r_jabatan.id_jenis_jabatan', $jenjab_pensiun)
            ->having(DB::raw('MONTH(tanggal_pensiun)'), $bulan_id)
            ->havingRaw('YEAR(tanggal_pensiun) = YEAR(now())');

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit)
                    ->where('b.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('b.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja)
                    ->where('b.id_induk_upt', $pecah_induk_upt)
                    ->where('b.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('b.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($status_pegawai != null && $status_pegawai != "") {
            $data = $data->where('b.status_pegawai', [$status_pegawai]);
        }

        if ($id_jenjab != null && $id_jenjab != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', [$id_jenjab]);
        }

        $data = $data->get();
        // dd($data);

        $nama_bulan = [
            '01' => 'Januari',
            '02' => 'Februari',
            '03' => 'Maret',
            '04' => 'April',
            '05' => 'Mei',
            '06' => 'Juni',
            '07' => 'Juli',
            '08' => 'Agustus',
            '09' => 'September',
            '10' => 'Oktober',
            '11' => 'November',
            '12' => 'Desember'
        ];

        $nama_bulan_judul = $nama_bulan[$bulan_id];

        // Generate PDF
        $pdf = PDF::loadView('ExecutiveSummary::pdf-statistik.pensiun_perbulan', ['employee' => $data, 'nama_bulan' => $nama_bulan_judul]);
        return $pdf->download('executiveSummary.pdf');
    }

    public function printStatistik(Request $request)
    {
        $param = StatistikASNRepository::handleQueryParam($request);
    }
}
