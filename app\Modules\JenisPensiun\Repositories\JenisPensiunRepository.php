<?php

namespace App\Modules\JenisPensiun\Repositories;

use App\Modules\JenisPensiun\Models\JenisPensiun;

class JenisPensiunRepository
{
    public static function datatable($per_page = 15)
    {
        $data = JenisPensiun::paginate($per_page);
        return $data;
    }
    public static function get($jenis_pensiun_id)
    {
        $jenis_pensiun = JenisPensiun::where('id', $jenis_pensiun_id)->first();
        return $jenis_pensiun;
    }
    public static function create($jenis_pensiun)
    {
        $jenis_pensiun = JenisPensiun::create($jenis_pensiun);
        return $jenis_pensiun;
    }

    public static function update($jenis_pensiun_id, $jenis_pensiun)
    {
        JenisPensiun::where('id', $jenis_pensiun_id)->update($jenis_pensiun);
        $jenis_pensiun = JenisPensiun::where('id', $jenis_pensiun_id)->first();
        return $jenis_pensiun;
    }

    public static function delete($jenis_pensiun_id)
    {
        $delete = JenisPensiun::where('id', $jenis_pensiun_id)->delete();
        return $delete;
    }
}
