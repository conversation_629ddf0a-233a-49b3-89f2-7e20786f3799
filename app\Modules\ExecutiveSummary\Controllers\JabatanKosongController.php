<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use App\Modules\UnitKerja\Models\UnitKerjaModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class JabatanKosongController extends Controller
{
    public function datatable(Request $request)
    {
        // dd($request->input('per_page'));
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = UnitKerjaModel::leftJoin('tb_01', 'a_skpd.plt_nip', '=', 'tb_01.nip')
            ->join('eselon', 'a_skpd.idesl', '=', 'eselon.id')
            ->join('kepangkatan as d', 'eselon.idgolrumin', '=', 'd.id')
            ->join('kepangkatan as e', 'eselon.idgolrumax', '=', 'e.id')
            ->select(
                'a_skpd.idskpd',
                'a_skpd.path AS skpd',
                'a_skpd.jab AS jab',
                'eselon.name AS eselon',
                DB::raw("d.name AS gol_min"),
                DB::raw("e.name AS gol_max"),
                'a_skpd.plt_nip AS plt_nip',
                DB::raw(
                    "CONCAT(IFNULL(tb_01.gelar_depan,''),' ',tb_01.nama,IF(tb_01.gelar_belakang IS NULL,'',CONCAT(', ',tb_01.gelar_belakang)))
                        AS nama
                    "
                ),
            )
            ->whereRaw('tb_01.nip IS NULL')
            ->whereUserHaveAccess('a_skpd');

        $data = $data->orderBy('a_skpd.idskpd', 'asc')
            ->paginate($per_page);

        return JsonResponseHandler::setResult($data)->send();
    }
}
