<?php

namespace App\Modules\EmployeeStatus\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\EmployeeStatus\Models\EmployeeStatus;
use App\Modules\EmployeeStatus\Repositories\EmployeeStatusRepository;
use App\Modules\EmployeeStatus\Requests\EmployeeStatusCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class EmployeeStatusController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('EmployeeStatus::index', ['permissions' => $permissions]);
    }

    public function get()
    {
        $data = EmployeeStatus::select('id', 'name')->get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = EmployeeStatusRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $employee_status_id)
    {
        $employee_status = EmployeeStatus::where('id', $employee_status_id)->first();
        return JsonResponseHandler::setResult($employee_status)->send();
    }

    public function create()
    {
        return view('EmployeeStatus::create');
    }

    public function store(EmployeeStatusCreateRequest $request)
    {
        $payload = $request->all();
        $employee_status = EmployeeStatusRepository::create($payload);
        return JsonResponseHandler::setResult($employee_status)->send();
    }

    public function show(Request $request, $id)
    {
        $employee_status = EmployeeStatusRepository::get($id);
        return JsonResponseHandler::setResult($employee_status)->send();
    }

    public function edit($id)
    {
        return view('EmployeeStatus::edit', ['employee_status_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $employee_status = EmployeeStatusRepository::update($id, $payload);
        return JsonResponseHandler::setResult($employee_status)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = EmployeeStatusRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
