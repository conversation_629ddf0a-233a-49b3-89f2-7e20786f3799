<?php

namespace App\Modules\GolonganRuang\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\GolonganRuang\Models\GolonganRuang;
use App\Modules\GolonganRuang\Repositories\GolonganRuangRepository;
use App\Modules\GolonganRuang\Requests\GolonganRuangCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class GolonganRuangController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('GolonganRuang::index', ['permissions' => $permissions]);
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = GolonganRuangRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $golongan_ruang_id)
    {
        $golongan_ruang = GolonganRuang::where('id', $golongan_ruang_id)->first();
        return JsonResponseHandler::setResult($golongan_ruang)->send();
    }

    public function create()
    {
        return view('GolonganRuang::create');
    }

    public function store(GolonganRuangCreateRequest $request)
    {
        $payload = $request->all();
        $golongan_ruang = GolonganRuangRepository::create($payload);
        return JsonResponseHandler::setResult($golongan_ruang)->send();
    }

    public function show(Request $request, $id)
    {
        $golongan_ruang = GolonganRuangRepository::get($id);
        return JsonResponseHandler::setResult($golongan_ruang)->send();
    }

    public function edit($id)
    {
        return view('GolonganRuang::edit', ['golongan_ruang_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $golongan_ruang = GolonganRuangRepository::update($id, $payload);
        return JsonResponseHandler::setResult($golongan_ruang)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = GolonganRuangRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }

    public function employeeDatatable(Request $request, $employeeId)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = GolonganRuangRepository::employeeDatatable($employeeId, $per_page);
        return JsonResponseHandler::setResult($data)->send();
    }
}
