<?php

namespace App\Http\Controllers;

use App\Handler\JsonResponseHandler;
use Illuminate\Http\Request;

class AppController extends Controller
{
    public $module_name;
    public $repository;
    
    public function __construct($module_name, $repository)
    {
        $this->module_name = $module_name;
    }

    public function index(Request $request)
    {
        return view($this->module_name . '::index');
    }

    public function datatable(Request $request)
    {
        $employees = $this->repository->datatable($request->all());
        return JsonResponseHandler::setResult($employees)->send();
    }

    public function create()
    {
        return view($this->module_name . '::create');
    }

    public function show($id)
    {
        $employee = $this->repository->get($id);
        return JsonResponseHandler::setResult($employee)->send();
    }

    public function edit($id)
    {
        return view($this->module_name . '::edit');
    }

    public function destroy($id)
    {
        $delete = $this->repository->delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
