@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="employee-status">
    <default-datatable title="EmployeeStatus" url="{!! url('employee-status') !!}" :headers="headers" :can-add="{{ $permissions['create-employee_status'] }}" :can-edit="{{ $permissions['update-employee_status'] }}" :can-delete="{{ $permissions['delete-employee_status'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'name',
        						text: 'name'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#employee-status');
</script>
@endsection