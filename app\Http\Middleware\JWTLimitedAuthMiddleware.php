<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Helpers\JWTHelper;
use App\Modules\User\Model\UserModel;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class JWTLimitedAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $token = $request->bearerToken();
        if($token == null) {
            $token = $request->input('token');
        }

        if (!$token) {
            return response()->json(['message' => 'Token not provided'], 401);
        }

        $userData = JWTHelper::validateLimitedToken($token);

        if($userData == null) {
            $userData = JWTHelper::validateToken($token);
        }

        if (!$userData) {
            return response()->json(['message' => 'Invalid token'], 401);
        }

        $user = UserModel::find($userData['user_id']);
        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        Auth::setUser($user);

        return $next($request);
    }
}
