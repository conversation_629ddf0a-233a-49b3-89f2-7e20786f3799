<?php

namespace App\Modules\ExecutiveSummary\Repositories;

use App\Exceptions\AppException;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\ExecutiveSummary\Constants\ValidityConstant;
use App\Modules\KeluargaAnak\Models\KeluargaAnakModel;
use App\Modules\KeluargaIstriSuami\Models\KeluargaIstriSuamiModel;
use App\Modules\KeluargaOrangTua\Models\KeluargaOrangTuaModel;
use App\Modules\RiwayatPns\Models\RiwayatPnsModel;
use App\Modules\RiwayatCpns\Models\RiwayatCpnsModel;
use App\Modules\RiwayatPppk\Models\RiwayatPppkModel;
use App\Modules\RiwayatJabatan\Models\RiwayatJabatanModel;
use App\Modules\RiwayatPendidikan\Models\RiwayatPendidikanModel;
use App\Modules\RiwayatPendidikanNonFormal\Models\RiwayatPendidikanNonFormalModel;
use App\Modules\RiwayatKepangkatan\Models\RiwayatKepangkatanModel;
use App\Modules\RiwayatKgb\Models\RiwayatKgbModel;
use App\Modules\RiwayatKinerjaAsn\Models\RiwayatKinerjaAsnModel;
use App\Modules\KeluargaMertua\Models\KeluargaMertuaModel;
use App\Type\JsonResponseType;
use Illuminate\Support\Facades\Log;

class ValiditasRepository
{
    public static function getEmployeeValidityAndCompletion($employeeId)
    {
        $employeeObj = EmployeeModel::where('id', $employeeId)->first();
        Log::info("\nProcessing Employee Completion AND Validity for " . $employeeObj->nip);
        if ($employeeObj == null) {
            throw new AppException("Employee Not Found", JsonResponseType::ERROR, [], 400);
        }
        $employee = $employeeObj->toArray();
        $employeeSubmissionField = $employeeObj->getLastSubmissionIndexedField();

        // All Completion
        $completion = [];

        // Personal Completion
        $personalCompletion = self::getCompletion($employee, $employeeSubmissionField, $employee, ValidityConstant::PERSONAL_FIELD_TO_CHECK);
        $completion[] = [
            'label' => 'Data Pribadi',
            'percentage' => (array_reduce($personalCompletion, function ($carry, $item) {
                return $carry + ($item['is_complete'] ? 1 : 0);
            }, 0) / count($personalCompletion)) * 100,
            'percentage_validation' => (array_reduce($personalCompletion, function ($carry, $item) {
                return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
            }, 0) / count($personalCompletion)) * 100,
            'childs' => $personalCompletion
        ];

        // Pasangan
        if ($employee['status_pernikahan'] == '3') {
            $completion[] = [
                'label' => 'Pasangan Terakhir (Belum Menikah)',
                'percentage' => 100,
                'percentage_validation' => 100,
                'childs' => [],
                'dont_count' => true
            ];
        } else {
            $pasanganObject = KeluargaIstriSuamiModel::where('id_pegawai', $employeeId)->orderBy('id', 'DESC')->first();
            if ($pasanganObject == null) {
                $completion[] = [
                    'label' => 'Pasangan Terakhir',
                    'percentage' => 0,
                    'percentage_validation' => 0,
                    'childs' => []
                ];
            } else {
                $pasangan = $pasanganObject->toArray();
                $pasanganSubmissionField = $pasanganObject->getLastSubmissionIndexedField();

                $pasanganCompletion = self::getCompletion($pasangan, $pasanganSubmissionField, $employee, ValidityConstant::PASANGAN_FIELD_TO_CHECK);
                $completion[] = [
                    'label' => 'Pasangan Terakhir',
                    'percentage' => (array_reduce($pasanganCompletion, function ($carry, $item) {
                        return $carry + ($item['is_complete'] ? 1 : 0);
                    }, 0) / count($pasanganCompletion)) * 100,
                    'percentage_validation' => (array_reduce($pasanganCompletion, function ($carry, $item) {
                        return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                    }, 0) / count($pasanganCompletion)) * 100,
                    'childs' => $pasanganCompletion
                ];
            }
        }

        // Anak
        if ($employee['status_pernikahan'] == '3') {
            $completion[] = [
                'label' => 'Belum Memiliki Anak',
                'percentage' => 100,
                'percentage_validation' => 100,
                'childs' => [],
                'dont_count' => true
            ];
        } else {
            $anakObject = KeluargaAnakModel::where('id_pegawai', $employeeId)->get();
            if (empty($anakObject)) {
                $completion[] = [
                    'label' => 'Belum Memiliki Anak',
                    'percentage' => 100,
                    'percentage_validation' => 100,
                    'childs' => []
                ];
            } else {
                $completion[] = self::getAnakCompletionData($employee);
            }
        }

        //ortu (ayah)
        // Todo : Pisah Ayah IBU
        $ortuObject = KeluargaOrangTuaModel::where('id_pegawai', $employeeId)->where('status_orangtua', 1)
            ->orderBy('id', 'DESC')->first();
        if ($ortuObject == null) {
            $completion[] = [
                'label' => 'Orang Tua (Ayah)',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $ortu = $ortuObject->toArray();
            $ortuSubmissionField = $ortuObject->getLastSubmissionIndexedField();

            $ortuCompletion = self::getCompletion($ortu, $ortuSubmissionField, $employee, ValidityConstant::ORTU_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Orang Tua (Ayah)',
                'percentage' => (array_reduce($ortuCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($ortuCompletion)) * 100,
                'percentage_validation' => (array_reduce($ortuCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) ? $item['is_valid'] : 0);
                }, 0) / count($ortuCompletion)) * 100,
                'childs' => $ortuCompletion
            ];
        }

        //ortu (ibu)
        $ortuObject = KeluargaOrangTuaModel::where('id_pegawai', $employeeId)->where('status_orangtua', 2)
            ->orderBy('id', 'DESC')->first();
        if ($ortuObject == null) {
            $completion[] = [
                'label' => 'Orang Tua (Ibu)',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $ortu = $ortuObject->toArray();
            $ortuSubmissionField = $ortuObject->getLastSubmissionIndexedField();

            $ortuCompletion = self::getCompletion($ortu, $ortuSubmissionField, $employee, ValidityConstant::ORTU_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Orang Tua (Ibu)',
                'percentage' => (array_reduce($ortuCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($ortuCompletion)) * 100,
                'percentage_validation' => (array_reduce($ortuCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) ? $item['is_valid'] : 0);
                }, 0) / count($ortuCompletion)) * 100,
                'childs' => $ortuCompletion
            ];
        }

        //bapak mertua
        $mertuaObject = KeluargaMertuaModel::where('id_pegawai', $employeeId)
            ->where('status_mertua', 1)
            ->with('mertuaDariPasangan')
            ->first();
        if ($mertuaObject == null) {
            $completion[] = [
                'label' => 'Bapak Mertua',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $mertua = $mertuaObject->toArray();
            $mertuaSubmissionField = $mertuaObject->getLastSubmissionIndexedField();

            $mertuaCompletion = self::getCompletion($mertua, $mertuaSubmissionField, $employee, ValidityConstant::MERTUA_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Bapak Mertua',
                'percentage' => (array_reduce($mertuaCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($mertuaCompletion)) * 100,
                'percentage_validation' => (array_reduce($mertuaCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($mertuaCompletion)) * 100,
                'childs' => $mertuaCompletion
            ];
        }

        //ibu mertua
        $mertuaObject = KeluargaMertuaModel::where('id_pegawai', $employeeId)
            ->where('status_mertua', 2)
            ->with('mertuaDariPasangan')
            ->first();
        if ($mertuaObject == null) {
            $completion[] = [
                'label' => 'Ibu Mertua',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $mertua = $mertuaObject->toArray();
            $mertuaSubmissionField = $mertuaObject->getLastSubmissionIndexedField();

            $mertuaCompletion = self::getCompletion($mertua, $mertuaSubmissionField, $employee, ValidityConstant::MERTUA_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Ibu Mertua',
                'percentage' => (array_reduce($mertuaCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($mertuaCompletion)) * 100,
                'percentage_validation' => (array_reduce($mertuaCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($mertuaCompletion)) * 100,
                'childs' => $mertuaCompletion
            ];
        }


        //cpns
        // if ($employee['status_pegawai'] == '1') {
        $cpnsObject = RiwayatCpnsModel::where('id_pegawai', $employeeId)->first();
        if ($cpnsObject == null) {
            $completion[] = [
                'label' => 'CPNS',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $cpns = $cpnsObject->toArray();
            $cpnsSubmissionField = $cpnsObject->getLastSubmissionIndexedField();

            $cpnsCompletion = self::getCompletion($cpns, $cpnsSubmissionField, $employee, ValidityConstant::CPNS_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'CPNS',
                'percentage' => (array_reduce($cpnsCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($cpnsCompletion)) * 100,
                'percentage_validation' => (array_reduce($cpnsCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($cpnsCompletion)) * 100,
                'childs' => $cpnsCompletion
            ];
        }
        // }

        //PNS
        if ($employee['status_pegawai'] == '2') {
            $pnsObject = RiwayatPnsModel::where('id_pegawai', $employeeId)->first();
            if ($pnsObject == null) {
                $completion[] = [
                    'label' => 'PNS',
                    'percentage' => 0,
                    'percentage_validation' => 0,
                    'childs' => []
                ];
            } else {
                $pns = $pnsObject->toArray();
                $pnsSubmissionField = $pnsObject->getLastSubmissionIndexedField();

                $pnsCompletion = self::getCompletion($pns, $pnsSubmissionField, $employee, ValidityConstant::PNS_FIELD_TO_CHECK);
                $completion[] = [
                    'label' => 'PNS',
                    'percentage' => (array_reduce($pnsCompletion, function ($carry, $item) {
                        return $carry + ($item['is_complete'] ? 1 : 0);
                    }, 0) / count($pnsCompletion)) * 100,
                    'percentage_validation' => (array_reduce($pnsCompletion, function ($carry, $item) {
                        return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                    }, 0) / count($pnsCompletion)) * 100,
                    'childs' => $pnsCompletion
                ];
            }
        }

        //pppk
        if ($employee['status_pegawai'] == '3') {
            $pppkObject = RiwayatPppkModel::where('id_pegawai', $employeeId)->first();
            if ($pppkObject == null) {
                $completion[] = [
                    'label' => 'PPPK',
                    'percentage' => 0,
                    'percentage_validation' => 0,
                    'childs' => []
                ];
            } else {
                $pppk = $pppkObject->toArray();
                $pppkSubmissionField = $pppkObject->getLastSubmissionIndexedField();

                $pppkCompletion = self::getCompletion($pppk, $pppkSubmissionField, $employee, ValidityConstant::PPPK_FIELD_TO_CHECK);
                $completion[] = [
                    'label' => 'PPPK',
                    'percentage' => (array_reduce($pppkCompletion, function ($carry, $item) {
                        return $carry + ($item['is_complete'] ? 1 : 0);
                    }, 0) / count($pppkCompletion)) * 100,
                    'percentage_validation' => (array_reduce($pppkCompletion, function ($carry, $item) {
                        return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                    }, 0) / count($pppkCompletion)) * 100,
                    'childs' => $pppkCompletion
                ];
            }
        }

        //jabatan terakhir
        $jabatanObject = RiwayatJabatanModel::where('id_pegawai', $employeeId)->first();
        if ($jabatanObject == null) {
            $completion[] = [
                'label' => 'Jabatan Terakhir',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $jabatan = $jabatanObject->toArray();
            $jabatanSubmissionField = $jabatanObject->getLastSubmissionIndexedField();

            $jabatanCompletion = self::getCompletion($jabatan, $jabatanSubmissionField, $employee, ValidityConstant::JABATAN_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Jabatan Terakhir',
                'percentage' => (array_reduce($jabatanCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($jabatanCompletion)) * 100,
                'percentage_validation' => (array_reduce($jabatanCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($jabatanCompletion)) * 100,
                'childs' => $jabatanCompletion
            ];
        }

        //pendidikan formal
        $pendidikanFormalObject = RiwayatPendidikanModel::where('id_pegawai', $employeeId)->first();
        if ($pendidikanFormalObject == null) {
            $completion[] = [
                'label' => 'Pendidikan Formal',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $pendidikan_formal = $pendidikanFormalObject->toArray();
            $pendidikanFormalSubmissionField = $pendidikanFormalObject->getLastSubmissionIndexedField();

            $pendidikanFormalCompletion = self::getCompletion($pendidikan_formal, $pendidikanFormalSubmissionField, $employee, ValidityConstant::PENDIDIKAN_FORMAL_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Pendidikan Formal',
                'percentage' => (array_reduce($pendidikanFormalCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($pendidikanFormalCompletion)) * 100,
                'percentage_validation' => (array_reduce($pendidikanFormalCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($pendidikanFormalCompletion)) * 100,
                'childs' => $pendidikanFormalCompletion
            ];
        }

        //pendidikan non formal terakhir
        $pendidikanNonFormalObject = RiwayatPendidikanNonFormalModel::where('id_pegawai', $employeeId)
            ->orderBy('tahun_diklat', 'desc')->first();

        if ($pendidikanNonFormalObject == null) {
            $completion[] = [
                'label' => 'Pendidikan Non Formal Terakhir',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $pendidikan_nonformal = $pendidikanNonFormalObject->toArray();
            $pendidikanNonFormalSubmissionField = $pendidikanNonFormalObject->getLastSubmissionIndexedField();

            $pendidikanNonFormalCompletion = self::getCompletion($pendidikan_nonformal, $pendidikanNonFormalSubmissionField, $employee, ValidityConstant::PENDIDIKAN_NON_FORMAL_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Pendidikan Non Formal Terakhir',
                'percentage' => (array_reduce($pendidikanNonFormalCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($pendidikanNonFormalCompletion)) * 100,
                'percentage_validation' => (array_reduce($pendidikanNonFormalCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($pendidikanNonFormalCompletion)) * 100,
                'childs' => $pendidikanNonFormalCompletion
            ];
        }

        //pangkat terakhir
        $pangkatObject = RiwayatKepangkatanModel::where('id_pegawai', $employeeId)
            ->where('isakhir', 1)
            ->with(['pegawai', 'pendidikan'])
            ->first();

        if ($pangkatObject == null) {
            $completion[] = [
                'label' => 'Pangkat Terakhir',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $pangkat = $pangkatObject->toArray();
            $pangkatSubmissionField = $pangkatObject->getLastSubmissionIndexedField();

            $pangkatCompletion = self::getCompletion($pangkat, $pangkatSubmissionField, $employee, ValidityConstant::PANGKAT_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Pangkat Terakhir',
                'percentage' => (array_reduce($pangkatCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($pangkatCompletion)) * 100,
                'percentage_validation' => (array_reduce($pangkatCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($pangkatCompletion)) * 100,
                'childs' => $pangkatCompletion
            ];
        }

        //kgb terakhir
        $kgbObject = RiwayatKgbModel::where('id_pegawai', $employeeId)->first();
        if ($kgbObject == null) {
            $completion[] = [
                'label' => 'Gaji Berkala Terakhir',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $kgb = $kgbObject->toArray();
            $kgbSubmissionField = $kgbObject->getLastSubmissionIndexedField();

            $kgbCompletion = self::getCompletion($kgb, $kgbSubmissionField, $employee, ValidityConstant::KGB_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Gaji Berkala Terakhir',
                'percentage' => (array_reduce($kgbCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($kgbCompletion)) * 100,
                'percentage_validation' => (array_reduce($kgbCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($kgbCompletion)) * 100,
                'childs' => $kgbCompletion
            ];
        }

        //kinerja 2 tahun terakhir
        $currentYear = now()->year; //tahun sekarang
        $twoYearsAgo = $currentYear - 2;
        $kinerjaObject = RiwayatKinerjaAsnModel::where('id_pegawai', $employeeId)
            ->whereBetween('tahun',  [$twoYearsAgo, $currentYear])
            ->first();
        if ($kinerjaObject == null) {
            $completion[] = [
                'label' => 'Kinerja 2 Tahun Terakhir',
                'percentage' => 0,
                'percentage_validation' => 0,
                'childs' => []
            ];
        } else {
            $kinerja = $kinerjaObject->toArray();
            $kinerjaSubmissionField = $kinerjaObject->getLastSubmissionIndexedField();

            $kinerjaCompletion = self::getCompletion($kinerja, $kinerjaSubmissionField, $employee, ValidityConstant::EKIN_FIELD_TO_CHECK);
            $completion[] = [
                'label' => 'Kinerja 2 Tahun Terakhir',
                'percentage' => (array_reduce($kinerjaCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($kinerjaCompletion)) * 100,
                'percentage_validation' => (array_reduce($kinerjaCompletion, function ($carry, $item) {
                    return $carry + (isset($item['is_valid']) && ($item['is_valid']) ? 1 : 0);
                }, 0) / count($kinerjaCompletion)) * 100,
                'childs' => $kinerjaCompletion
            ];
        }

        $totalCompletionPercentage = 0;
        $totalValidationPercentage = 0;
        $totalCountedItems = 0;
        foreach ($completion as $item) {
            if (!isset($item['dont_count'])) {
                $totalCompletionPercentage += !empty($item['percentage']) ? $item['percentage'] : 0;
                $totalValidationPercentage += !empty($item['percentage_validation']) ? $item['percentage_validation'] : 0;
                $totalCountedItems += 1;
            }
        }

        $data = [
            'average_completion_percentage' =>  $totalCompletionPercentage / count($completion),
            'average_validation_ercentage' =>  $totalValidationPercentage / count($completion),
            'items' => $completion
        ];

        EmployeeModel::where('id', $employeeId)->update([
            'validity_percentage' => $data['average_validation_ercentage'],
            'completion_percentage' => $data['average_completion_percentage']
        ]);

        return $data;
    }

    public static function getAnakCompletionData($employee)
    {
        $children = KeluargaAnakModel::where('id_pegawai', $employee['id'])->get();
        if (empty($children)) {
            return [
                'label' => 'Belum Memiliki Anak',
                'percentage' => 100,
                'percentage_validation' => 100,
                'childs' => []
            ];
        }

        $payload =  [
            'label' => 'Anak',
            'sub' => []
        ];

        foreach ($children as $index => $anak) {
            $anakSubmissionField = $anak->getLastSubmissionIndexedField();
            $anakCompletion = self::getCompletion($anak, $anakSubmissionField, $employee, ValidityConstant::ANAK_FIELD_TO_CHECK);
            $payload['sub'][] = [
                'label' => 'Anak ke-' . $index + 1,
                'percentage' => (array_reduce($anakCompletion, function ($carry, $item) {
                    return $carry + ($item['is_complete'] ? 1 : 0);
                }, 0) / count($anakCompletion)) * 100,
                'percentage_validation' => (array_reduce($anakCompletion, function ($carry, $item) {
                    return $carry + ($item['is_valid'] ? 1 : 0);
                }, 0) / count($anakCompletion)) * 100,
                'childs' => $anakCompletion
            ];
        }

        $sumOfPercentage = array_reduce($payload['sub'], function ($carry, $item) {
            return $carry + $item['percentage'];
        }, 0);
        $sumOfPercentageValidation = array_reduce($payload['sub'], function ($carry, $item) {
            return $carry + $item['percentage_validation'];
        }, 0);
        $subCount = count($payload['sub']);
        $payload['percentage'] = $subCount > 0 ? $sumOfPercentage / $subCount : 0;
        $payload['percentage_validation'] = $subCount > 0 ? $sumOfPercentageValidation / $subCount : 0;

        return $payload;
    }

    public static function getCompletion($item, $submissionField, $employee, $fieldToChecks)
    {
        $completion = array_map(function ($field) use ($item, $submissionField, $employee, $fieldToChecks) {
            return [
                'label' =>  $fieldToChecks[$field],
                'name' => $field,
                'value' => self::valueResolver($item, $field),
                'is_complete' => self::checkIsFieldFilled($item, $field),
                'is_valid' => $employee['is_validate'] && self::checkIsFieldFilled($item, $field) ? 1 : self::checkIsFieldValid($submissionField, $field)
            ];
        }, array_keys($fieldToChecks));

        return $completion;
    }

    public static function valueResolver($data, $field)
    {
        return isset($data[$field]) ? $data[$field] : null;
    }

    public static function checkIsFieldFilled($data, $fields)
    {
        $fields = explode(",", $fields);
        $status = true;
        foreach ($fields as $field) {
            if (!isset($data[$field])) {
                $status =  false;
                continue;
            }
            if ($data[$field] == null) {
                $status =  false;
                continue;
            }
            if ($data[$field] == '') {
                $status =  false;
                continue;
            }
        }
        return $status;
    }

    public static function checkIsFieldValid($data, $fields)
    {
        $fields = explode(",", $fields);
        $status = true;

        foreach ($fields as $field) {
            if (!isset($data[$field])) {
                $status = false;
                continue;
            }
            if ($data[$field] == null) {
                $status = false;
                continue;
            }
            if ($data[$field] == '') {
                $status = false;
                continue;
            }
            if (!isset($data[$field]['status'])) {
                $status = false;
                continue;
            }
            if ($data[$field]['status'] == null) {
                $status = false;
                continue;
            }
            if ($data[$field]['status'] == '') {
                $status = false;
                continue;
            }
            if ($data[$field]['status'] != '1') {
                $status = false;
                continue;
            }
        }

        return $status;
    }
}
