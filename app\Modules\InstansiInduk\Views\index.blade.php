@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="instansi-induk">
    <default-datatable title="InstansiInduk" url="{!! url('instansi-induk') !!}" :headers="headers" :can-add="{{ $permissions['create-instansi_induk'] }}" :can-edit="{{ $permissions['update-instansi_induk'] }}" :can-delete="{{ $permissions['delete-instansi_induk'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'id_instansi_induk',
        						text: 'id'
    					},    
					{
        						value: 'instansi_induk',
        						text: 'nama instansi'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#instansi-induk');
</script>
@endsection