<?php

namespace App\Modules\ChildStatus\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\ChildStatus\Models\ChildStatus;
use App\Modules\ChildStatus\Repositories\ChildStatusRepository;
use App\Modules\ChildStatus\Requests\ChildStatusCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class ChildStatusController extends Controller
{
    public function index(Request $request)
    {
        $child_status = ChildStatus::get();
        return JsonResponseHandler::setResult($child_status)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = ChildStatusRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $child_status_id)
    {
        $child_status = ChildStatus::where('id', $child_status_id)->first();
        return JsonResponseHandler::setResult($child_status)->send();
    }

    public function create()
    {
        return view('ChildStatus::create');
    }

    public function store(ChildStatusCreateRequest $request)
    {
        $payload = $request->all();
        $child_status = ChildStatusRepository::create($payload);
        return JsonResponseHandler::setResult($child_status)->send();
    }

    public function show(Request $request, $id)
    {
        $child_status = ChildStatusRepository::get($id);
        return JsonResponseHandler::setResult($child_status)->send();
    }

    public function edit($id)
    {
        return view('ChildStatus::edit', ['child_status_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $child_status = ChildStatusRepository::update($id, $payload);
        return JsonResponseHandler::setResult($child_status)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = ChildStatusRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
