@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="agama">
    <default-datatable title="Agama" url="{!! url('agama') !!}" :headers="headers" :can-add="{{ $permissions['create-agama'] }}" :can-edit="{{ $permissions['update-agama'] }}" :can-delete="{{ $permissions['delete-agama'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'agama',
        						text: 'agama'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#agama');
</script>
@endsection