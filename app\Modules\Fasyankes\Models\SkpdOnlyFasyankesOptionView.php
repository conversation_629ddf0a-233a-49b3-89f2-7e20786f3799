<?php

namespace App\Modules\Fasyankes\Models;
    
use Illuminate\Database\Eloquent\Model;
use App\Modules\Fasyankes\Models\Fasyankes;
use Illuminate\Support\Facades\DB;

class SkpdOnlyFasyankesOptionView extends Model
{
    use \Awobaz\Compoships\Compoships;

    protected $table = 'a_skpd_only_fasyankes_option_view';
    protected $guarded = ['*'];
    protected $primaryKey = null;
    public $incrementing = false;

    protected static function booted()
    {
        static::creating(function () {
            throw new \Exception("Insert not allowed on this model.");
        });

        static::updating(function () {
            throw new \Exception("Update not allowed on this model.");
        });

        static::deleting(function () {
            throw new \Exception("Delete not allowed on this model.");
        });
    }

    public function subSubUnitfasyankes()
    {
        return $this->hasMany(
            Fasyankes::class, 
            ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit'], 
            ['id_unit_kerja', 'id_induk_upt', 'id_sub_unit']
        );
    }

    public function scopeActive($query)
    {
        return $query->whereRaw("
            flag = 1 AND
            id_induk_upt = '00' AND
            id_sub_unit  = '00' AND
            id_sub_sub_unit  = '00' AND
            id_sub_sub_sub_unit = '00'
        ");
    }

    public function scopeOnlyAllowedIdEselonJabatan($query)
    {
        $idEselon = DB::table('a_allowed_id_eselon_unor_skpd')
            ->pluck('id_eselon')
            ->toArray();

        return $query->whereIn('id_eselon', $idEselon);
    }

    // public function unitKerjaFasyankes()
    // {
    //     return $this->hasOne(UnitKerjaModel::cla)
    // }
}