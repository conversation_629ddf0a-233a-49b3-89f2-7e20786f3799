<?php

namespace App\Modules\Gender\Repositories;

use App\Modules\Gender\Models\Gender;

class GenderRepository
{
    public static function datatable($per_page = 15)
    {
        $data = Gender::paginate($per_page);
        return $data;
    }
    public static function get($gender_id)
    {
        $gender = Gender::where('id', $gender_id)->first();
        return $gender;
    }
    public static function create($gender)
    {
        $gender = Gender::create($gender);
        return $gender;
    }

    public static function update($gender_id, $gender)
    {
        Gender::where('id', $gender_id)->update($gender);
        $gender = Gender::where('id', $gender_id)->first();
        return $gender;
    }

    public static function delete($gender_id)
    {
        $delete = Gender::where('id', $gender_id)->delete();
        return $delete;
    }
}
