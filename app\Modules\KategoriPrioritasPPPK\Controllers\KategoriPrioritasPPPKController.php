<?php

namespace App\Modules\KategoriPrioritasPPPK\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\KategoriPrioritasPPPK\Repositories\KategoriPrioritasPPPKRepository;
use App\Modules\KategoriPrioritasPPPK\Models\KategoriPrioritasPPPK;
use App\Modules\KategoriPrioritasPPPK\Requests\KategoriPrioritasPPPKCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class KategoriPrioritasPPPKController extends Controller
{
    public function index(Request $request)
    {
        $data = KategoriPrioritasPPPK::select('id', 'kategori')->get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = KategoriPrioritasPPPKRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('KategoriPrioritasPPPK::create');
    }

    public function store(KategoriPrioritasPPPKCreateRequest $request)
    {
        $payload = $request->all();
        $kategori_prioritas_pppk = KategoriPrioritasPPPKRepository::create($payload);
        return JsonResponseHandler::setResult($kategori_prioritas_pppk)->send();
    }

    public function show(Request $request, $id)
    {
        $kategori_prioritas_pppk = KategoriPrioritasPPPKRepository::get($id);
        return JsonResponseHandler::setResult($kategori_prioritas_pppk)->send();
    }

    public function edit($id)
    {
        return view('KategoriPrioritasPPPK::edit', ['kategori_prioritas_pppk_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $kategori_prioritas_pppk = KategoriPrioritasPPPKRepository::update($id, $payload);
        return JsonResponseHandler::setResult($kategori_prioritas_pppk)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = KategoriPrioritasPPPKRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
