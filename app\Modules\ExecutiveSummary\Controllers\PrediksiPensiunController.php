<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\ExecutiveSummary\Models\PrediksiPensiunModel;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Exports\ExcelExport;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\Snappy\Facades\SnappyPdf as PDF;

class PrediksiPensiunController extends Controller
{
    public function datatable(Request $request)
    {
        $data = PrediksiPensiunModel::dataPensiun($request, true);

        return JsonResponseHandler::setResult($data)->send();
    }

    public function cetakPdf(Request $request){
        $data = PrediksiPensiunModel::dataPensiun($request, false);
        $pdf = PDF::loadView('ExecutiveSummary::pdf-format.prediksi_pensiun', ['data' => $data]);
        return $pdf->download('pensiun.pdf');
    }

    public function exportExcel(Request $request)
    {
        $data = PrediksiPensiunModel::dataPensiun($request, false);

        $data = $data->toArray();
        return Excel::download(new ExcelExport($data), 'pensiun.xlsx');
    }
}
