@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jenis-pensiun">
    <default-datatable title="JenisPensiun" url="{!! url('jenis-pensiun') !!}" :headers="headers" :can-add="{{ $permissions['create-jenis_pensiun'] }}" :can-edit="{{ $permissions['update-jenis_pensiun'] }}" :can-delete="{{ $permissions['delete-jenis_pensiun'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'name',
        						text: 'name'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jenis-pensiun');
</script>
@endsection