<?php

namespace App\Modules\Efile\Repositories;

use App\Exceptions\AppException;
use App\Type\JsonResponseType;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Sentry\Laravel\Integration;
use App\Handler\FileHandler;
use Illuminate\Support\Arr;

class EfileRepository
{
    private $baseUrl;
    private $username;
    private $password;
    private $timeout;
    private $accessToken;

    public function __construct()
    {
        $this->baseUrl = config('services.efile.baseUrl');
        $this->username = config('services.efile.username');
        $this->password = config('services.efile.password');
        $this->timeout = config('services.efile.timeout');
        // dd($this->baseUrl, $this->username, $this->password, $this->timeout);

        $accessToken = Cache::remember('efile_access_tokenn', 1, function () {
            $response = Http::withHeaders([
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])
                ->timeout($this->timeout)
                ->connectTimeout($this->timeout)
                ->asForm()
                ->post("{$this->baseUrl}/getToken", [
                    'user' => $this->username,
                    'pass' => $this->password,
                ]);

            if (!$response->successful()) {
                throw new AppException("Failed when retrieving efile access token", JsonResponseType::INTERNAL_SERVER_ERROR, [], 400);
            }
            return $response->json()['token'];
        });

        $this->accessToken = $accessToken;
        $baseUrl = $this->baseUrl;
        $timeout = $this->timeout;

        Http::macro('efile', function () use ($accessToken, $baseUrl, $timeout) {
            return Http::withHeaders([
                'Authorization' => $accessToken,
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])
                ->timeout($timeout)
                ->connectTimeout($timeout)
                ->baseUrl($baseUrl)->asForm();
        });
    }

    public function getFileByNipAndCodes($nip, $codes)
    {
        $response = Http::efile()
            ->timeout($this->timeout)
            ->connectTimeout($this->timeout)
            ->post("/getEfile", [
                'nip' => $nip,
                'kodes' => $codes
            ]);
        if ($response->failed()) {
            throw new AppException("Failed when retrieving efiles", JsonResponseType::INTERNAL_SERVER_ERROR, [], 400);
        }
        $efileData = @$response->json()['data'];

        if (!empty($efileData)) {
            foreach ($efileData as $key => $efile) {
                if ($efile['response'] == 'success') {
                    // cari iddok efile yang terbesar
                    if ($efile['riwayat'] == 0) {
                        if (count($efile['efile']) > 1) {
                            $iddokPluck = Arr::pluck($efile['efile'], 'iddok');
                            $iddokPluck = array_map('intval', $iddokPluck);
                            $iddok = max($iddokPluck);
         
                            foreach ($efile['efile'] as $file) {
                                if ($file['iddok'] == $iddok) {
                                    $efileData[$key]['efile'] = [];
                                    $efileData[$key]['efile'][0] = $file;
                                }
                            } 
                        }      
                    }
                 
                    foreach ($efileData[$key]['efile'] as $keyDok => $groupedEfile) {
                        $efileData[$key]['efile'][$keyDok]['modified_at'] 
                            = $groupedEfile['updated_at'] ?? $groupedEfile['created_at'];
                    }
                }
            }
        }

        return $efileData;
    }

    public function openFile($url)
    {
        $response = Http::withHeaders([
            'Authorization' => $this->accessToken
        ])->get($url);
        if (!$response->successful()) {
            throw new AppException("Failed when retrieving efiles", JsonResponseType::INTERNAL_SERVER_ERROR, [], 400);
        }
        return $response->body();
    }

    public function storeFile($nip, $code, $filepath, $oldRiwayatId, $old_id = null, int $combine = 0)
    {
        try {
            if (in_array($filepath, ['[object Object]', 'null', 'undefined'])) {
                return false;
            }

            $efileDetail = compact('nip', 'code', 'filepath', 'oldRiwayatId', 'old_id', 'combine');
            Log::debug("Receiveing Efile request:\nnip: {$nip}\ncode:{$code}\nfilepath={$filepath}\noldId={$oldRiwayatId}");

            if (!in_array($combine, [0, 1])) {
                throw new AppException(
                    '[EFILE ERROR] - Terjadi kesalahan di sisi server. <br><br>Error detail: Invalid combine efile code.', 
                    JsonResponseType::INTERNAL_SERVER_ERROR,
                    $efileDetail,
                    500
                );
            }

            $fileContent = Storage::readStream($filepath);
            if (empty($fileContent)) {
                Log::debug('Efile error - Filepath not found. Current filepath: ' . $filepath);
                Integration::captureUnhandledException(new AppException("Uploaded File not exist", JsonResponseType::NOT_FOUND, ['filepath' => $filepath]));

                return false;
                // throw new AppException(
                //     "[EFILE ERROR] - File usulan tidak ditemukan di server SIASN.",
                //     JsonResponseType::INTERNAL_SERVER_ERROR,
                //     $efileDetail,
                //     500
                // );
            }
    
            // dd($fileContent);
            $response = Http::withHeaders(['Authorization' => $this->accessToken])
                ->timeout($this->timeout)
                ->connectTimeout($this->timeout)
                ->attach(
                    'file',
                    $fileContent,
                )
                ->post("{$this->baseUrl}/addEfile", [
                    'nip' => $nip,
                    'kode' => $code,
                    'id_riwayat' => $oldRiwayatId,
                    'combine' => $combine
                ]);
            Log::debug("RESPONSE BODY : " . $response->body());
            Log::debug("RESPONSE JSON : " . json_encode($response->json()));
      
            if (!$response->successful() || $response->failed()) {
                throw new AppException("[EFILE ERROR] Failed when saving efile - ERROR RESPONSE", JsonResponseType::INTERNAL_SERVER_ERROR, $efileDetail, 500);
            }
            if ($response->json() == null) {
                // Log::debug($response->json());
                throw new AppException("[EFILE ERROR] Failed when saving efile - NO RESPONSE", JsonResponseType::INTERNAL_SERVER_ERROR, $efileDetail, 500);
            }
            if (empty($response->json()['data']) && !empty($response->json()['message'])) {
                throw new AppException("[EFILE ERROR] - " . $response->json()['message'], JsonResponseType::INTERNAL_SERVER_ERROR, $efileDetail, 500);
            }
            if (empty($response->json()['data'])) {
                throw new AppException("[EFILE ERROR] - Unknown Error From EFILE PROVIDER", JsonResponseType::INTERNAL_SERVER_ERROR, $efileDetail, 500);
            }
            if ($response->json()['response'] != 'success') {
                throw new AppException("[EFILE ERROR] - File Gagal Terupload ke Server Efile", JsonResponseType::INTERNAL_SERVER_ERROR, ['efile_response' => $response->json(), 'efile_detail' => $efileDetail], 500);
            }
            if (empty($response->json()['data']['uuid'])) {
                throw new AppException("[EFILE ERROR] - Efile <b>".$response->json()['data']['nama_dok']."</b> berhasil terupload, namun terjadi kesalahan pada server Efile . Harap hubungi admin efile (UUID null)", JsonResponseType::INTERNAL_SERVER_ERROR, ['efile_response' => $response->json(), 'efile_detail' => $efileDetail], 500);
            }
            // while (empty($response->json()['data']['uuid'])) {
            //     $this->storeFile($nip, $code, $filepath, $oldRiwayatId, $old_id);
            // }

            if (!empty($old_id)) {
                $this->deleteFile($nip, $code, $old_id);
            }

            return $response->json()['data'];
        }
        catch (AppException $e) {
            throw $e;
        }
    }

    public function deleteFile(string $nip, string $code, string $id_riwayat) 
    {
        $response = Http::withHeaders(['Authorization' => $this->accessToken])
                ->timeout($this->timeout)
                ->connectTimeout($this->timeout)
                ->asForm()
                ->post("{$this->baseUrl}/delEfile", [
                    'nip' => $nip, 
                    'kode' => $code,
                    'id_riwayat' => $id_riwayat
                ]);

        if ($response->json()['response'] == 'success') {
            Log::debug('Efile untuk NIP '.$nip.' dan fileCode '.$code.' BERHASIL dihapus. Current id riwayat = '.$id_riwayat);
        }
        else {
            Log::debug('Efile untuk NIP '.$nip.' dan fileCode '.$code.' GAGAL dihapus. Current id riwayat = '.$id_riwayat);
        }

        return $response->json();
    }
}
