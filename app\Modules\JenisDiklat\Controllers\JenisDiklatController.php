<?php

namespace App\Modules\JenisDiklat\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenisDiklat\Models\JenisDiklat;
use App\Modules\JenisDiklat\Repositories\JenisDiklatRepository;
use App\Modules\JenisDiklat\Requests\JenisDiklatCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenisDiklatController extends Controller
{
    public function index(Request $request)
    {
        $jenis_diklat = JenisDiklat::get();
        return JsonResponseHandler::setResult($jenis_diklat)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenisDiklatRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('JenisDiklat::create');
    }

    public function store(Request $request)
    {
        $payload = $request->all();
        $jenis_diklat = JenisDiklatRepository::create($payload);
        return JsonResponseHandler::setResult($jenis_diklat)->send();
    }

    public function show(Request $request, $id)
    {
        $jenis_diklat = JenisDiklatRepository::get($id);
        return JsonResponseHandler::setResult($jenis_diklat)->send();
    }

    public function byMetodeDiklat(Request $request, $id)
    {
        $jenis_diklat = JenisDiklatRepository::byMetodeDiklat($id);
        return JsonResponseHandler::setResult($jenis_diklat)->send();
    }

    public function edit($id)
    {
        return view('JenisDiklat::edit', ['jenis_diklat_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jenis_diklat = JenisDiklatRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jenis_diklat)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JenisDiklatRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
