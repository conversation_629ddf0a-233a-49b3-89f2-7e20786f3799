<?php
namespace App\Modules\DiklatStruktural;

use App\Modules\DiklatStruktural\Controllers\DiklatStrukturalController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/diklat-struktural')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [DiklatStrukturalController::class, 'index']);
    Route::get('/datatable', [DiklatStrukturalController::class, 'datatable'])->middleware('authorize:read-diklat_struktural');
    Route::get('/create', [DiklatStrukturalController::class, 'create'])->middleware('authorize:create-diklat_struktural');
    Route::post('/', [DiklatStrukturalController::class, 'store'])->middleware('authorize:create-diklat_struktural');
    Route::get('/{diklat_struktural_id}/detail', [DiklatStrukturalController::class, 'show'])->middleware('authorize:read-diklat_struktural');
    Route::get('/{diklat_struktural_id}/edit', [DiklatStrukturalController::class, 'edit'])->middleware('authorize:update-diklat_struktural');
    Route::patch('/{diklat_struktural_id}', [DiklatStrukturalController::class, 'update'])->middleware('authorize:update-diklat_struktural');
    Route::delete('/{diklat_struktural_id}', [DiklatStrukturalController::class, 'destroy'])->middleware('authorize:delete-diklat_struktural');
});