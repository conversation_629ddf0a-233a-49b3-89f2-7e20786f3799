<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class MastfotoExport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mastfoto:export';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export presence photo from mastfoto table in eps database to presencePhoto folder';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $mastfoto = DB::table('bkd_siasn_jateng.tb_01')
            ->join('eps.MASTFOTO as MASTFOTO', function ($join) {
                $join->on('tb_01.nip_lama', '=', 'MASTFOTO.NIP');
                    // ->orOn('tb_01.nip_lama', '=', 'MASTFOTO.NIP');
            })
            ->select(
                'tb_01.nip as tb_01_nip', 
                // 'tb_01.nip_lama as tb_01_nip_lama', 
                'tb_01.id as tb_01_id_pegawai',
                // 'MASTFOTO.nip as MASTFOTO_nip',
                'MASTFOTO.DATA as MASTFOTO_DATA'
            )
            ->get();
        
        $storage = Storage::disk('local');
        $exportCount = 1;
        $errorCount = 1;
        foreach ($mastfoto as $foto) {
            $save = $storage->put(
                'employee/presencePhoto/' . $foto->tb_01_id_pegawai, 
                $foto->MASTFOTO_DATA, //  blob files
            );
            if ($save == 1) {
                $this->info('Exported ' . $exportCount++ . ' photos. Current NIP: ' . $foto->tb_01_nip . '. Current ID Pegawai: ' . $foto->tb_01_id_pegawai);
            }
            else {
                $this->info($errorCount++ . ' Export failed. Current NIP: ' . $foto->tb_01_nip . '. Current ID Pegawai: ' . $foto->tb_01_id_pegawai);
            }
        }
    }
}