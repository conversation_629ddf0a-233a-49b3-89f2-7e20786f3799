<?php

namespace App\Modules\Employee\Controller;

use App\Http\Controllers\Controller;
use App\Handler\FileHandler;
use App\Handler\JsonResponseHandler;
use App\Type\JsonResponseType;
use Illuminate\Http\Request;
use App\Modules\User\Model\UserModel;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Employee\Model\EmployeePresencePhotoModel;
use App\Modules\Employee\Model\EmployeePresencePhotoTempModel;
use App\Modules\Employee\Model\EmployeeMastfotoEpsModel;
use App\Modules\Pdm\Repositories\PdmRepository;
use App\Modules\Employee\Repositories\PhotoRepository;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use App\Exceptions\AppException;

class PhotoController extends Controller
{
    // const PROFILE_PHOTO_DIR = 'employee/profilePhoto';
    // const PRESENCE_PHOTO_DIR = 'employee/presencePhoto';
    // const PRESENCE_PHOTO_PDM_DIR = 'submission/presencePhoto';

    public function getProfilePhoto($employeeID)
    {
        try {
            $path = PhotoRepository::PROFILE_PHOTO_DIR . '/' . $employeeID;
            $photo = FileHandler::getFile($path);
        }
        catch (AppException $e) {
            $photo = FileHandler::getDefaultPhoto();
        }
        return $photo;
    }

    public function uploadProfilePhoto(Request $request, $employeeID)
    {
        $file = $request->file('photo');
        FileHandler::store(
            file: $file,
            targetDir: PhotoRepository::PROFILE_PHOTO_DIR,
            fileName: $employeeID,
            allowedMime: 'image',
            isNeedExtension: false,
            allowedExtensions: []
        );
        return JsonResponseHandler::setResult('Foto Profil Berhasil Diupload')->send();
    }

    public function getPresencePhoto($employeeID)
    {
        try {
            $path = PhotoRepository::PRESENCE_PHOTO_DIR . '/' . $employeeID;
            $photo = FileHandler::getFile($path);
        }
        catch (AppException $e) {
            $photo = FileHandler::getDefaultPhoto();
        }
        return $photo;
    }

    public function getPdmPresencePhoto($employeeID, $tempId)
    {
        try {
            // $temp = PhotoRepository::getTemp($employeeID, 0, $tempId);
            // $usulanBaru = PhotoRepository::getTemp($employeeID, 0);
            // if (empty($temp) && !empty($usulanBaru)) {
            //     throw new AppException('');
            // }
            $path = PhotoRepository::PRESENCE_PHOTO_PDM_DIR . '/' . $employeeID;
            $photo = FileHandler::getFile($path);
        }
        catch (AppException $e) {
            $photo = FileHandler::getDefaultPhoto();
        }
        return $photo;
    }

    public function uploadPresencePhoto(Request $request, $employeeID)
    {
        DB::beginTransaction();
        try {
            $file = $request->file('photo');
            if (!$file) {
                throw new AppException('Foto Presensi belum diupload');
            }

            $extMime = FileHandler::validatePhotoAndGetMimeExt($file);
            $user = UserModel::with('roles')
                    ->where('id', Auth::user()->id)
                    ->first();
            $isAdmin = $user->isAdmin();

            $id = ['id_pegawai' => $employeeID];
            $payload = [
                'id_pegawai' => $employeeID,
                'mime' => $extMime['mimeType'],
                'user_id' => $user->id,
                'role_id' => $user->roles[0]->id,
            ];
    
            if ($isAdmin) {
                $payload['status'] = 1;
                EmployeePresencePhotoModel::updateOrCreate($id, $payload);
                
                $employee = EmployeeModel::find($employeeID);
                $mastfotoID = [
                    'NIP' => $employee->nip_lama ?? $employee->nip
                ];
                $mastfotoPayload = [
                    'NIP' => $employee->nip_lama ?? $employee->nip,
                    'MIMETYPE' => $extMime['ext'],
                    'DATA' => file_get_contents($file->getRealPath()),
                    'updated_at' => now()
                ];
                EmployeeMastfotoEpsModel::updateOrCreate($mastfotoID, $mastfotoPayload);
            }
            else {
                EmployeePresencePhotoModel::firstOrCreate($id, $payload)->doPdm($payload, 2);           
            }

            $path = $isAdmin 
                    ? PhotoRepository::PRESENCE_PHOTO_DIR 
                    : PhotoRepository::PRESENCE_PHOTO_PDM_DIR;
    
            FileHandler::store(
                file: $file,
                targetDir: $path,
                fileName: $employeeID,
                allowedMime: 'image',
                isNeedExtension: false,
                allowedExtensions: []
            );

            DB::commit();
            
            $message = $isAdmin
                        ? 'Foto Presensi Berhasil Diupload'
                        : 'Pengajuan PDM Foto Presensi Berhasil';
    
            return JsonResponseHandler::setMessage($message)
                ->setResult(true)
                ->send();
        }
        catch (AppException $e) {
            DB::rollBack();
            throw new AppException($e->getMessage());
        }
    }

    public function presencePhotoSubmissionStatus($employeeID)
    {
        $submission = PdmRepository::getLastSubmission($employeeID, 'presence_photo');
        $status = false;
        if (!empty($submission)) {
            $file = Storage::exists(PhotoRepository::PRESENCE_PHOTO_PDM_DIR . '/' . $employeeID);
            $status = !empty($submission) && $file;
        }
        $message = $status
                    ? 'Permohonan perubahan foto presensi sedang dalam proses verifikasi'
                    : 'Belum ada permohonan perubahan foto presensi';
        
        return JsonResponseHandler::setMessage($message)
            ->setResult($status)
            ->send();
    }

    public function presencePhotoPdmfield($employeeID, $tempId)
    {
        $tempId = PhotoRepository::getSubmissionTempId($employeeID, $tempId);

        $fieldList = (new EmployeePresencePhotoModel)->getPdmFieldDetail($tempId);
        foreach ($fieldList as $field) {
            $fieldList['remark'] = $field['remark'];
            if ($field['status'] == '1') {
                $fieldList['verification_status'] = 1;
                continue;
            }
            else if ($field['status'] == '2') {
                $fieldList['verification_status'] = 2;
                break;
            }
            else {
                $fieldList['verification_status'] = 0;
            }
        }
        return JsonResponseHandler::setResult($fieldList)->send();
    }

    public function presencePhotoSubmissionTimeline($employeeID, $tempId)
    {
        $tempId = PhotoRepository::getSubmissionTempId($employeeID, $tempId);

        $data = (new EmployeePresencePhotoModel)->getPdmTimeline($tempId);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function presencePhotoSubmissionCurrentVerificator($employeeID)
    {
        $tempId = PhotoRepository::getSubmissionTempId($employeeID, '');

        $verificator = (new EmployeePresencePhotoModel)->getCurrentAndNextVerificator($tempId);
        return JsonResponseHandler::setResult($verificator)->send();
    }

    public function presencePhotoSubmissionProcess(Request $request, $employeeID, $tempId)
    {
        $payload = $request->all();
        $verify = (new EmployeePresencePhotoModel)->processSubmission(
            $tempId, $payload['status'], $payload['remarkVerification'], $payload['remarkField']);
        return JsonResponseHandler::setResult($verify)->send();
    }

}