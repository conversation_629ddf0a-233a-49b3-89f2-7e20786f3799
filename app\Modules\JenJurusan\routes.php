<?php
namespace App\Modules\<PERSON>Jurusan;

use App\Modules\<PERSON>J<PERSON>an\Controllers\JenJurusanController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenjurusan')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenJurusanController::class, 'index']);
    Route::get('/datatable', [JenJurusanController::class, 'datatable']);
    Route::get('/create', [JenJurusanController::class, 'create'])->middleware('authorize:create-jenjurusan');
    Route::post('/', [JenJurusanController::class, 'store'])->middleware('authorize:create-jenjurusan');
    Route::get('/{jenjurusan_id}', [JenJurusanController::class, 'show'])->middleware('authorize:read-jenjurusan');
    Route::get('/{jenjurusan_id}/detail', [Jen<PERSON>urusanController::class, 'detail'])->middleware('authorize:read-jenjurusan');
    Route::get('/{jenjurusan_id}/jenjang', [JenJurusanController::class, 'filterJenjang']);
    Route::get('/{jenjurusan_id}/edit', [JenJurusanController::class, 'edit'])->middleware('authorize:update-jenjurusan');
    Route::put('/{jenjurusan_id}', [JenJurusanController::class, 'update'])->middleware('authorize:update-jenjurusan');
    Route::patch('/{jenjurusan_id}', [JenJurusanController::class, 'update'])->middleware('authorize:update-jenjurusan');
    Route::delete('/{jenjurusan_id}', [JenJurusanController::class, 'destroy'])->middleware('authorize:delete-jenjurusan');
});