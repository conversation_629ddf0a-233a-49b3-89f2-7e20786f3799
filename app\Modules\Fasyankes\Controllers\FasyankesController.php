<?php

namespace App\Modules\Fasyankes\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Fasyankes\Repositories\FasyankesRepository;
use App\Modules\Fasyankes\Models\Fasyankes;
use App\Modules\Fasyankes\Requests\FasyankesCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class FasyankesController extends Controller
{
    public function index(Request $request)
    {
        $data = Fasyankes::get();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = FasyankesRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('Fasyankes::create');
    }

    public function store(FasyankesCreateRequest $request)
    {
        $payload = $request->all();
        $fasyankes = FasyankesRepository::create($payload);
        return JsonResponseHandler::setResult($fasyankes)->send();
    }

    public function show(Request $request, $id)
    {
        $fasyankes = FasyankesRepository::get($id);
        return JsonResponseHandler::setResult($fasyankes)->send();
    }

    public function edit($id)
    {
        return view('Fasyankes::edit', ['fasyankes_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $fasyankes = FasyankesRepository::update($id, $payload);
        return JsonResponseHandler::setResult($fasyankes)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = FasyankesRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
