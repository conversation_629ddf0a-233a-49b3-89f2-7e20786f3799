@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="golongan-ruang">
    <default-datatable title="GolonganRuang" url="{!! url('golongan-ruang') !!}" :headers="headers" :can-add="{{ $permissions['create-golongan_ruang'] }}" :can-edit="{{ $permissions['update-golongan_ruang'] }}" :can-delete="{{ $permissions['delete-golongan_ruang'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'name',
        						text: 'name'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#golongan-ruang');
</script>
@endsection