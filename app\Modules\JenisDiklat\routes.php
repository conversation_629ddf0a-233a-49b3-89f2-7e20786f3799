<?php
namespace App\Modules\JenisDiklat;

use App\Modules\JenisDiklat\Controllers\JenisDiklatController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jenis-diklat')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JenisDiklatController::class, 'index'])->middleware('authorize:read-jenis_diklat');
    Route::get('/datatable', [JenisDiklatController::class, 'datatable'])->middleware('authorize:read-jenis_diklat');
    Route::get('/create', [JenisDiklatController::class, 'create'])->middleware('authorize:create-jenis_diklat');
    Route::post('/', [JenisDiklatController::class, 'store'])->middleware('authorize:create-jenis_diklat');
    Route::get('/{jenis_diklat_id}/detail', [JenisDiklatController::class, 'show'])->middleware('authorize:read-jenis_diklat');
    Route::get('/{jenis_diklat_id}/bymetodediklat', [JenisDiklatController::class, 'byMetodeDiklat']);
    Route::get('/{jenis_diklat_id}/edit', [JenisDiklatController::class, 'edit'])->middleware('authorize:update-jenis_diklat');
    Route::patch('/{jenis_diklat_id}', [JenisDiklatController::class, 'update'])->middleware('authorize:update-jenis_diklat');
    Route::delete('/{jenis_diklat_id}', [JenisDiklatController::class, 'destroy'])->middleware('authorize:delete-jenis_diklat');
});