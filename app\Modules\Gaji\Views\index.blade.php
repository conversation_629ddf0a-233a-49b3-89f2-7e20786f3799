@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="gaji">
    <default-datatable title="Gaji" url="{!! url('gaji') !!}" :headers="headers" :can-add="{{ $permissions['create-gaji'] }}" :can-edit="{{ $permissions['update-gaji'] }}" :can-delete="{{ $permissions['delete-gaji'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#gaji');
</script>
@endsection