<?php

namespace App\Modules\Gaji\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Gaji\Repositories\GajiRepository;
use App\Modules\Gaji\Requests\GajiCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class GajiController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('Gaji::index', ['permissions' => $permissions]);
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = GajiRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function create()
    {
        return view('Gaji::create');
    }

    // public function store(GajiCreateRequest $request)
    // {
    //     $payload = $request->all();
    //     $gaji = GajiRepository::create($payload);
    //     return JsonResponseHandler::setResult($gaji)->send();
    // }

    // public function show(Request $request, $id)
    // {
    //     $gaji = GajiRepository::get($id);
    //     return JsonResponseHandler::setResult($gaji)->send();
    // }

    public function edit($id)
    {
        return view('Gaji::edit', ['gaji_id' => $id]);
    }

    // public function update(Request $request, $id)
    // {
    //     $payload = $request->all();
    //     unset($payload['created_at']);
    //     unset($payload['updated_at']);
    //     $gaji = GajiRepository::update($id, $payload);
    //     return JsonResponseHandler::setResult($gaji)->send();
    // }

    // public function destroy(Request $request, $id)
    // {
    //     $delete = GajiRepository::delete($id);
    //     return JsonResponseHandler::setResult($delete)->send();
    // }
}
