<?php

namespace App\Modules\DiklatStruktural\Models;
    
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\Eselon\Models\Eselon;

class DiklatStruktural extends Model
{
    use SoftDeletes;
    protected $table = 'a_dikstru';
    protected $guarded = [];
    public $timestamps = false;

    public function eselon()
    {
        return $this->belongsTo(Eselon::class, 'id_eselon', 'id');
    }
}