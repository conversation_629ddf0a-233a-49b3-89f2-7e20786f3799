<?php

namespace App\Modules\ExecutiveSummary\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\ExecutiveSummary\Repositories\ExecutiveSummaryRepository;
use App\Modules\ExecutiveSummary\Repositories\ValiditasRepository;
use App\Modules\Permission\Repositories\PermissionRepository;
use App\Modules\UnitKerja\Models\UnitKerjaModel;
use Illuminate\Http\Request;

class ValiditasDataASNController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('ExecutiveSummary::validitas-data-asn.index', ['permissions' => $permissions]);
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = ExecutiveSummaryRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function skpdCompletionAndValidity(Request $request)
    {
        $data = UnitKerjaModel::whereRaw("
                    flag = 1 AND
                    id_induk_upt = '00' AND
                    id_sub_unit  = '00' AND
                    id_sub_sub_unit  = '00' AND
                    id_sub_sub_sub_unit = '00'
                ")
            ->search($request->input('keyword'))
            ->withAvg('employees', 'completion_percentage')
            ->withAvg('employees', 'validity_percentage')
            ->whereUserHaveAccess();

        $sortBy = $request->input('sortBy');
        if (!empty($sortBy) && !empty($sortBy['column'])) {
            if ($sortBy['column'] == 'skpd') {
                $data = $data->orderBy('skpd', $sortBy['type']);
            }
            if ($sortBy['column'] == 'employees_avg_completion_percentage') {
                $data = $data->orderBy('employees_avg_completion_percentage', $sortBy['type']);
            }
            if ($sortBy['column'] == 'employees_avg_validity_percentage') {
                $data = $data->orderBy('employees_avg_validity_percentage', $sortBy['type']);
            }
        }
        return JsonResponseHandler::setResult($data->paginate($request->input('per_page')))->send();
    }

    public function employeeCompletionAndValidity(Request $request, $employeeId)
    {
        $validityAndCompletionData = ValiditasRepository::getEmployeeValidityAndCompletion($employeeId);
        return JsonResponseHandler::setResult($validityAndCompletionData)->send();
    }
}
