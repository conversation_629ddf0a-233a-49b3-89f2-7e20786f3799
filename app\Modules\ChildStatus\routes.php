<?php
namespace App\Modules\ChildStatus;

use App\Modules\ChildStatus\Controllers\ChildStatusController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/child-status')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [ChildStatusController::class, 'index'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/datatable', [ChildStatusController::class, 'datatable']);
    Route::get('/create', [ChildStatusController::class, 'create'])->middleware('authorize:create-child_status');
    Route::post('/', [ChildStatusController::class, 'store'])->middleware('authorize:create-child_status');
    Route::get('/{child_status_id}', [ChildStatusController::class, 'show'])->middleware('authorize:read-child_status');
    Route::get('/{child_status_id}/detail', [ChildStatusController::class, 'detail'])->middleware('authorize:read-child_status');
    Route::get('/{child_status_id}/edit', [ChildStatusController::class, 'edit'])->middleware('authorize:update-child_status');
    Route::put('/{child_status_id}', [ChildStatusController::class, 'update'])->middleware('authorize:update-child_status');
    Route::patch('/{child_status_id}', [ChildStatusController::class, 'update'])->middleware('authorize:update-child_status');
    Route::delete('/{child_status_id}', [ChildStatusController::class, 'destroy'])->middleware('authorize:delete-child_status');
});