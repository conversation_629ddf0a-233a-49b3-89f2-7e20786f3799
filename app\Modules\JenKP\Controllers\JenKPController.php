<?php

namespace App\Modules\JenKP\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenKP\Models\JenKPModel;
use App\Modules\JenKP\Repositories\JenKPRepository;
// use App\Modules\JenKP\Requests\JenKPCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenKPController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenKPRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function index(Request $request)
    {
        $permissions = JenKPModel::get();
        return JsonResponseHandler::setResult($permissions)->send();

    }
}
