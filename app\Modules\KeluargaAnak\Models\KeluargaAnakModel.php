<?php

namespace App\Modules\KeluargaAnak\Models;

use App\Exceptions\AppException;
use Illuminate\Database\Eloquent\Model;
use App\Modules\Gender\Models\Gender;
use App\Modules\ChildStatus\Models\ChildStatus;
use App\Modules\TkPendid\Models\TkPendid;
use App\Modules\pekerjaan\Models\pekerjaan;
use App\Modules\KeluargaAnak\Models\KeluargaAnakTempFieldModel;
use App\Modules\KeluargaAnak\Models\KeluargaAnakTempVerificatorModel;
use App\Traits\PdmTrait;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

use App\Modules\Efile\Repositories\EfileRepository;
use App\Modules\Employee\Model\EmployeeModel;
use Exception;
use App\Handler\FileHandler;

use function Psy\debug;

class KeluargaAnakModel extends Model
{
    use SoftDeletes;

    public $table = 'r_anak';
    protected $guarded = [];
    protected $appends = [
        'umur_anak', 
        'status_anak'
    ];
    protected $casts = [
        'status_tunjangan' => 'integer'
    ];

    const ANAK_EFILE_DIR = 'submission/anak';

    const EFILE_CODE_MAPPING = [
        "52"    => "status_tunjangan_efile",
        "35_3"  => "bpjs_anak_efile",
        "35_1"  => "bpjs_anak_efile", // punya pegawai
        "23_3"  => "nomor_akta_lahir_efile",
        "23_1"  => "nomor_akta_lahir_efile", // punya pegawai
        "19_3"  => "nomor_akta_mati_efile",
        "07_71" => "status_pegawai_efile", // punya pegawai
        "58"    => "surat_tidak_punya_penghasilan_anak_efile"
    ];

    // PDM PROPERTY
    use PdmTrait;
    protected $pdmClass = KeluargaAnakTempModel::class;
    protected $pdmVerificatorClass = KeluargaAnakTempVerificatorModel::class;
    protected $pdmFieldClass = KeluargaAnakTempFieldModel::class;
    public $allowedPdmFields = [
        'id_pegawai',
        'id_pasangan',
        'id_pasangan_usul',
        'nip',
        'status_asn',
        'anak_ke',
        'nama_anak',
        'tempat_lahir',
        'tanggal_lahir',
        'status_anak',
        'jenis_kelamin',
        'umur',
        'alamat',
        'alamat_rt',
        'alamat_rw',
        'alamat_kode_pos',
        'alamat_provinsi',
        'alamat_kabupaten',
        'alamat_kecamatan',
        'alamat_kelurahan',
        'alamat_long',
        'alamat_lat',
        'ktp_alamat',
        'ktp_alamat_rt',
        'ktp_alamat_rw',
        'ktp_alamat_kode_pos',
        'ktp_alamat_provinsi',
        'ktp_alamat_kabupaten',
        'ktp_alamat_kecamatan',
        'ktp_alamat_kelurahan',
        'ktp_alamat_long',
        'ktp_alamat_lat',
        'status_tunjangan',
        'status_perkawinan',
        'nik',
        'nomor_hp',
        'nomor_bpjs',
        'nomor_akta_lahir',
        'tanggal_akta_lahir',
        'status_hidup',
        'kematian_tanggal',
        'kematian_nomor',
        'id_pendidikan',
        'pekerjaan',
        'pekerjaan_status',
        'pekerjaan_nip',
        'pekerjaan_kantor',
        'pekerjaan_long',
        'pekerjaan_lat',
        'id_pasangan',
        'status_kedudukan',
        'status_tunjangan_efile',
        'bpjs_anak_efile',
        'nomor_akta_lahir_efile',
        'nomor_akta_mati_efile',
        'status_pegawai_efile',
        'surat_tidak_punya_penghasilan_anak_efile'
    ];

    public $hidePppkParuhWaktuPdmFields = [
        'status_tunjangan',
        'status_tunjangan_efile',
        'surat_tidak_punya_penghasilan_anak_efile'
    ];

    public function statusAnak(): BelongsTo
    {
        return $this->belongsTo(ChildStatus::class, 'status_anak', 'id');
    }

    public function jenisKelamin(): BelongsTo
    {
        return $this->belongsTo(Gender::class, 'jenis_kelamin', 'id');
    }

    public function tkpendid(): BelongsTo
    {
        return $this->belongsTo(TkPendid::class, 'id_pendidikan', 'idtkpendid');
    }

    public function pekerjaan(): BelongsTo
    {
        return $this->belongsTo(pekerjaan::class, 'pekerjaan', 'id');
    }

    // public function statusTunjangan(): BelongsTo
    // {
    //     return $this->belongsTo(StsTunjanganModel::class, 'status_tunjangan', 'idststunj');
    // }

    // public function statusTunjanganFinal(): BelongsTo
    // {
    //     return $this->belongsTo(StsTunjanganModel::class, 'status_tunjangan_final', 'idststunj');
    // }

    protected function umurAnak(): Attribute
    {
        return Attribute::make(
            get: function () {
                $today = date_create(date('Y-m-d'));
                $birthday = date_create($this->tanggal_lahir);
                $diff = date_diff($today, $birthday);
                $umur = date_interval_format($diff, "%y"); //"%y tahun, %m bulan, %d hari"

                return (int) $umur;
            }
        );
    }

    public function submissions()
    {
        return $this->hasMany(KeluargaAnakTempModel::class, 'id_pegawai', 'id_pegawai');
    }

    public function getLastSubmissionAttribute()
    {
        return $this->submissions()->where('status', 1)->orderBy('id', 'DESC')->first();
    }

    public function getLastSubmissionIndexedField()
    {
        if (!empty($this->last_submission)) {
            return $this->last_submission->fields->mapWithKeys(function ($item) {
                return [$item->field => $item->toArray()];
            })->toArray();
        } else {
            return [];
        }
    }

    public function employee()
    {
        return $this->belongsTo(EmployeeModel::class, 'id_pegawai', 'id');
    }

    public function handleEfileUpdate($paylod, $idJenisAksi)
    {
        try {
            $nip = $this->employee->nip;
            // $efileReferenceId = $idJenisAksi == 2 && !empty($this->old_id) ? $this->old_id : $this->id;
            $efileReferenceId = $this->id;
            $fileMappings = array_flip(self::EFILE_CODE_MAPPING);
            foreach ($paylod as $key => $value) {
                if (!array_key_exists($key, $fileMappings)) {
                    throw new AppException("EFILE ERROR: Undefined efile key $key");  
                }
            }
            foreach ($paylod as $key => $value) {
                if ($key == 'status_tunjangan_efile') {
                    $fileCode = "52";
                    (new EfileRepository())->storeFile(nip: $nip, code: $fileCode, filepath: $value, oldRiwayatId: $efileReferenceId, old_id: @$this->old_id);
                }
                if ($key == 'bpjs_anak_efile') {
                    $fileCode = '35_3';
                    (new EfileRepository())->storeFile(nip: $nip, code: $fileCode, filepath: $value, oldRiwayatId: $efileReferenceId, old_id: @$this->old_id);
                }
                if ($key == 'nomor_akta_lahir_efile') {
                    $fileCode = "23_3";
                    (new EfileRepository())->storeFile(nip: $nip, code: $fileCode, filepath: $value, oldRiwayatId: $efileReferenceId, old_id: @$this->old_id);
                }
                if ($key == 'nomor_akta_mati_efile') {
                    $fileCode = "19_3";
                    (new EfileRepository())->storeFile(nip: $nip, code: $fileCode, filepath: $value, oldRiwayatId: $efileReferenceId, old_id: @$this->old_id);
                }
                if ($key == 'status_pegawai_efile') {
                    $fileCode = "07_71";
                    (new EfileRepository())->storeFile(nip: $nip, code: $fileCode, filepath: $value, oldRiwayatId: $efileReferenceId, old_id: @$this->old_id);
                }
                if ($key == 'surat_tidak_punya_penghasilan_anak_efile') {
                    $fileCode = "58";
                    (new EfileRepository())->storeFile(nip: $nip, code: $fileCode, filepath: $value, oldRiwayatId: $efileReferenceId, old_id: @$this->old_id);
                }
            }
            foreach ($paylod as $key => $value) {
                FileHandler::delete($value);
            }
        }
        catch (AppException $e) {
            throw $e;
        }
    }

    public function handleEfileDelete($id_riwayat, $old_id = null)
    {
        $nip = $this->employee->nip;
        $fileCodes = ['52', '35_3', '23_3', '19_3', '07_71', '58'];

        foreach ($fileCodes as $code) {
            (new EfileRepository())->deleteFile($nip, $code, $id_riwayat);

            if (!empty($old_id)) {
                (new EfileRepository())->deleteFile($nip, $code, $old_id);
            }
        }
    }

    // Di DB production ada field status anak yang pake format huruf sama angka, 
    // kemungkinan kesalahan saat bikin status anak select di frontend
    // diseragamkan jadi format angka semua
    public function getStatusAnakAttribute()
    {
        if (!empty($this->attributes['status_anak'])) {
            switch ($this->attributes['status_anak']) {
                case 'A':
                    return 1;
                case 'K':
                    return 2;
                case 'T':
                    return 3;
                default:
                    return $this->attributes['status_anak'];
            }
        }
        // $status = ChildStatus::where('code', $this->status_anak)->first()->id;
    }

    public function getStatusAnakNameAttribute()
    {
        if (!empty($this->status_anak)) {
            return ChildStatus::where('id', $this->status_anak)
                ->orWhere('code', $this->status_anak)
                ->first()
                ?->name;
        }
    }

    // public function getStatusTunjanganAttribute()
    // {
    //     // dd($this->attributes['status_tunjangan'], $this->attributes['status_tunjangan_final']);
    //     $data = $this->attributes['status_tunjangan'] == 2
    //             ? 0 
    //             : $this->attributes['status_tunjangan'];
    //     return (int) ($data ?? 0);
    // }

    // public function getStatusTunjanganFinalAttribute()
    // {
    //     $data = $this->attributes['status_tunjangan_final'] == 2
    //             ? 0 
    //             : $this->attributes['status_tunjangan_final'];
    //     return (int) ($data ?? 0);          
    // }

    public function getStatusTunjanganNameAttribute()
    {
        return $this->status_tunjangan == 1
            ? 'Dapat' 
            : 'Tidak Dapat';
    }

    public function getStatusTunjanganFinalNameAttribute()
    {
        return $this->status_tunjangan_final == 1
            ? 'Dapat' 
            : 'Tidak Dapat';
    }

    public function getDokumenEfileAttribute()
    {
        try {
            $nip = $this->employee->nip;
            $fileCode = ["52", "35_3", "23_3", "19_3", "07_71"];
            $efiles = (new EfileRepository())
                ->getFileByNipAndCodes($nip, implode(",", [$fileCode]));
            if ($efiles != null && !empty($efiles) && isset($efiles[0]['efile']) && !empty($efiles[0]['efile'])) {
                $efile = $efiles[0]['efile'][0];
                $efile['name'] = $efiles[0]['nama_jenis'];
                $efile['code'] = $efiles[0]['id_jenis'];
                return $efile;
            } else {
                return null;
            }
        } catch (Exception $err) {
            return null;
        }
    }

    public function withEfile()
    {
        $item = $this;

        $employee = $this->employee()->first();
        $nip = $employee->nip;

        $requiredEfileCodes = ["52", "35_1", "35_3", "23_1", "23_3", "19_3", "07_71", "58"];
        $asnFileCodes = ["35_1", "23_1"];

        $pekerjaanNip = $this->pekerjaan_nip;
        // dd($nip);
        $efiles = (new EfileRepository())->getFileByNipAndCodes($nip, implode(",", $requiredEfileCodes));
        // dd($efiles);
        $efileAsn = !empty($pekerjaanNip) ? (new EfileRepository())->getFileByNipAndCodes($pekerjaanNip, implode(",", $asnFileCodes)) : [];
        $efileAsn = collect($efileAsn)->keyBy('id_jenis')->all();
        
        if (!empty($efiles)) {
            foreach ($efiles as $efile) {
                $idJenis = $efile['id_jenis'];
                // dd($idJenis);
                if (in_array($idJenis, $asnFileCodes) && $item->status_asn !== 1) {
                    continue;
                }
                if (in_array($idJenis, ["35_3", "23_3"]) && $item->status_asn === 1) {
                    continue;
                }
                // $usedIdRiwayat = $this->old_id ?? $this->id;
                $filesIndexedByIdRiwayat = collect($efile['efile'])
                    ->filter(function ($file) {
                        return ($file['id_riwayat'] == $this->old_id) || ($file['id_riwayat'] == $this->id);
                    })
                    // ->where('id_riwayat', $usedIdRiwayat)
                    ->keyBy('id_riwayat')
                    ->all();
    
                $key = KeluargaAnakModel::EFILE_CODE_MAPPING[$efile['id_jenis']];
    
                if (!empty($filesIndexedByIdRiwayat) && $efile['response'] == 'success' && !empty($efile['efile'])) {
                    $fileData = $filesIndexedByIdRiwayat[$this->id] ?? $filesIndexedByIdRiwayat[$this->old_id] ?? null;
                    // dd($fileData);
                    // Handle if current object is ASN
                    if ($item->status_asn === 1 && in_array($idJenis, $asnFileCodes)) {
                        if (empty($item->pekerjaan_nip)) {
                            $item->{$key} = null;
                            continue;
                        }
                        if (empty($efileAsn)) {
                            $item->{$key} = null;
                            continue;
                        }
                        if (empty($efileAsn[$idJenis])) {
                            $item->{$key} = null;
                            continue;
                        }
                        if (empty($efileAsn[$idJenis]['efile'])) {
                            $item->{$key} = null;
                            continue;
                        }
                        $fileData = $efileAsn[$idJenis]['efile'][0];
                    }
    
                    $fileData['name'] = $efile['nama_jenis'];
                    $fileData['id_jenis'] = $efile['id_jenis'];
                    $item->{$key} = $fileData;
                } else {
                    $item->{$key} = null;
                }
            }
        }
        else {
            foreach ($requiredEfileCodes as $code) {
                $key = KeluargaAnakModel::EFILE_CODE_MAPPING[$code];
                $item->{$key} = null;
            }
        }
        // dd($item);

        // } catch (Exception $err) {
        //     foreach ($requiredEfileCodes as $code) {
        //         $key = KeluargaAnakModel::EFILE_CODE_MAPPING[$code];
        //         $item->{$key} = null;
        //     }
        // }

        return $item;
    }
}
