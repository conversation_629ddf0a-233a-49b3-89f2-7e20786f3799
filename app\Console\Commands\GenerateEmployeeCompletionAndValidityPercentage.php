<?php

namespace App\Console\Commands;

use App\Jobs\EmployeeCompletionJob;
use App\Modules\Employee\Model\EmployeeModel;
use App\Modules\ExecutiveSummary\Repositories\ValiditasRepository;
use Illuminate\Console\Command;

class GenerateEmployeeCompletionAndValidityPercentage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:employeeCompletionAndValidityPercentage';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        EmployeeModel::active()->chunk(1000, function ($employees) {
            foreach ($employees as $employee) {
                EmployeeCompletionJob::dispatch($employee->id);
            }
        });

        // ValiditasRepository::getEmployeeValidityAndCompletion("590d1fbf-2821-11ef-8e1c-0242ac130006");
    }
}
