<?php

namespace App\Modules\JenisPensiun\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JenisPensiun\Models\JenisPensiun;
use App\Modules\JenisPensiun\Repositories\JenisPensiunRepository;
use App\Modules\JenisPensiun\Requests\JenisPensiunCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class JenisPensiunController extends Controller
{
    public function index(Request $request)
    {
        $permissions = PermissionRepository::getPermissionStatusOnMenuPath($request->path());
        return view('JenisPensiun::index', ['permissions' => $permissions]);
    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenisPensiunRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $jenis_pensiun_id)
    {
        $jenis_pensiun = JenisPensiun::where('id', $jenis_pensiun_id)->first();
        return JsonResponseHandler::setResult($jenis_pensiun)->send();
    }

    public function create()
    {
        return view('JenisPensiun::create');
    }

    public function store(JenisPensiunCreateRequest $request)
    {
        $payload = $request->all();
        $jenis_pensiun = JenisPensiunRepository::create($payload);
        return JsonResponseHandler::setResult($jenis_pensiun)->send();
    }

    public function show(Request $request, $id)
    {
        $jenis_pensiun = JenisPensiunRepository::get($id);
        return JsonResponseHandler::setResult($jenis_pensiun)->send();
    }

    public function edit($id)
    {
        return view('JenisPensiun::edit', ['jenis_pensiun_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $jenis_pensiun = JenisPensiunRepository::update($id, $payload);
        return JsonResponseHandler::setResult($jenis_pensiun)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = JenisPensiunRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
