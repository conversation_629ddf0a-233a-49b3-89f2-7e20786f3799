<?php
namespace App\Modules\GolDarah;

use App\Modules\GolDarah\Controllers\GolDarahController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/goldarah')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [GolDarahController::class, 'get'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/datatable', [GolDarahController::class, 'datatable']);
    Route::get('/create', [GolDarahController::class, 'create'])->middleware('authorize:create-goldarah');
    Route::post('/', [GolDarahController::class, 'store'])->middleware('authorize:create-goldarah');
    Route::get('/{goldarah_id}', [GolDarahController::class, 'show'])->middleware('authorize:read-goldarah');
    Route::get('/{goldarah_id}/detail', [GolDarahController::class, 'detail'])->middleware('authorize:read-goldarah');
    Route::get('/{goldarah_id}/edit', [GolDarahController::class, 'edit'])->middleware('authorize:update-goldarah');
    Route::put('/{goldarah_id}', [GolDarahController::class, 'update'])->middleware('authorize:update-goldarah');
    Route::patch('/{goldarah_id}', [GolDarahController::class, 'update'])->middleware('authorize:update-goldarah');
    Route::delete('/{goldarah_id}', [GolDarahController::class, 'destroy'])->middleware('authorize:delete-goldarah');
});