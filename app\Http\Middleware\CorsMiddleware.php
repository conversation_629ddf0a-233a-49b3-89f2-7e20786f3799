<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CorsMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */

    protected $allowedOrigins = [
        'http://localhost:5173',
        'https://bkd-jateng-frontend.vercel.app',
        'http://localhost:4010',
        'http://localhost:4009',
        'http://localhost:5174',
        'http://***************',
        'https://siasn.jatengprov.go.id',
        'http://siasn.jatengprov.go.id',
        'http://**********:1080',
        null,
    ];

    public function handle(Request $request, Closure $next): Response
    {
        $origin = $request->header('Origin');
        if (in_array($origin, $this->allowedOrigins)) {
            $headers = [
                'Access-Control-Allow-Origin' => $origin,
                'Access-Control-Allow-Methods' => 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
                'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With, Cache-Control, X-Role-Id, X-User-Role-Id',
                'Access-Control-Allow-Credentials' => 'true',
            ];
        }


        if ($request->getMethod() === "OPTIONS") {
            return response()->json('OK', 200, $headers);
        }

        $response = $next($request);
        if (isset($headers)) {
            foreach ($headers as $key => $value) {
                $response->headers->set($key, $value);
            }
        }

        return $response;
    }
}
