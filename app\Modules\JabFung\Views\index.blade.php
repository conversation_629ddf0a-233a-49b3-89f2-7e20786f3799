@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="jabfung">
    <default-datatable title="JabFung" url="{!! url('jabfung') !!}" :headers="headers" :can-add="{{ $permissions['create-jabfung'] }}" :can-edit="{{ $permissions['update-jabfung'] }}" :can-delete="{{ $permissions['delete-jabfung'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#jabfung');
</script>
@endsection