<?php
namespace App\Modules\JabFungUm;

use App\Modules\JabFungUm\Controllers\JabFungUmController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jabfungum')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JabFungUmController::class, 'index']);
    Route::get('/datatable', [JabFungUmController::class, 'datatable'])->middleware('authorize:read-jabfungum');
    Route::get('/create', [JabFungUmController::class, 'create'])->middleware('authorize:create-jabfungum');
    Route::post('/', [JabFungUmController::class, 'store'])->middleware('authorize:create-jabfungum');
    Route::get('/{jabfungum_id}', [JabFungUmController::class, 'show'])->middleware('authorize:read-jabfungum');
    Route::get('/{jabfungum_id}/detail', [JabFungUmController::class, 'detail'])->middleware('authorize:read-jabfungum');
    Route::get('/{jabfungum_id}/edit', [JabFungUmController::class, 'edit'])->middleware('authorize:update-jabfungum');
    Route::put('/{jabfungum_id}', [JabFungUmController::class, 'update'])->middleware('authorize:update-jabfungum');
    Route::patch('/{jabfungum_id}', [JabFungUmController::class, 'update'])->middleware('authorize:update-jabfungum');
    Route::delete('/{jabfungum_id}', [JabFungUmController::class, 'destroy'])->middleware('authorize:delete-jabfungum');
});