@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="fasyankes">
    <default-datatable title="Fasyankes" url="{!! url('fasyankes') !!}" :headers="headers" :can-add="{{ $permissions['create-fasyankes'] }}" :can-edit="{{ $permissions['update-fasyankes'] }}" :can-delete="{{ $permissions['delete-fasyankes'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#fasyankes');
</script>
@endsection