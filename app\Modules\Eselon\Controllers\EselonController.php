<?php

namespace App\Modules\Eselon\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\Eselon\Models\Eselon;
use App\Modules\Eselon\Repositories\EselonRepository;
use App\Modules\Eselon\Requests\EselonCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;

class EselonController extends Controller
{
    public function index(Request $request)
    {
        $permissions = Eselon::get();
        return JsonResponseHandler::setResult($permissions)->send();

    }

    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = EselonRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function detail(Request $request, $eselon_id)
    {
        $eselon = Eselon::where('id', $eselon_id)->first();
        return JsonResponseHandler::setResult($eselon)->send();
    }

    public function create()
    {
        return view('Eselon::create');
    }

    public function store(EselonCreateRequest $request)
    {
        $payload = $request->all();
        $eselon = EselonRepository::create($payload);
        return JsonResponseHandler::setResult($eselon)->send();
    }

    public function show(Request $request, $id)
    {
        $eselon = EselonRepository::get($id);
        return JsonResponseHandler::setResult($eselon)->send();
    }

    public function edit($id)
    {
        return view('Eselon::edit', ['eselon_id' => $id]);
    }

    public function update(Request $request, $id)
    {
        $payload = $request->all();
        unset($payload['created_at']);
        unset($payload['updated_at']);
        $eselon = EselonRepository::update($id, $payload);
        return JsonResponseHandler::setResult($eselon)->send();
    }

    public function destroy(Request $request, $id)
    {
        $delete = EselonRepository::delete($id);
        return JsonResponseHandler::setResult($delete)->send();
    }
}
