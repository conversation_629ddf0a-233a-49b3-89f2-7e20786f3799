<?php

namespace App\Modules\JenKeduduPeg\Repositories;

use App\Modules\JenKeduduPeg\Models\JenKeduduPeg;

class JenKeduduPegRepository
{
    public static function datatable($per_page = 15, $keyword)
    {
        $data = JenKeduduPeg::search($keyword)->paginate($per_page);
        return $data;
    }
    public static function get($jenkedudupeg_id)
    {
        $jenkedudupeg = JenKeduduPeg::where('id', $jenkedudupeg_id)->first();
        return $jenkedudupeg;
    }
    public static function create($jenkedudupeg)
    {
        $jenkedudupeg = JenKeduduPeg::create($jenkedudupeg);
        return $jenkedudupeg;
    }

    public static function update($jenkedudupeg_id, $jenkedudupeg)
    {
        JenKeduduPeg::where('id', $jenkedudupeg_id)->update($jenkedudupeg);
        $jenkedudupeg = JenKeduduPeg::where('id', $jenkedudupeg_id)->first();
        return $jenkedudupeg;
    }

    public static function delete($jenkedudupeg_id)
    {
        $delete = JenKeduduPeg::where('id', $jenkedudupeg_id)->delete();
        return $delete;
    }
}
