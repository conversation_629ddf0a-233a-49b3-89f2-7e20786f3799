<?php
namespace App\Modules\JabFung;

use App\Modules\JabFung\Controllers\JabFungController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/jabfung')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [JabFungController::class, 'index']);
    Route::get('/datatable', [JabFungController::class, 'datatable']);
    Route::get('/create', [JabFungController::class, 'create'])->middleware('authorize:create-jabfung');
    Route::post('/', [JabFungController::class, 'store'])->middleware('authorize:create-jabfung');
    Route::get('/{jabfung_id}', [JabFungController::class, 'show'])->middleware('authorize:read-jabfung');
    Route::get('/{jabfung_id}/detail', [JabFungController::class, 'detail'])->middleware('authorize:read-jabfung');
    Route::get('/{jabfung_id}/edit', [JabFungController::class, 'edit'])->middleware('authorize:update-jabfung');
    Route::put('/{jabfung_id}', [JabFungController::class, 'update'])->middleware('authorize:update-jabfung');
    Route::patch('/{jabfung_id}', [JabFungController::class, 'update'])->middleware('authorize:update-jabfung');
    Route::delete('/{jabfung_id}', [JabFungController::class, 'destroy'])->middleware('authorize:delete-jabfung');
});