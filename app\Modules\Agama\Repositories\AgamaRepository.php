<?php

namespace App\Modules\Agama\Repositories;

use App\Modules\Agama\Models\Agama;

class AgamaRepository
{
    public static function datatable($per_page = 15)
    {
        $data = Agama::paginate($per_page);
        return $data;
    }
    public static function get($agama_id)
    {
        $agama = Agama::where('id', $agama_id)->first();
        return $agama;
    }
    public static function create($agama)
    {
        $agama = Agama::create($agama);
        return $agama;
    }

    public static function update($agama_id, $agama)
    {
        Agama::where('id', $agama_id)->update($agama);
        $agama = Agama::where('id', $agama_id)->first();
        return $agama;
    }

    public static function delete($agama_id)
    {
        $delete = Agama::where('id', $agama_id)->delete();
        return $delete;
    }
}
