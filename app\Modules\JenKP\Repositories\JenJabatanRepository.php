<?php

namespace App\Modules\JenKP\Repositories;

use App\Modules\JenKP\Models\JenKPModel;

class JenKPRepository
{
    public static function datatable($per_page = 15)
    {
        $data =  JenKPModel::paginate($per_page);
        return $data;
    }
    public static function get($jeniskp_id)
    {
        $jeniskp = JenKPModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }
    public static function create($jeniskp)
    {
        $jeniskp = JenKPModel::create($jeniskp);
        return $jeniskp;
    }

    public static function update($jeniskp_id, $jeniskp)
    {
        JenKPModel::where('id', $jeniskp_id)->update($jeniskp);
        $jeniskp = JenKPModel::where('id', $jeniskp_id)->first();
        return $jeniskp;
    }

    public static function delete($jeniskp_id)
    {
        $delete = JenKPModel::where('id', $jeniskp_id)->delete();
        return $delete;
    }
}
