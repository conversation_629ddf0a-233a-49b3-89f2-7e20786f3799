@extends('dashboard_layout.index')
@section('content')
<div class="page-inner" id="bidang">
    <default-datatable title="Bidang" url="{!! url('bidang') !!}" :headers="headers" :can-add="{{ $permissions['create-bidang'] }}" :can-edit="{{ $permissions['update-bidang'] }}" :can-delete="{{ $permissions['delete-bidang'] }}" />
</div>

<script type="module">
    Vue.createApp({
        data() {
            return {
                headers: [
                    {
                        text: 'Id',
                        value: 'id',
                    },    
					{
        						value: 'code',
        						text: 'Kode Bidang'
    					},    
					{
        						value: 'name',
        						text: 'Nama Bidang'
    					},    
					],
            }
        },
        created() {},
        methods: {},
        components: {
            'default-datatable': DefaultDatatable
        },
    }).mount('#bidang');
</script>
@endsection