<?php
namespace App\Modules\KategoriPrioritasPPPK;

use App\Modules\KategoriPrioritasPPPK\Controllers\KategoriPrioritasPPPKController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/kategori-prioritas-pppk')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)

    Route::get('/', [KategoriPrioritasPPPKController::class, 'index']);
    Route::get('/datatable', [KategoriPrioritasPPPKController::class, 'datatable'])->middleware('authorize:read-kategori_prioritas_pppk');
    Route::get('/create', [KategoriPrioritasPPPKController::class, 'create'])->middleware('authorize:create-kategori_prioritas_pppk');
    Route::post('/', [KategoriPrioritasPPPKController::class, 'store'])->middleware('authorize:create-kategori_prioritas_pppk');
    Route::get('/{kategori_prioritas_pppk_id}', [KategoriPrioritasPPPKController::class, 'show'])->middleware('authorize:read-kategori_prioritas_pppk');
    Route::get('/{kategori_prioritas_pppk_id}/edit', [KategoriPrioritasPPPKController::class, 'edit'])->middleware('authorize:update-kategori_prioritas_pppk');
    Route::put('/{kategori_prioritas_pppk_id}', [KategoriPrioritasPPPKController::class, 'update'])->middleware('authorize:update-kategori_prioritas_pppk');
    Route::delete('/{kategori_prioritas_pppk_id}', [KategoriPrioritasPPPKController::class, 'destroy'])->middleware('authorize:delete-kategori_prioritas_pppk');
});