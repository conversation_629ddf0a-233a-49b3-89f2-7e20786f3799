<?php

namespace App\Modules\ExecutiveSummary\Models;
    
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Modules\Employee\Model\EmployeeModel;

class PrediksiPensiunModel extends Model
{
    public static function dataPensiun(Request $request, $paginate = true)
    {
        $id_unit_kerja = $request->input('id_unit_kerja'); //B2
        $id_sub_unit = $request->input('id_sub_unit'); //B2.00.50
        $id_sub_sub_unit = $request->input('id_sub_sub_unit'); //B2.***********

        $pecah_induk_upt = substr($id_sub_unit, 3, 2); //00
        $pecah_sub_unit = substr($id_sub_unit, 6, 2); //50
        $pecah_sub_sub_unit = substr($id_sub_sub_unit, 9, 2); //00
        $pecah_sub_sub_sub_unit = substr($id_sub_sub_unit, 12, 2); //00

        $kelamin = $request->input('kelamin');
        $id_pendidikan = $request->input('id_pendidikan');
        $id_jenis_jabatan = $request->input('id_jenis_jabatan');
        $month_end = $request->input('month_end');
        $month_start = $request->input('month_start'); 
        $year = $request->input('year');

        $keyword = $request->input('keyword') != null ? $request->input('keyword') : null;
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;

        $data = DB::table('tb_01')
            ->leftJoin('r_jabatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_jabatan.id_pegawai')
                    ->where('r_jabatan.isakhir', '=', 1);
            })
            ->leftJoin('r_kepangkatan', function ($join) {
                $join
                    ->on('tb_01.id', '=', 'r_kepangkatan.id_pegawai')
                    ->where('r_kepangkatan.isakhir', '=', 1);
            })
            ->leftjoin('a_skpd', function ($join) {
                $join
                    ->on('tb_01.id_unit_kerja', '=', 'a_skpd.id_unit_kerja')
                    ->on('tb_01.id_induk_upt', '=', 'a_skpd.id_induk_upt')
                    ->on('tb_01.id_sub_unit', '=', 'a_skpd.id_sub_unit')
                    ->on('tb_01.id_sub_sub_unit', '=', 'a_skpd.id_sub_sub_unit')
                    ->on('tb_01.id_sub_sub_sub_unit', '=', 'a_skpd.id_sub_sub_sub_unit')
                    ->where('a_skpd.flag', '=', 1);
            })
            ->leftJoin('a_jabfung', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfung.idjabfung')
                    ->where('a_jabfung.flag', '=', 1);
            })
            ->leftJoin('a_jabfungum', function ($join) {
                $join
                    ->on('r_jabatan.id_jabatan', '=', 'a_jabfungum.idjabfungum')
                    ->where('a_jabfungum.flag', '=', 1);
            })
            ->select(
                'tb_01.id',
                'tb_01.nip',
                'tb_01.nip_lama',
                'tb_01.nama',
                'tb_01.tempat_lahir',
                'tb_01.tanggal_lahir',
                'a_skpd.path',
                'r_jabatan.jabatan',
                'r_kepangkatan.masa_kerja_bulan',
                'r_kepangkatan.masa_kerja_tahun',
                DB::raw('CONCAT(tb_01.gelar_depan,IF(LENGTH(tb_01.gelar_depan)>0," ",""),tb_01.nama,IF(LENGTH(tb_01.gelar_belakang)>0,", ",""),tb_01.gelar_belakang) as namalengkap'),
                DB::raw("
                    CONCAT(
                        LEFT(
                            DATE_ADD(
                                DATE_ADD(
                                    tb_01.tanggal_lahir,
                                    INTERVAL IF(r_jabatan.id_jenis_jabatan=1,a_skpd.bup,IF(r_jabatan.id_jenis_jabatan=2,a_jabfung.bup,IF(r_jabatan.id_jenis_jabatan=3,a_jabfungum.bup,58))) YEAR
                                ),
                                INTERVAL 1 MONTH
                            ),
                            8
                        ),
                        '01'
                    ) AS tanggal_pensiun
                "),
                DB::raw("
                    DATE_FORMAT(
                        FROM_DAYS(
                            TO_DAYS(NOW()) - TO_DAYS(tb_01.tanggal_lahir)
                        ),
                        '%Y'
                    ) + 0 AS usia
                ")
            )
            //->whereNotNull('tb_01.tanggal_lahir')
            ->whereNotIn('tb_01.kedudukan_pegawai', [99, 21])
            ->where(function ($query) {
                return (new EmployeeModel())->scopeWhereUserHaveAccess($query, 'tb_01.id_unit_kerja');
            });

        if ($year != null && $year != "") {
            $data = $data->havingRaw('YEAR(tanggal_pensiun) = ?', [$year]);
        }else{
            $currentYear = date('Y');
            $data = $data->havingRaw('YEAR(tanggal_pensiun) = ?', [$currentYear]);
        }

        if ($month_start != null && $month_start != "") {
            $data = $data->havingRaw('MONTH(tanggal_pensiun) >= ?', [$month_start]);
        }
        if ($month_end != null && $month_end != "") {
            $data = $data->havingRaw('MONTH(tanggal_pensiun) <= ?', [$month_end]);
        }
        if (($month_end == null || $month_end == "") && ($month_start == null || $month_start == "")) {
            // $data = $data->having('tanggal_pensiun', '>=', date('Y-m-d'));
            $data = $data->havingRaw('MONTH(tanggal_pensiun) BETWEEN 01 AND 12');
        }
        if ($id_jenis_jabatan != null && $id_jenis_jabatan != "") {
            $data = $data->where('r_jabatan.id_jenis_jabatan', $id_jenis_jabatan);
        }

        if (!empty($id_unit_kerja)) {
            if (!empty($id_sub_unit) && !empty($id_sub_sub_unit)) {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                    ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                    ->where('tb_01.id_sub_unit', $pecah_sub_unit)
                    ->where('tb_01.id_sub_sub_unit', $pecah_sub_sub_unit)
                    ->where('tb_01.id_sub_sub_sub_unit', $pecah_sub_sub_sub_unit);
            } elseif (!empty($id_sub_unit)) {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja)
                    ->where('tb_01.id_induk_upt', $pecah_induk_upt)
                    ->where('tb_01.id_sub_unit', $pecah_sub_unit);
            } else {
                $data = $data->where('tb_01.id_unit_kerja', $id_unit_kerja);
            }
        }

        if ($kelamin != null && $kelamin != "") {
            $data = $data->where('tb_01.jenis_kelamin', [$kelamin]);
        }

        if ($id_pendidikan != null && $id_pendidikan != "") {
            $data = $data->where('r_pendidikan_formal.id_jenjang', [$id_pendidikan]);
        }

        if ($keyword != null) $data->whereRaw("tb_01.nama LIKE '%" . $keyword . "%' or tb_01.nip LIKE '%" . $keyword . "%'");

        if ($paginate) {
            $data = $data->orderBy(DB::raw('tanggal_pensiun, tb_01.tanggal_lahir'))
                            ->paginate($request->input('per_page', 15));
        } else {
            $data = $data->orderBy(DB::raw('tanggal_pensiun, tb_01.tanggal_lahir'))
                            ->get();
        }
       
        return $data;
    }
}