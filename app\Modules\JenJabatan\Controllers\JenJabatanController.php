<?php

namespace App\Modules\JenJabatan\Controllers;

use App\Handler\JsonResponseHandler;
use App\Http\Controllers\Controller;
use App\Modules\JabFung\Models\JabFung;
use App\Modules\JenJabatan\Models\JenJabatanModel;
use App\Modules\JenJabatan\Repositories\JenJabatanRepository;
// use App\Modules\JenJabatan\Requests\JenJabatanCreateRequest;
use App\Modules\Permission\Repositories\PermissionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class JenJabatanController extends Controller
{
    public function datatable(Request $request)
    {
        $per_page = $request->input('per_page') != null ? $request->input('per_page') : 15;
        $data = JenJabatanRepository::datatable($per_page);
        return JsonResponseHandler::setResult($data)->send();
    }

    public function index(Request $request)
    {
        $permissions = JenJabatanModel::get();
        return JsonResponseHandler::setResult($permissions)->send();
    }

    public function store(Request $request)
    {
        $data = $request->all();
        $create = JenJabatanRepository::create($data);
        return JsonResponseHandler::setResult($create)->send();
    }

    public function detail($id)
    {
        $data = JenJabatanModel::where('idjenjab', $id)->first();
        return JsonResponseHandler::setResult($data)->send();
    }

    public function update(Request $request, $id)
    {
        $data = $request->all();
        $update = JenJabatanRepository::update($id, $data);
        return JsonResponseHandler::setResult($update)->send();
    }

    public function delete($id)
    {
        $delete = JenJabatanModel::where('idjenjab', $id)->delete();
        return JsonResponseHandler::setResult($delete)->send();
    }

    public function subjabatan($jabatan_id)
    {
        $subJabatan = [];
        $jabfung = JabFung::where('idjabfung',$jabatan_id)->first();
    if (!empty($jabfung)) {
            $kelompok = $jabfung->idsapk_kel;
            $subJabatan = DB::table('a_sub_jabatan')
                            ->where('kel_jabatan_id',$kelompok)
                            ->get();
        }
        return JsonResponseHandler::setResult($subJabatan)->send();
    }

    public function subJabatanAll(Request $request)
    {
        $data = [];
        $keyword = $request->input('keyword') ?? null;
        $rumpunJabatanId = $request->input('rumpun_jabatan') ?? null;
        // reference id rumpun jabatan dari view a_sub_jabatan_group_by_kel_jabatan_id_view

        $data = DB::table('a_sub_jabatan')
                ->when(!empty($keyword), function ($q) use ($keyword) {
                    $q->where('nama', 'like', $keyword.'%');
                })
                ->when(!empty($rumpunJabatanId), function ($q) use ($rumpunJabatanId) {
                    $kelJab = DB::table('a_sub_jabatan_group_by_kel_jabatan_id_view')
                        ->where('id', $rumpunJabatanId)
                        ->first();

                    return $q->where('kel_jabatan_id', @$kelJab->kel_jabatan_id);
                })
                ->get();

        return JsonResponseHandler::setResult($data)->send();
    }
}
