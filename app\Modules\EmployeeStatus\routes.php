<?php
namespace App\Modules\EmployeeStatus;

use App\Modules\EmployeeStatus\Controllers\EmployeeStatusController;
use Illuminate\Support\Facades\Route;

// USE MARKER (DONT DELETE THIS LINE)

Route::prefix('/employee-status')->group(function() {

    // SUB MENU MARKER (DONT DELETE THIS LINE)


    Route::get('/', [EmployeeStatusController::class, 'index'])->middleware('authorize:read-employee_status');
    Route::get('/get', [EmployeeStatusController::class, 'get'])->withoutMiddleware(['deny.pegawai']);
    Route::get('/datatable', [EmployeeStatusController::class, 'datatable'])->middleware('authorize:read-employee_status');
    Route::get('/', [EmployeeStatusController::class, 'index']);
    Route::get('/datatable', [EmployeeStatusController::class, 'datatable']);
    Route::get('/create', [EmployeeStatusController::class, 'create'])->middleware('authorize:create-employee_status');
    Route::post('/', [EmployeeStatusController::class, 'store'])->middleware('authorize:create-employee_status');
    Route::get('/{employee_status_id}', [EmployeeStatusController::class, 'show'])->middleware('authorize:read-employee_status');
    Route::get('/{employee_status_id}/detail', [EmployeeStatusController::class, 'detail'])->middleware('authorize:read-employee_status');
    Route::get('/{employee_status_id}/edit', [EmployeeStatusController::class, 'edit'])->middleware('authorize:update-employee_status');
    Route::put('/{employee_status_id}', [EmployeeStatusController::class, 'update'])->middleware('authorize:update-employee_status');
    Route::patch('/{employee_status_id}', [EmployeeStatusController::class, 'update'])->middleware('authorize:update-employee_status');
    Route::delete('/{employee_status_id}', [EmployeeStatusController::class, 'destroy'])->middleware('authorize:delete-employee_status');
});

